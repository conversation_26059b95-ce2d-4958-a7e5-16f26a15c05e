{CMF标准化指标 - Cha<PERSON>n Money Flow 0-100标准化版本}
N:20;
M:5;
P:100;

{计算收盘位置值 (CLV)}
CLV:=(CLOSE-LOW-HIGH+CLOSE)/(HIGH-LOW);

{计算资金流量}
MF:=CLV*VOL;

{计算原始CMF}
CMF原始:=SUM(MF,N)/SUM(VOL,N);

{计算P周期内CMF的最高值和最低值}
MAXCMF:=HHV(CMF原始,P);
MINCMF:=LLV(CMF原始,P);

{0-100标准化CMF}
CMF:(CMF原始-MINCMF)/(MAXCMF-MINCMF)*100,COLORWHITE,LINETHICK2;

{价格高低点判断}
HH:=HIGH>=HHV(HIGH,M);
LL:=LOW<=LLV(LOW,M);

{标准化CMF高低点判断}
CMFHH:=CMF>=HHV(CMF,M);
CMFLL:=CMF<=LLV(CMF,M);

{背离信号计算 - 基于标准化后的CMF}
{顶背离：价格创新高但CMF未创新高，且CMF在50以上}
顶背离:=HH AND CMFHH=0 AND CMF>50;

{底背离：价格创新低但CMF未创新低，且CMF在50以下}
底背离:=LL AND CMFLL=0 AND CMF<50;

{标准化参考线}
超买线:80,COLORRED,LINETHICK1;
中线:50,COLORYELLOW,LINETHICK1;
超卖线:20,COLORGREEN,LINETHICK1;

{背离信号显示}
STICKLINE(顶背离,50,CMF,3,0),COLORRED;
STICKLINE(底背离,50,CMF,3,0),COLORGREEN;

{背离信号文字提示}
DRAWTEXT(顶背离,CMF+5,'顶背离'),COLORRED;
DRAWTEXT(底背离,CMF-5,'底背离'),COLORGREEN;

{使用说明}
{1. CMF值在0-100之间波动，50为中性线}
{2. CMF>80：资金流入强劲，超买区域}
{3. CMF<20：资金流出严重，超卖区域}
{4. 顶背离：价格新高但CMF未新高，看跌信号}
{5. 底背离：价格新低但CMF未新低，看涨信号}
{6. 标准化后更便于设置交易策略阈值}
