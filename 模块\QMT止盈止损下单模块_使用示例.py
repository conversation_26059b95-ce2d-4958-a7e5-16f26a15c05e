#coding:gbk
"""
QMT止盈止损下单模块 - 使用示例（方案三优化版本）
演示如何使用数据缓冲区功能进行实时交易

使用方法：
1. 在QMT中导入此策略文件
2. 策略会自动在init中下载历史数据
3. 在handlebar中初始化缓冲区并获取历史数据
4. 实时更新技术指标并执行交易逻辑
"""

# 导入必要的库
import datetime
import time
from collections import deque

# 配置参数
BUFFER_CONFIG = {
    'buffer_size': 150,           # 缓冲区大小
    'history_data_count': 100,    # 历史数据量
    'min_trading_periods': 25,    # 最少交易周期
    'debug_mode': True            # 调试模式
}

def init(ContextInfo):
    """
    策略初始化函数 - QMT标准模式
    注意：在init()中只能下载数据，不能获取数据
    """
    print("🚀 启动QMT止盈止损策略 - 数据缓冲区版本")
    print("="*60)

    # 获取股票代码
    stock_code = ContextInfo.stockcode + '.' + ContextInfo.market
    print(f"📊 目标股票: {stock_code}")
    print(f"🕐 初始化时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 配置参数调整（可选）
    STRATEGY_CONFIG['buffer_size'] = 150           # 缓冲区大小
    STRATEGY_CONFIG['history_data_count'] = 100    # 历史数据量
    STRATEGY_CONFIG['min_trading_periods'] = 25    # 最少交易周期

    # 启用调试模式（可选）
    ContextInfo.debug_mode = True

    # 下载历史数据（QMT要求在init中完成）
    print(f"\n📥 开始下载历史数据...")

    try:
        # 计算下载日期范围
        import datetime
        end_date = datetime.datetime.now().strftime('%Y%m%d')
        start_date = (datetime.datetime.now() - datetime.timedelta(days=10)).strftime('%Y%m%d')

        print(f"📅 下载范围: {start_date} ~ {end_date}")

        # 下载分钟级历史数据
        download_history_data(stock_code, "1m", start_date, end_date)
        print(f"✅ 分钟数据下载完成")

        # 下载日线数据用于长期指标
        download_history_data(stock_code, "1d", start_date, end_date)
        print(f"✅ 日线数据下载完成")

    except Exception as e:
        print(f"⚠️ 历史数据下载异常: {e}")
        print(f"💡 提示: 将在handlebar中逐步填充数据缓冲区")

    # 等待数据写入完成
    print(f"\n⏳ 等待数据写入完成...")
    import time
    time.sleep(5)

    # 标记初始化完成，但数据缓冲区将在第一次handlebar中创建
    ContextInfo.init_completed = True
    ContextInfo.buffer_initialized = False

    print(f"\n💡 策略初始化完成，等待handlebar进行数据获取...")
    print("="*60)

def handlebar(ContextInfo):
    """
    K线数据处理函数 - QMT标准模式
    在这里进行数据获取和缓冲区管理
    """
    try:
        # 第一次运行时初始化数据缓冲区
        if not getattr(ContextInfo, 'buffer_initialized', False):
            initialize_buffer_in_handlebar(ContextInfo)

        # 更新数据缓冲区
        update_buffer_with_current_data(ContextInfo)

        # 如果需要自定义交易逻辑，可以在这里添加
        custom_trading_logic(ContextInfo)

    except Exception as e:
        print(f"❌ K线处理异常: {e}")
        import traceback
        traceback.print_exc()

def initialize_buffer_in_handlebar(ContextInfo):
    """
    在handlebar中初始化数据缓冲区并预加载历史数据
    """
    try:
        print("📊 首次运行handlebar，初始化数据缓冲区...")

        # 获取股票代码
        stock_code = ContextInfo.stockcode + '.' + ContextInfo.market

        # 创建数据缓冲区
        from QMT止盈止损下单模块 import MarketDataBuffer
        buffer_size = STRATEGY_CONFIG.get('buffer_size', 150)
        ContextInfo.data_buffer = MarketDataBuffer(buffer_size)

        # 尝试获取历史数据填充缓冲区
        print("📥 获取历史数据填充缓冲区...")
        history_count = STRATEGY_CONFIG.get('history_data_count', 100)

        success = ContextInfo.data_buffer.preload_history_data(
            ContextInfo, stock_code, '1m', history_count
        )

        if success:
            print(f"✅ 历史数据预加载成功，缓冲区包含 {ContextInfo.data_buffer.get_data_count()} 根K线")
        else:
            print("⚠️ 历史数据预加载失败，将使用实时数据逐步填充")

        # 标记缓冲区已初始化
        ContextInfo.buffer_initialized = True

    except Exception as e:
        print(f"❌ 缓冲区初始化异常: {e}")
        import traceback
        traceback.print_exc()
        # 即使失败也标记为已初始化，避免重复尝试
        ContextInfo.buffer_initialized = True

def update_buffer_with_current_data(ContextInfo):
    """
    使用当前K线数据更新缓冲区
    """
    try:
        # 确保缓冲区存在
        if not hasattr(ContextInfo, 'data_buffer'):
            print("⚠️ 数据缓冲区不存在，跳过更新")
            return

        # 获取当前价格
        current_price = ContextInfo.get_bar_timetag(ContextInfo.barpos)
        if hasattr(ContextInfo, 'close'):
            current_price = ContextInfo.close[-1]
        else:
            # 备用方案
            current_price = ContextInfo.get_market_data(['close'], [ContextInfo.stockcode + '.' + ContextInfo.market])
            if current_price:
                current_price = current_price[0]['close']
            else:
                print("⚠️ 无法获取当前价格")
                return

        # 获取其他OHLCV数据
        try:
            high_price = ContextInfo.high[-1] if hasattr(ContextInfo, 'high') else current_price
            low_price = ContextInfo.low[-1] if hasattr(ContextInfo, 'low') else current_price
            open_price = ContextInfo.open[-1] if hasattr(ContextInfo, 'open') else current_price
            volume = ContextInfo.vol[-1] if hasattr(ContextInfo, 'vol') else 0
        except:
            high_price = low_price = open_price = current_price
            volume = 0

        # 更新缓冲区
        ContextInfo.data_buffer.update_realtime_data(
            close_price=current_price,
            high_price=high_price,
            low_price=low_price,
            open_price=open_price,
            volume=volume,
            timestamp=ContextInfo.get_bar_timetag(ContextInfo.barpos)
        )

        # 计算技术指标
        ma5 = ContextInfo.data_buffer.calculate_ma(5)
        ma20 = ContextInfo.data_buffer.calculate_ma(20)
        atr = ContextInfo.data_buffer.calculate_atr(14)

        # 存储到上下文
        ContextInfo.ma5 = ma5
        ContextInfo.ma20 = ma20
        ContextInfo.atr = atr
        ContextInfo.current_price = current_price

        # 调试信息
        if getattr(ContextInfo, 'debug_mode', False):
            print(f"📊 数据更新: 价格={current_price:.3f}, 缓冲区={ContextInfo.data_buffer.get_data_count()}根K线")
            if ma5 and ma20:
                print(f"📈 指标: MA5={ma5:.3f}, MA20={ma20:.3f}, ATR={atr:.3f if atr else 'N/A'}")

    except Exception as e:
        print(f"❌ 缓冲区更新异常: {e}")
        import traceback
        traceback.print_exc()

def custom_trading_logic(ContextInfo):
    """
    自定义交易逻辑示例
    """
    try:
        # 检查是否有足够的指标数据
        if not all([
            hasattr(ContextInfo, 'ma5'),
            hasattr(ContextInfo, 'ma20'),
            hasattr(ContextInfo, 'current_price'),
            hasattr(ContextInfo, 'data_buffer')
        ]):
            return
        
        # 只在最新K线执行交易
        if not ContextInfo.is_last_bar():
            return
        
        # 检查数据是否足够
        if not ContextInfo.data_buffer.is_ready_for_trading(25):
            return
        
        ma5 = ContextInfo.ma5
        ma20 = ContextInfo.ma20
        current_price = ContextInfo.current_price
        atr = getattr(ContextInfo, 'atr', None)
        
        # 获取股票代码
        stock_code = getattr(ContextInfo, 'stock', ContextInfo.stockcode + '.' + ContextInfo.market)
        
        print(f"\n🔍 自定义交易逻辑检查:")
        print(f"   股票: {stock_code}")
        print(f"   当前价格: {current_price:.3f}")
        print(f"   MA5: {ma5:.3f}")
        print(f"   MA20: {ma20:.3f}")
        print(f"   ATR: {atr:.3f if atr else 'N/A'}")
        
        # 示例策略：均线交叉 + ATR过滤
        if ma5 > ma20 * 1.002:  # MA5明显高于MA20
            if atr and atr > current_price * 0.01:  # ATR大于1%，市场有足够波动
                print(f"📈 强烈买入信号: MA5({ma5:.3f}) >> MA20({ma20:.3f}), ATR={atr:.3f}")
                
                # 执行买入（如果需要）
                # result = execute_buy_order_enhanced(ContextInfo, current_price)
                # print(f"买入结果: {result}")
                
        elif ma5 < ma20 * 0.998:  # MA5明显低于MA20
            print(f"📉 卖出信号: MA5({ma5:.3f}) << MA20({ma20:.3f})")
            
            # 执行卖出（如果需要）
            # result = execute_sell_order_enhanced(ContextInfo, current_price, "均线死叉")
            # print(f"卖出结果: {result}")
        
        else:
            print(f"➡️ 无明确信号，继续观察")
        
    except Exception as e:
        print(f"❌ 自定义交易逻辑异常: {e}")
        import traceback
        traceback.print_exc()

def show_strategy_status(ContextInfo):
    """
    显示策略状态信息
    """
    try:
        if hasattr(ContextInfo, 'data_buffer'):
            buffer = ContextInfo.data_buffer
            print(f"\n📊 策略状态:")
            print(f"   数据缓冲区: {buffer.get_data_count()}根K线")
            print(f"   初始化状态: {'✅' if buffer.is_initialized else '❌'}")
            print(f"   交易就绪: {'✅' if buffer.is_ready_for_trading() else '❌'}")
            
            if hasattr(ContextInfo, 'ma5') and hasattr(ContextInfo, 'ma20'):
                print(f"   MA5: {ContextInfo.ma5:.3f}")
                print(f"   MA20: {ContextInfo.ma20:.3f}")
                print(f"   当前价格: {ContextInfo.current_price:.3f}")
        
    except Exception as e:
        print(f"❌ 状态显示异常: {e}")

# ============================================================================
# 策略信息
# ============================================================================

print("📄 QMT止盈止损策略使用示例已加载 - 数据缓冲区版本")
print("🔧 演示方案三优化的完整使用方法")
print("📅 版本: 1.0.0 (2024-12-19)")
print("⚠️ 注意: 请在QMT环境中使用，确保模块正确导入")

print("\n" + "="*60)
print("📖 使用说明:")
print("="*60)
print("1. 确保已正确导入 QMT止盈止损下单模块")
print("2. 在QMT中加载此示例策略")
print("3. 策略将自动:")
print("   - 预加载历史数据")
print("   - 实时计算技术指标")
print("   - 执行交易逻辑")
print("4. 可通过修改 STRATEGY_CONFIG 调整参数")
print("5. 启用 debug_mode 查看详细日志")
print("="*60)
