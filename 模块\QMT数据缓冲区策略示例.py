#coding:gbk
"""
QMT数据缓冲区策略示例 - 方案三优化完整版本
演示如何在QMT中正确使用历史数据缓冲区进行实时交易

核心特点：
1. init()中下载历史数据
2. handlebar()中获取历史数据并初始化缓冲区
3. 实时更新技术指标
4. 开盘即可交易

使用方法：
1. 在QMT中导入此策略文件
2. 策略会自动处理所有数据管理
3. 可通过修改配置参数调整行为
"""

# 导入必要的库
import datetime
import time
from collections import deque

# ============================================================================
# 数据缓冲区管理类（简化版）
# ============================================================================

class SimpleMarketDataBuffer:
    """
    简化的市场数据缓冲区管理类
    """
    
    def __init__(self, buffer_size=100):
        self.buffer_size = buffer_size
        self.close_buffer = deque(maxlen=buffer_size)
        self.high_buffer = deque(maxlen=buffer_size)
        self.low_buffer = deque(maxlen=buffer_size)
        self.open_buffer = deque(maxlen=buffer_size)
        self.volume_buffer = deque(maxlen=buffer_size)
        self.time_buffer = deque(maxlen=buffer_size)
        self.is_initialized = False
        
        print(f"📊 数据缓冲区初始化完成，容量: {buffer_size}根K线")
    
    def preload_history_data(self, ContextInfo, stock_code, period='1m', count=100):
        """预加载历史数据填充缓冲区"""
        try:
            print(f"📥 开始预加载历史数据: {stock_code}, 周期: {period}, 数量: {count}")
            
            # 使用QMT API获取历史数据
            hist_data = ContextInfo.get_market_data_ex(
                [],  # 获取所有字段
                [stock_code],
                period=period,
                count=count,
                dividend_type='back_ratio'  # 后复权
            )
            
            if hist_data is None or stock_code not in hist_data:
                print(f"⚠️ 历史数据获取失败，尝试备用方案...")
                hist_data = ContextInfo.get_market_data_ex(
                    [],
                    [stock_code],
                    period=period,
                    count=min(count, 50),
                    dividend_type='none'
                )
            
            if hist_data is None or stock_code not in hist_data:
                print(f"❌ 历史数据获取失败，将使用实时数据逐步填充缓冲区")
                return False
            
            # 解析历史数据
            data_df = hist_data[stock_code]
            data_count = len(data_df)
            
            print(f"✅ 成功获取 {data_count} 根历史K线数据")
            
            # 填充缓冲区
            for i in range(data_count):
                row = data_df.iloc[i]
                
                self.close_buffer.append(float(row.get('close', row.get('lastPrice', 0))))
                self.high_buffer.append(float(row.get('high', row.get('lastPrice', 0))))
                self.low_buffer.append(float(row.get('low', row.get('lastPrice', 0))))
                self.open_buffer.append(float(row.get('open', row.get('lastPrice', 0))))
                self.volume_buffer.append(float(row.get('volume', 0)))
                
                time_val = row.get('time', row.get('timetag', ''))
                self.time_buffer.append(str(time_val))
            
            self.is_initialized = True
            print(f"📊 缓冲区预加载完成，当前数据量: {len(self.close_buffer)}根K线")
            return True
            
        except Exception as e:
            print(f"❌ 历史数据预加载异常: {e}")
            return False
    
    def update_realtime_data(self, close_price, high_price=None, low_price=None, 
                           open_price=None, volume=0, timestamp=None):
        """更新实时数据到缓冲区"""
        self.close_buffer.append(float(close_price))
        self.high_buffer.append(float(high_price or close_price))
        self.low_buffer.append(float(low_price or close_price))
        self.open_buffer.append(float(open_price or close_price))
        self.volume_buffer.append(float(volume))
        self.time_buffer.append(str(timestamp or datetime.datetime.now()))
    
    def calculate_ma(self, period):
        """计算移动平均线"""
        if len(self.close_buffer) < period:
            return None
        recent_prices = list(self.close_buffer)[-period:]
        return sum(recent_prices) / period
    
    def calculate_atr(self, period=14):
        """计算ATR（平均真实波幅）"""
        if len(self.close_buffer) < period + 1:
            return None
        
        highs = list(self.high_buffer)[-period-1:]
        lows = list(self.low_buffer)[-period-1:]
        closes = list(self.close_buffer)[-period-1:]
        
        true_ranges = []
        for i in range(1, len(highs)):
            tr1 = highs[i] - lows[i]
            tr2 = abs(highs[i] - closes[i-1])
            tr3 = abs(lows[i] - closes[i-1])
            true_ranges.append(max(tr1, tr2, tr3))
        
        return sum(true_ranges) / len(true_ranges) if true_ranges else 0
    
    def get_current_price(self):
        """获取当前价格"""
        return self.close_buffer[-1] if self.close_buffer else None
    
    def get_data_count(self):
        """获取缓冲区数据量"""
        return len(self.close_buffer)
    
    def is_ready_for_trading(self, min_periods=20):
        """检查是否有足够数据进行交易"""
        return len(self.close_buffer) >= min_periods

# ============================================================================
# 配置参数
# ============================================================================

BUFFER_CONFIG = {
    'buffer_size': 150,           # 缓冲区大小
    'history_data_count': 100,    # 历史数据量
    'min_trading_periods': 25,    # 最少交易周期
    'debug_mode': True            # 调试模式
}

# ============================================================================
# QMT策略函数
# ============================================================================

def init(ContextInfo):
    """
    策略初始化函数 - QMT标准模式
    在init()中只能下载数据，不能获取数据
    """
    print("🚀 启动QMT数据缓冲区策略")
    print("="*60)
    
    # 获取股票代码
    stock_code = ContextInfo.stockcode + '.' + ContextInfo.market
    print(f"📊 目标股票: {stock_code}")
    print(f"🕐 初始化时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 下载历史数据（QMT要求在init中完成）
    print(f"\n📥 开始下载历史数据...")
    
    try:
        # 计算下载日期范围
        end_date = datetime.datetime.now().strftime('%Y%m%d')
        start_date = (datetime.datetime.now() - datetime.timedelta(days=10)).strftime('%Y%m%d')
        
        print(f"📅 下载范围: {start_date} ~ {end_date}")
        
        # 下载分钟级历史数据
        download_history_data(stock_code, "1m", start_date, end_date)
        print(f"✅ 分钟数据下载完成")
        
        # 下载日线数据用于长期指标
        download_history_data(stock_code, "1d", start_date, end_date)
        print(f"✅ 日线数据下载完成")
        
    except Exception as e:
        print(f"⚠️ 历史数据下载异常: {e}")
        print(f"💡 提示: 将在handlebar中逐步填充数据缓冲区")
    
    # 等待数据写入完成
    print(f"\n⏳ 等待数据写入完成...")
    time.sleep(5)
    
    # 标记初始化完成
    ContextInfo.init_completed = True
    ContextInfo.buffer_initialized = False
    
    print(f"\n💡 策略初始化完成，等待handlebar进行数据获取...")
    print("="*60)

def handlebar(ContextInfo):
    """
    K线数据处理函数 - QMT标准模式
    在handlebar中进行数据获取和缓冲区管理
    """
    try:
        # 第一次运行时初始化数据缓冲区
        if not getattr(ContextInfo, 'buffer_initialized', False):
            initialize_buffer(ContextInfo)
        
        # 更新数据缓冲区
        update_buffer_with_current_data(ContextInfo)
        
        # 只在最新K线执行交易逻辑
        if not ContextInfo.is_last_bar():
            return
        
        # 检查是否有足够数据进行交易
        if hasattr(ContextInfo, 'data_buffer'):
            min_periods = BUFFER_CONFIG['min_trading_periods']
            if not ContextInfo.data_buffer.is_ready_for_trading(min_periods):
                print(f"⏳ 数据不足，需要{min_periods}根K线，当前{ContextInfo.data_buffer.get_data_count()}根")
                return
            
            # 执行交易逻辑
            execute_trading_logic(ContextInfo)
        
    except Exception as e:
        print(f"❌ K线处理异常: {e}")
        import traceback
        traceback.print_exc()

def initialize_buffer(ContextInfo):
    """在handlebar中初始化数据缓冲区并预加载历史数据"""
    try:
        print("📊 首次运行handlebar，初始化数据缓冲区...")
        
        # 获取股票代码
        stock_code = ContextInfo.stockcode + '.' + ContextInfo.market
        
        # 创建数据缓冲区
        buffer_size = BUFFER_CONFIG['buffer_size']
        ContextInfo.data_buffer = SimpleMarketDataBuffer(buffer_size)
        
        # 尝试获取历史数据填充缓冲区
        print("📥 获取历史数据填充缓冲区...")
        history_count = BUFFER_CONFIG['history_data_count']
        
        success = ContextInfo.data_buffer.preload_history_data(
            ContextInfo, stock_code, '1m', history_count
        )
        
        if success:
            print(f"✅ 历史数据预加载成功，缓冲区包含 {ContextInfo.data_buffer.get_data_count()} 根K线")
        else:
            print("⚠️ 历史数据预加载失败，将使用实时数据逐步填充")
        
        # 标记缓冲区已初始化
        ContextInfo.buffer_initialized = True
        
    except Exception as e:
        print(f"❌ 缓冲区初始化异常: {e}")
        ContextInfo.buffer_initialized = True  # 避免重复尝试

def update_buffer_with_current_data(ContextInfo):
    """使用QMT当前K线数据更新缓冲区"""
    try:
        if not hasattr(ContextInfo, 'data_buffer'):
            return
        
        # 获取当前K线数据
        if hasattr(ContextInfo, 'close') and len(ContextInfo.close) > 0:
            current_price = ContextInfo.close[-1]
            high_price = ContextInfo.high[-1] if hasattr(ContextInfo, 'high') else current_price
            low_price = ContextInfo.low[-1] if hasattr(ContextInfo, 'low') else current_price
            open_price = ContextInfo.open[-1] if hasattr(ContextInfo, 'open') else current_price
            volume = ContextInfo.vol[-1] if hasattr(ContextInfo, 'vol') else 0
        else:
            # 备用方案
            stock_code = ContextInfo.stockcode + '.' + ContextInfo.market
            market_data = ContextInfo.get_market_data(['close', 'high', 'low', 'open', 'volume'], [stock_code])
            
            if market_data and len(market_data) > 0:
                data = market_data[0]
                current_price = data.get('close', 0)
                high_price = data.get('high', current_price)
                low_price = data.get('low', current_price)
                open_price = data.get('open', current_price)
                volume = data.get('volume', 0)
            else:
                return
        
        # 获取时间戳
        try:
            timestamp = ContextInfo.get_bar_timetag(ContextInfo.barpos)
        except:
            timestamp = datetime.datetime.now()
        
        # 更新缓冲区
        ContextInfo.data_buffer.update_realtime_data(
            close_price=current_price,
            high_price=high_price,
            low_price=low_price,
            open_price=open_price,
            volume=volume,
            timestamp=timestamp
        )
        
        # 计算技术指标
        ma5 = ContextInfo.data_buffer.calculate_ma(5)
        ma20 = ContextInfo.data_buffer.calculate_ma(20)
        atr = ContextInfo.data_buffer.calculate_atr(14)
        
        # 存储到上下文
        ContextInfo.ma5 = ma5
        ContextInfo.ma20 = ma20
        ContextInfo.atr = atr
        ContextInfo.current_price = current_price
        
        # 调试信息
        if BUFFER_CONFIG['debug_mode']:
            print(f"📊 数据更新: 价格={current_price:.3f}, 缓冲区={ContextInfo.data_buffer.get_data_count()}根K线")
            if ma5 and ma20:
                print(f"📈 指标: MA5={ma5:.3f}, MA20={ma20:.3f}, ATR={atr:.3f if atr else 'N/A'}")
        
    except Exception as e:
        print(f"❌ 缓冲区更新异常: {e}")

def execute_trading_logic(ContextInfo):
    """执行交易逻辑 - 基于缓冲区数据和指标"""
    try:
        # 获取指标数据
        ma5 = getattr(ContextInfo, 'ma5', None)
        ma20 = getattr(ContextInfo, 'ma20', None)
        atr = getattr(ContextInfo, 'atr', None)
        current_price = getattr(ContextInfo, 'current_price', None)
        
        if not all([ma5, ma20, current_price]):
            print("⚠️ 指标数据不完整，跳过交易逻辑")
            return
        
        print(f"\n📊 交易信号检查:")
        print(f"   当前价格: {current_price:.3f}")
        print(f"   MA5: {ma5:.3f}")
        print(f"   MA20: {ma20:.3f}")
        print(f"   ATR: {atr:.3f if atr else 'N/A'}")
        
        # 简单的均线交叉策略示例
        if ma5 > ma20 * 1.002:  # MA5明显高于MA20
            if atr and atr > current_price * 0.01:  # ATR大于1%，市场有足够波动
                print(f"📈 强烈买入信号: MA5({ma5:.3f}) >> MA20({ma20:.3f}), ATR={atr:.3f}")
                # 这里可以调用买入函数
                
        elif ma5 < ma20 * 0.998:  # MA5明显低于MA20
            print(f"📉 卖出信号: MA5({ma5:.3f}) << MA20({ma20:.3f})")
            # 这里可以调用卖出函数
        
        else:
            print(f"➡️ 无明确信号，继续观察")
        
    except Exception as e:
        print(f"❌ 交易逻辑执行异常: {e}")

# ============================================================================
# 策略信息
# ============================================================================

print("📄 QMT数据缓冲区策略示例已加载")
print("🔧 演示方案三优化的完整使用方法")
print("📅 版本: 1.0.0 (2024-12-19)")
print("⚠️ 注意: 请在QMT环境中使用")

print("\n" + "="*60)
print("📖 核心优势:")
print("="*60)
print("✅ 开盘即可交易（预加载历史数据）")
print("✅ 实时指标计算（MA5、MA20、ATR）")
print("✅ 高效内存管理（滑动窗口机制）")
print("✅ 完整的错误处理和恢复机制")
print("✅ QMT标准模式兼容（init下载，handlebar获取）")
print("="*60)
