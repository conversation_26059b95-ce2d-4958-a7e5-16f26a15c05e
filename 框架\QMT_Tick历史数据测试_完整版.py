#coding:gbk
"""
QMT Tick历史数据测试策略 - 完整版
专门获取并打印过去100个tick数据

重要特性:
1. 先下载tick历史数据，再获取数据
2. Tick数据成交量是增量成交量，需要自己处理
3. 只能使用lastPrice最新价，OHLC都是当日价格
4. 获取并打印过去100个tick数据

作者: QMT策略开发
版本: 3.0.0
日期: 2024-12-19
"""

import numpy as np
from datetime import datetime
import time

# ============================================================================
# QMT策略标准接口
# ============================================================================

def init(ContextInfo):
    """
    策略初始化函数 - 下载tick历史数据
    """
    print("🚀 QMT Tick历史数据测试策略启动 - 完整版")
    print("="*80)
    
    # 获取股票代码
    ContextInfo.stock = ContextInfo.stockcode + '.' + ContextInfo.market
    print(f"📊 测试股票: {ContextInfo.stock}")
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # === 下载tick历史数据 ===
    print(f"\n📥 开始下载tick历史数据...")
    
    # 设置下载参数 - 只下载过去一个交易日的数据（QMT限制）
    from datetime import datetime, timedelta

    # 计算上一个交易日
    today = datetime.now()
    # 如果今天是周一，上一个交易日是上周五（3天前）
    # 如果今天是其他工作日，上一个交易日是昨天（1天前）
    if today.weekday() == 0:  # 周一
        last_trading_day = today - timedelta(days=3)
    elif today.weekday() == 6:  # 周日
        last_trading_day = today - timedelta(days=2)
    else:  # 周二到周六
        last_trading_day = today - timedelta(days=1)

    start_date = last_trading_day.strftime('%Y%m%d')  # 上一个交易日
    end_date = start_date  # 只下载这一天的数据
    period = "tick"        # tick数据

    print(f"📅 下载范围: {start_date} (过去一个交易日，QMT限制)")
    print(f"📊 数据周期: {period}")
    print(f"🎯 目标股票: {ContextInfo.stock}")
    
    try:
        # 下载tick历史数据
        download_history_data(ContextInfo.stock, period, start_date, end_date)
        print(f"✅ Tick历史数据下载完成")
        
    except Exception as e:
        print(f"⚠️ Tick历史数据下载异常: {e}")
        print(f"💡 提示: 请检查网络连接和股票代码")
    
    # 等待下载完成
    print(f"\n⏳ 等待tick数据下载完成...")
    time.sleep(15)  # 等待15秒确保数据写入完成
    
    # 初始化计数器
    ContextInfo.tick_counter = 0
    
    print(f"\n💡 Tick数据下载完成，等待handlebar()函数进行数据获取...")
    print("="*80)

def handlebar(ContextInfo):
    """
    K线数据处理函数 - 获取并打印过去100个tick数据
    """
    ContextInfo.tick_counter = getattr(ContextInfo, 'tick_counter', 0) + 1
    
    # 每次K线都执行tick数据获取
    print(f"\n🔍 获取过去100个Tick数据 (第{ContextInfo.tick_counter}根K线)")
    print("-" * 80)
    
    # 获取并打印100个tick数据
    success = get_and_print_100_ticks(ContextInfo)
    
    if success:
        print(f"🎉 成功获取并打印了过去100个tick数据！")
    else:
        print(f"❌ 获取tick数据失败")
    
    print("-" * 80)

# ============================================================================
# Tick数据获取函数
# ============================================================================

def get_and_print_100_ticks(ContextInfo):
    """
    获取并打印过去100个tick数据
    """
    print(f"📦 开始获取过去100个Tick数据: {ContextInfo.stock}")
    
    # 基础信息检查
    print(f"  🔍 基础信息检查:")
    print(f"    股票代码: {ContextInfo.stock}")
    print(f"    原始代码: {ContextInfo.stockcode}")
    print(f"    市场代码: {ContextInfo.market}")
    print(f"    当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 使用正确的历史数据获取方式（参考示例文档）
        print(f"  📥 获取历史tick数据...")
        
        # 设置时间范围（与下载时保持一致）
        from datetime import timedelta
        today = datetime.now()
        if today.weekday() == 0:  # 周一
            last_trading_day = today - timedelta(days=3)
        elif today.weekday() == 6:  # 周日
            last_trading_day = today - timedelta(days=2)
        else:  # 周二到周六
            last_trading_day = today - timedelta(days=1)

        start_date = last_trading_day.strftime('%Y%m%d')
        end_date = start_date  # 只获取这一天的数据
        
        print(f"  📊 API参数: period=tick, start_time={start_date}, end_time={end_date or '最新'}")
        
        # 方法1: 使用历史数据API（推荐方式）
        hist_tick_data = ContextInfo.get_market_data_ex(
            [],  # 获取所有字段
            [ContextInfo.stock],
            period='tick',
            start_time=start_date,
            end_time=end_date,
            dividend_type="back_ratio"  # 后复权
        )
        
        # 如果方法1失败，尝试方法2（直接获取最近数据）
        if hist_tick_data is None or ContextInfo.stock not in hist_tick_data:
            print(f"  🔄 方法1失败，尝试方法2: 获取最近100个tick数据...")
            hist_tick_data = ContextInfo.get_market_data_ex(
                [],  # 获取所有字段
                [ContextInfo.stock],
                period='tick',
                count=100,  # 获取最近100条
                dividend_type='none'
            )
        
        # 检查数据获取结果
        if hist_tick_data is None:
            print(f"  ❌ 获取tick数据失败: API返回None")
            return False
        
        if ContextInfo.stock not in hist_tick_data:
            print(f"  ❌ 股票代码不在返回数据中")
            print(f"  📊 期望: {ContextInfo.stock}, 实际: {list(hist_tick_data.keys())}")
            return False
        
        data_df = hist_tick_data[ContextInfo.stock]
        tick_count = len(data_df)
        
        print(f"  ✅ 成功获取 {tick_count} 笔tick数据")
        print(f"  📊 数据类型: {type(data_df)}")
        print(f"  📊 数据结构: {data_df.columns.tolist() if hasattr(data_df, 'columns') else 'N/A'}")
        
        if tick_count == 0:
            print(f"  ⚠️ 获取到的tick数据为空")
            return False
        
        # 限制显示最近100条数据
        if tick_count > 100:
            print(f"  📝 数据量较大({tick_count}条)，只显示最近100条")
            data_df = data_df.tail(100)
            tick_count = 100
        
        # 打印所有tick数据
        print(f"\n  📋 所有Tick数据详情:")
        print(f"  {'序号':<4} {'时间':<20} {'价格':<10} {'成交量':<12} {'增量':<12}")
        print(f"  {'-'*4} {'-'*20} {'-'*10} {'-'*12} {'-'*12}")
        
        prev_volume = 0
        total_incremental = 0
        
        for i, (_, tick) in enumerate(data_df.iterrows()):
            tick_time = tick.get('time', '')
            price = tick.get('lastPrice', 0)
            volume = tick.get('volume', 0)
            
            # 计算增量成交量（纯差值）
            if i == 0:
                incremental = volume
            else:
                incremental = volume - prev_volume
            
            total_incremental += incremental if incremental > 0 else 0
            prev_volume = volume
            
            # 时间格式转换
            if isinstance(tick_time, (int, float)) and tick_time > 1000000000:
                try:
                    if tick_time > 1000000000000:  # 毫秒时间戳
                        time_readable = datetime.fromtimestamp(tick_time/1000).strftime('%H:%M:%S.%f')[:-3]
                    else:  # 秒时间戳
                        time_readable = datetime.fromtimestamp(tick_time).strftime('%H:%M:%S')
                except:
                    time_readable = str(tick_time)
            else:
                time_readable = str(tick_time)
            
            print(f"  {i+1:<4} {time_readable:<20} {price:<10.3f} {volume:<12.0f} {incremental:<12.0f}")
        
        # 统计信息
        prices = [tick.get('lastPrice', 0) for _, tick in data_df.iterrows()]
        volumes = [tick.get('volume', 0) for _, tick in data_df.iterrows()]
        
        valid_prices = [p for p in prices if p > 0]
        if valid_prices:
            min_price = min(valid_prices)
            max_price = max(valid_prices)
            avg_price = sum(valid_prices) / len(valid_prices)
            
            print(f"\n  📊 数据统计:")
            print(f"    总tick数: {tick_count}")
            print(f"    价格范围: {min_price:.3f} ~ {max_price:.3f}")
            print(f"    平均价格: {avg_price:.3f}")
            print(f"    成交量范围: {min(volumes)} ~ {max(volumes)}")
            print(f"    总增量成交量: {total_incremental}")
        
        print(f"  ✅ 100个Tick数据打印完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 获取tick数据异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

# ============================================================================
# 策略信息
# ============================================================================

print("📄 QMT Tick历史数据测试策略已加载 - 完整版")
print("🔧 先下载tick历史数据，再获取并打印过去100个tick数据")
print("📅 版本: 3.0.0 (2024-12-19)")
