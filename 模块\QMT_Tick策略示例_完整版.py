#coding:gbk
"""
QMT Tick策略示例 - 完整版
专门针对tick周期的止盈止损交易策略

核心特点：
1. 专门针对tick数据优化
2. init()中下载历史tick数据
3. handlebar()中获取历史tick数据并初始化缓冲区
4. 实时更新tick指标
5. 开盘即可交易

使用方法：
1. 在QMT中设置为tick周期
2. 导入此策略文件
3. 策略会自动处理所有tick数据管理
4. 可通过修改配置参数调整行为

作者: QMT策略开发
版本: 1.0.0
日期: 2024-12-19
"""

# 导入QMT止盈止损下单模块
from QMT止盈止损下单模块 import (
    MarketDataBuffer,
    STRATEGY_CONFIG,
    initialize_buffer_in_handlebar,
    update_buffer_with_current_data_qmt,
    execute_buy_order_enhanced,
    execute_sell_order_enhanced,
    get_main_account_id
)

# 导入必要的库
import datetime
import time

# ============================================================================
# Tick策略配置参数
# ============================================================================

# 针对tick数据的专门配置
TICK_STRATEGY_CONFIG = {
    # Tick数据缓冲区配置
    'enable_data_buffer': True,     # 启用tick数据缓冲区
    'buffer_size': 1000,            # tick缓冲区大小（tick数据量大）
    'history_data_count': 500,      # 预加载历史tick数据数量
    'min_trading_periods': 100,     # 开始交易所需的最少tick周期
    'debug_mode': True,             # 启用调试模式
    
    # 交易参数
    'buy_hang_offset_ratio': 0.001,  # 买入挂单偏移（tick级别精度更高）
    'sell_hang_offset_ratio': 0.001, # 卖出挂单偏移
    'take_profit_ratio': 0.02,       # 止盈比例（2%）
    'stop_loss_ratio': 0.01,         # 止损比例（1%）
    
    # Tick特殊参数
    'tick_price_threshold': 0.01,    # tick价格变动阈值
    'volume_threshold': 1000,        # 成交量阈值
}

# 更新主配置
STRATEGY_CONFIG.update(TICK_STRATEGY_CONFIG)

# ============================================================================
# QMT策略函数
# ============================================================================

def init(ContextInfo):
    """
    策略初始化函数 - Tick数据专用版
    """
    print("🚀 启动QMT Tick止盈止损策略")
    print("="*60)
    
    # 基础设置
    ContextInfo.stock = ContextInfo.stockcode + '.' + ContextInfo.market
    print(f"📊 交易标的: {ContextInfo.stock}")
    print(f"🕐 初始化时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📈 数据周期: Tick")
    
    # 账户设置
    try:
        ContextInfo.acct = get_main_account_id(ContextInfo)
        ContextInfo.acct_type = "STOCK"
        print(f"✅ 账户设置: {ContextInfo.acct}")
    except Exception as e:
        print(f"⚠️ 账户设置异常: {e}")
        ContextInfo.acct = "*********"
        ContextInfo.acct_type = "STOCK"
    
    # 下载历史tick数据
    print(f"\n📥 开始下载历史tick数据...")
    
    try:
        # 获取当前时间
        now = datetime.datetime.now()
        current_hour = now.hour
        
        # 判断是否在交易时间内（9:00-15:30）
        is_trading_time = (9 <= current_hour <= 15)
        
        print(f"🕐 当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 是否交易时间: {'是' if is_trading_time else '否'}")
        
        # 计算下载日期范围
        if is_trading_time:
            # 盘中运行：下载当日tick数据
            start_date = now.strftime('%Y%m%d')
            end_date = now.strftime('%Y%m%d')
            print(f"📅 盘中模式 - 下载当日tick数据: {start_date}")
        else:
            # 非盘中：下载最近两个交易日tick数据
            end_date = now.strftime('%Y%m%d')
            start_date = (now - datetime.timedelta(days=3)).strftime('%Y%m%d')
            print(f"📅 非盘中模式 - 下载最近交易日tick数据: {start_date} ~ {end_date}")
        
        # 下载tick历史数据
        try:
            print(f"📊 下载tick数据: {ContextInfo.stock}")
            download_history_data(ContextInfo.stock, "tick", start_date, end_date)
            print(f"✅ Tick数据下载完成")
        except NameError:
            print(f"⚠️ download_history_data函数不可用，将在handlebar中获取历史tick数据")
        except Exception as e:
            print(f"⚠️ Tick数据下载异常: {e}")
            print(f"💡 提示: 将在handlebar中获取历史tick数据")
        
    except Exception as e:
        print(f"⚠️ 历史数据下载异常: {e}")
        print(f"💡 提示: 将在handlebar中逐步填充tick数据缓冲区")
    
    # 等待数据写入完成
    print(f"\n⏳ 等待tick数据写入完成...")
    time.sleep(2)
    
    # 初始化策略状态
    ContextInfo.strategy_state = {}
    ContextInfo.tick_count = 0
    ContextInfo.last_price = 0
    ContextInfo.buffer_initialized = False
    ContextInfo.debug_mode = TICK_STRATEGY_CONFIG.get('debug_mode', False)
    
    print(f"\n💡 Tick策略初始化完成，等待handlebar进行数据获取...")
    print("="*60)

def handlebar(ContextInfo):
    """
    Tick数据处理函数 - 每个tick都会调用
    """
    try:
        # 第一次运行时初始化tick数据缓冲区
        if not getattr(ContextInfo, 'buffer_initialized', False):
            initialize_tick_buffer(ContextInfo)
        
        # 更新tick数据缓冲区
        update_tick_buffer(ContextInfo)
        
        # 检查是否有足够tick数据进行交易
        if hasattr(ContextInfo, 'data_buffer'):
            min_periods = TICK_STRATEGY_CONFIG.get('min_trading_periods', 100)
            if not ContextInfo.data_buffer.is_ready_for_trading(min_periods):
                if getattr(ContextInfo, 'debug_mode', False):
                    tick_count = ContextInfo.data_buffer.get_data_count()
                    if tick_count % 50 == 0:  # 每50个tick打印一次
                        print(f"⏳ Tick数据积累中，需要{min_periods}个tick，当前{tick_count}个")
                return
        
        # 获取当前tick价格
        if hasattr(ContextInfo, 'current_price') and ContextInfo.current_price:
            current_price = ContextInfo.current_price
        else:
            # 备用方案：直接获取最新价
            try:
                current_price = ContextInfo.get_market_data(['lastPrice'], [ContextInfo.stock])[0]['lastPrice']
            except:
                print("⚠️ 无法获取tick价格数据，跳过本次执行")
                return
        
        # 执行tick级别的交易逻辑
        execute_tick_trading_logic(ContextInfo, current_price)
        
    except Exception as e:
        print(f"❌ Tick处理异常: {e}")
        import traceback
        traceback.print_exc()

def initialize_tick_buffer(ContextInfo):
    """初始化tick数据缓冲区"""
    try:
        print("📊 首次运行handlebar，初始化tick数据缓冲区...")
        
        # 获取股票代码
        stock_code = ContextInfo.stockcode + '.' + ContextInfo.market
        
        # 创建tick数据缓冲区
        buffer_size = TICK_STRATEGY_CONFIG.get('buffer_size', 1000)
        ContextInfo.data_buffer = MarketDataBuffer(buffer_size)
        
        # 尝试获取历史tick数据填充缓冲区
        print("📥 获取历史tick数据填充缓冲区...")
        history_count = TICK_STRATEGY_CONFIG.get('history_data_count', 500)
        
        success = ContextInfo.data_buffer.preload_history_data(
            ContextInfo, stock_code, 'tick', history_count
        )
        
        if success:
            print(f"✅ 历史tick数据预加载成功，缓冲区包含 {ContextInfo.data_buffer.get_data_count()} 个tick")
        else:
            print("⚠️ 历史tick数据预加载失败，将使用实时tick数据逐步填充")
        
        # 标记缓冲区已初始化
        ContextInfo.buffer_initialized = True
        
    except Exception as e:
        print(f"❌ Tick缓冲区初始化异常: {e}")
        ContextInfo.buffer_initialized = True  # 避免重复尝试

def update_tick_buffer(ContextInfo):
    """更新tick数据缓冲区 - 使用tick合成K线方法"""
    try:
        if not hasattr(ContextInfo, 'data_buffer'):
            return

        # 获取当前tick数据
        try:
            market_data = ContextInfo.get_market_data(['lastPrice', 'lastVolume'], [ContextInfo.stock])
            if market_data and len(market_data) > 0:
                data = market_data[0]
                current_price = data.get('lastPrice', 0)
                tick_volume = data.get('lastVolume', 0)  # tick增量成交量
            else:
                return
        except:
            return

        # 获取时间戳
        try:
            timestamp = ContextInfo.get_bar_timetag(ContextInfo.barpos)
        except:
            timestamp = datetime.datetime.now()

        # 使用新的tick数据更新方法（自动合成K线）
        ContextInfo.data_buffer.update_tick_data(
            tick_price=current_price,
            tick_volume=tick_volume,
            timestamp=timestamp
        )

        # 计算基于合成K线的技术指标
        ma5 = ContextInfo.data_buffer.calculate_ma(5)   # 最近5根合成K线的MA
        ma20 = ContextInfo.data_buffer.calculate_ma(20) # 最近20根合成K线的MA
        ma50 = ContextInfo.data_buffer.calculate_ma(50) # 最近50根合成K线的MA

        # 存储到上下文
        ContextInfo.ma5 = ma5
        ContextInfo.ma20 = ma20
        ContextInfo.ma50 = ma50
        ContextInfo.current_price = current_price
        ContextInfo.tick_count += 1

        # 调试信息（每100个tick打印一次）
        if TICK_STRATEGY_CONFIG.get('debug_mode', False) and ContextInfo.tick_count % 100 == 0:
            kline_count = ContextInfo.data_buffer.get_data_count()
            print(f"📊 Tick更新: 价格={current_price:.3f}, 已合成{kline_count}根K线")
            if ma5 and ma20:
                print(f"📈 K线指标: MA5={ma5:.3f}, MA20={ma20:.3f}, MA50={ma50:.3f if ma50 else 'N/A'}")

    except Exception as e:
        print(f"❌ Tick缓冲区更新异常: {e}")

def execute_tick_trading_logic(ContextInfo, current_price):
    """执行tick级别的交易逻辑"""
    try:
        # 获取指标数据
        ma5 = getattr(ContextInfo, 'ma5', None)
        ma20 = getattr(ContextInfo, 'ma20', None)
        ma50 = getattr(ContextInfo, 'ma50', None)
        
        if not all([ma5, ma20, current_price]):
            return
        
        # 价格变动检查
        price_threshold = TICK_STRATEGY_CONFIG.get('tick_price_threshold', 0.01)
        if abs(current_price - getattr(ContextInfo, 'last_price', current_price)) < price_threshold:
            return  # 价格变动太小，跳过
        
        # 简单的tick级别均线策略
        if ma5 > ma20 * 1.001:  # MA5明显高于MA20（tick级别用更小的阈值）
            if ma50 and ma5 > ma50 * 1.002:  # 确认趋势
                # 强烈买入信号
                if ContextInfo.tick_count % 200 == 0:  # 控制交易频率
                    print(f"📈 Tick买入信号: 价格={current_price:.3f}, MA5={ma5:.3f}, MA20={ma20:.3f}")
                    # 这里可以调用买入函数
                    # execute_buy_order_enhanced(ContextInfo, current_price)
                    
        elif ma5 < ma20 * 0.999:  # MA5明显低于MA20
            if ma50 and ma5 < ma50 * 0.998:  # 确认趋势
                # 卖出信号
                if ContextInfo.tick_count % 200 == 0:  # 控制交易频率
                    print(f"📉 Tick卖出信号: 价格={current_price:.3f}, MA5={ma5:.3f}, MA20={ma20:.3f}")
                    # 这里可以调用卖出函数
                    # execute_sell_order_enhanced(ContextInfo, current_price)
        
        # 更新最后价格
        ContextInfo.last_price = current_price
        
    except Exception as e:
        print(f"❌ Tick交易逻辑执行异常: {e}")

# ============================================================================
# 策略信息
# ============================================================================

print("📄 QMT Tick策略示例已加载")
print("🔧 专门针对tick周期优化的完整策略")
print("📅 版本: 1.0.0 (2024-12-19)")
print("⚠️ 注意: 请在QMT的tick周期中使用")

print("\n" + "="*60)
print("📖 Tick策略特点:")
print("="*60)
print("✅ 专门针对tick数据优化")
print("✅ 开盘即可交易（预加载历史tick数据）")
print("✅ Tick自动合成K线（2个tick合成1根K线）")
print("✅ 基于合成K线计算技术指标（MA5、MA20、MA50）")
print("✅ 高效内存管理（滑动窗口机制）")
print("✅ 完整的错误处理和恢复机制")
print("✅ QMT标准模式兼容（init下载，handlebar获取）")
print("✅ 高频交易支持")
print("")
print("📊 Tick合成K线逻辑:")
print("   • QMT tick周期：3秒一个tick")
print("   • 合成策略：2个tick合成1根K线（6秒K线）")
print("   • OHLC计算：开盘=第1个tick价格，收盘=第2个tick价格")
print("   • 高低价：取2个tick价格的最高和最低值")
print("   • 成交量：2个tick成交量相加")
print("   • 指标计算：基于合成的K线计算MA等技术指标")
print("="*60)
