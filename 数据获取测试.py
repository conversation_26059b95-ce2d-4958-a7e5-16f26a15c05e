#coding:gbk

"""
数据获取测试脚本
用于验证修复后的数据获取逻辑是否正确
"""

import pandas as pd
import numpy as np

def test_data_access():
    """
    测试数据访问逻辑
    """
    print("🧪 测试数据访问逻辑")
    
    # 模拟不同的数据结构
    test_cases = [
        # 完整的4列数据
        {
            'name': '完整OHLCV数据',
            'data': pd.DataFrame({
                'close': [10.1, 10.2, 10.3],
                'high': [10.2, 10.3, 10.4],
                'low': [10.0, 10.1, 10.2],
                'volume': [1000, 1100, 1200]
            })
        },
        # 只有收盘价数据
        {
            'name': '仅收盘价数据',
            'data': pd.DataFrame({
                'close': [10.1, 10.2, 10.3]
            })
        },
        # 空数据
        {
            'name': '空数据',
            'data': pd.DataFrame()
        }
    ]
    
    for case in test_cases:
        print(f"\n📊 测试案例: {case['name']}")
        stock_data = case['data']
        
        try:
            print(f"   数据结构: {stock_data.shape}, 列数: {len(stock_data.columns)}")
            print(f"   列名: {list(stock_data.columns)}")
            
            # 应用修复后的逻辑
            if len(stock_data.columns) >= 4:
                closes = list(stock_data.iloc[:, 0])
                highs = list(stock_data.iloc[:, 1])
                lows = list(stock_data.iloc[:, 2])
                volumes = list(stock_data.iloc[:, 3])
                print("   ✅ 获取完整的OHLCV数据")
                print(f"   收盘价: {closes}")
                print(f"   最高价: {highs}")
                print(f"   最低价: {lows}")
                print(f"   成交量: {volumes}")
            elif len(stock_data.columns) >= 1:
                closes = list(stock_data.iloc[:, 0])
                highs = closes.copy()
                lows = closes.copy()
                volumes = [1] * len(closes)
                print("   ⚠️ 数据列数不足，使用收盘价填充其他数据")
                print(f"   收盘价: {closes}")
                print(f"   填充最高价: {highs}")
                print(f"   填充最低价: {lows}")
                print(f"   默认成交量: {volumes}")
            else:
                print("   ❌ 数据为空或格式错误")
                
        except Exception as e:
            print(f"   ❌ 错误: {e}")

def test_atr_calculation():
    """
    测试ATR计算
    """
    print("\n🧪 测试ATR计算")
    
    # 模拟价格数据
    closes = [10.0, 10.1, 10.2, 9.9, 10.3, 10.1, 10.4]
    highs = [10.1, 10.2, 10.3, 10.0, 10.4, 10.2, 10.5]
    lows = [9.9, 10.0, 10.1, 9.8, 10.2, 10.0, 10.3]
    
    try:
        # 计算ATR
        tr_list = []
        for i in range(1, len(closes)):
            high = highs[i]
            low = lows[i]
            prev_close = closes[i-1]
            
            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)
            tr = max(tr1, tr2, tr3)
            tr_list.append(tr)
        
        atr = np.mean(tr_list[-5:])  # 5日ATR
        print(f"   ✅ ATR计算成功: {atr:.4f}")
        print(f"   真实波幅序列: {tr_list}")
        
    except Exception as e:
        print(f"   ❌ ATR计算失败: {e}")

if __name__ == "__main__":
    test_data_access()
    test_atr_calculation()
    print("\n🎯 测试完成")
