# -*- coding: utf-8 -*-
"""
测试QMT兼容版文件是否能正常导入和运行
"""

def test_qmt_compatible_file():
    """测试QMT兼容版文件"""
    try:
        print("🔍 开始测试QMT兼容版文件...")
        
        # 尝试导入文件
        import sys
        import os
        sys.path.append('框架')
        
        # 模拟QMT环境的基本函数
        def mock_get_last_price():
            return 10.0
            
        def mock_get_last_volume():
            return 1000
            
        def mock_is_last_bar():
            return True
            
        # 创建模拟的C对象
        class MockC:
            def __init__(self):
                self.stockcode = "000001"
                self.market = "SZ"
                
            def get_last_price(self):
                return mock_get_last_price()
                
            def get_last_volume(self):
                return mock_get_last_volume()
                
            def is_last_bar(self):
                return mock_is_last_bar()
        
        # 导入QMT兼容版文件
        import importlib.util
        spec = importlib.util.spec_from_file_location("qmt_strategy", "框架/6sk线_QMT兼容版.py")
        qmt_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(qmt_module)

        init = qmt_module.init
        handlebar = qmt_module.handlebar
        CompleteCMFBIASDivergenceDetector = qmt_module.CompleteCMFBIASDivergenceDetector
        
        print("✅ 文件导入成功")
        
        # 测试检测器
        detector = CompleteCMFBIASDivergenceDetector()
        print("✅ 检测器创建成功")
        
        # 创建测试数据
        test_klines = []
        for i in range(100):
            test_klines.append({
                'timestamp': f'2024-01-{i+1:02d} 09:30:00',
                'open': 10.0 + i * 0.01,
                'high': 10.1 + i * 0.01,
                'low': 9.9 + i * 0.01,
                'close': 10.0 + i * 0.01,
                'volume': 1000 + i * 10
            })
        
        # 测试信号检测
        result = detector.get_signals(test_klines)
        print(f"✅ 信号检测成功: {result['status']}")
        
        # 测试初始化函数
        C = MockC()
        init(C)
        print("✅ 初始化函数执行成功")
        
        print("🎉 所有测试通过！QMT兼容版文件可以正常使用")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_qmt_compatible_file()
