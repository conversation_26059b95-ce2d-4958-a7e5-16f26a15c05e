# coding: utf-8
"""
止盈止损和下单模块简化测试
不依赖numpy和pandas，可以直接运行
"""

import datetime
import time
import random

# ============================================================================
# 模拟QMT环境
# ============================================================================

class MockQMTEnvironment:
    """模拟QMT交易环境"""
    
    def __init__(self):
        self.orders = []
        self.account_cash = 100000.0
        self.positions = {}
        self.order_id_counter = 1000
        
    def passorder(self, op_type, order_type, account, stock, price_type, price, volume, strategy, action, msg, context):
        """模拟QMT下单函数"""
        order_id = f"ORD_{self.order_id_counter}"
        self.order_id_counter += 1
        
        order = {
            'order_id': order_id,
            'op_type': op_type,
            'stock': stock,
            'price': price,
            'volume': volume,
            'status': 50,
            'submit_time': datetime.datetime.now(),
            'msg': msg
        }
        
        self.orders.append(order)
        print(f"📤 模拟下单: {msg}")
        print(f"   订单ID: {order_id}")
        
        # 模拟立即成交
        if op_type == 23:  # 买入
            cost = price * volume
            if self.account_cash >= cost:
                self.account_cash -= cost
                self.positions[stock] = self.positions.get(stock, 0) + volume
                order['status'] = 52
                print(f"✅ 模拟买入成交: {volume}股@{price:.3f}")
            else:
                order['status'] = 53
                print(f"❌ 模拟买入失败: 资金不足")
        
        elif op_type == 24:  # 卖出
            if self.positions.get(stock, 0) >= volume:
                self.account_cash += price * volume
                self.positions[stock] = self.positions.get(stock, 0) - volume
                order['status'] = 52
                print(f"✅ 模拟卖出成交: {volume}股@{price:.3f}")
            else:
                order['status'] = 53
                print(f"❌ 模拟卖出失败: 持仓不足")
        
        return order_id

# 全局模拟环境
mock_env = MockQMTEnvironment()

def passorder(op_type, order_type, account, stock, price_type, price, volume, strategy, action, msg, context):
    return mock_env.passorder(op_type, order_type, account, stock, price_type, price, volume, strategy, action, msg, context)

# ============================================================================
# 模拟策略上下文
# ============================================================================

class MockContext:
    """模拟策略上下文对象"""
    
    def __init__(self, stock='000001.SZ'):
        self.stock = stock
        self.acct = 'TEST_ACCOUNT'
        self.position = 0
        self.entry_price = 0
        self.exit_price = 0
        self.highest_price_since_entry = 0
        self.trailing_stop_price = 0
        self.use_trailing_stop = True
        self.stop_loss_pct = 0.05
        self.take_profit_pct = 0.10
        self.固定止损 = 0.5
        self.buy_hang_offset_ratio = 0.002
        self.sell_hang_offset_ratio = 0.002
        self.total_trades = 0
        self.successful_trades = 0
        self.failed_trades = 0

# ============================================================================
# 简化的止盈止损检查
# ============================================================================

def check_exit_conditions_simple(C, current_price):
    """
    简化的止盈止损检查
    """
    if C.position == 0:
        return {'should_exit': False, 'reason': '无持仓'}
    
    # 计算盈亏比例
    profit_pct = (current_price - C.entry_price) / C.entry_price
    
    # 更新最高价
    if current_price > C.highest_price_since_entry:
        C.highest_price_since_entry = current_price
    
    # 移动止盈逻辑
    if C.use_trailing_stop and C.position == 1:
        # 如果盈利超过10%，启动移动止盈
        if profit_pct >= 0.10:
            # 移动止盈线为最高价的95%
            trailing_stop = C.highest_price_since_entry * 0.95
            if current_price <= trailing_stop:
                return {'should_exit': True, 'reason': f'移动止盈触发 (保护{(trailing_stop - C.entry_price) / C.entry_price * 100:.1f}%利润)'}
    
    # 固定止损
    if profit_pct <= -0.05:
        return {'should_exit': True, 'reason': '固定止损触发 (-5%)'}
    
    # 固定止盈
    if profit_pct >= 0.15:
        return {'should_exit': True, 'reason': '固定止盈触发 (+15%)'}
    
    return {'should_exit': False, 'reason': f'继续持仓 (盈亏: {profit_pct:.2%})'}

# ============================================================================
# 简化的订单执行
# ============================================================================

def execute_buy_order_simple(C, current_price, volume=1000):
    """简化的买入订单执行"""
    try:
        print(f"\n📤 准备执行买入订单")
        
        if C.position != 0:
            return {'success': False, 'reason': '已有持仓，无法买入'}
        
        # 计算挂单价格
        hang_price = current_price * (1 - C.buy_hang_offset_ratio)
        hang_price = round(hang_price, 3)
        
        print(f"📊 买入参数:")
        print(f"   当前价格: {current_price:.3f}")
        print(f"   挂单价格: {hang_price:.3f}")
        print(f"   买入数量: {volume}股")
        
        # 执行买入
        order_msg = f"买入{C.stock} {volume}股@{hang_price:.3f}"
        order_result = passorder(23, 1101, C.acct, C.stock, 11, hang_price, volume, "测试策略", "买入开仓", order_msg, C)
        
        if order_result:
            print(f"✅ 买入订单提交成功")
            C.entry_price = hang_price
            C.position = 1
            C.highest_price_since_entry = current_price
            C.trailing_stop_price = 0
            C.total_trades += 1
            
            return {'success': True, 'order_id': order_result, 'price': hang_price, 'volume': volume}
        else:
            C.failed_trades += 1
            return {'success': False, 'reason': '订单提交失败'}
            
    except Exception as e:
        print(f"❌ 买入订单执行异常: {e}")
        C.failed_trades += 1
        return {'success': False, 'reason': f'执行异常: {e}'}

def execute_sell_order_simple(C, current_price, reason="手动卖出"):
    """简化的卖出订单执行"""
    try:
        print(f"\n📤 准备执行卖出订单")
        print(f"   卖出原因: {reason}")
        
        if C.position == 0:
            return {'success': False, 'reason': '无持仓，无法卖出'}
        
        # 计算挂单价格
        hang_price = current_price * (1 + C.sell_hang_offset_ratio)
        hang_price = round(hang_price, 3)
        
        # 假设卖出1000股
        volume = 1000
        
        print(f"📊 卖出参数:")
        print(f"   当前价格: {current_price:.3f}")
        print(f"   挂单价格: {hang_price:.3f}")
        print(f"   卖出数量: {volume}股")
        
        # 计算盈亏
        if C.entry_price > 0:
            profit_pct = (hang_price - C.entry_price) / C.entry_price * 100
            profit_amount = (hang_price - C.entry_price) * volume
            print(f"   盈亏比例: {profit_pct:.2f}%")
            print(f"   盈亏金额: {profit_amount:.2f}")
        
        # 执行卖出
        order_msg = f"卖出{C.stock} {volume}股@{hang_price:.3f} ({reason})"
        order_result = passorder(24, 1101, C.acct, C.stock, 11, hang_price, volume, "测试策略", "卖出平仓", order_msg, C)
        
        if order_result:
            print(f"✅ 卖出订单提交成功")
            C.exit_price = hang_price
            C.position = 0
            C.entry_price = 0
            C.highest_price_since_entry = 0
            C.trailing_stop_price = 0
            C.successful_trades += 1
            
            return {'success': True, 'order_id': order_result, 'price': hang_price, 'volume': volume, 'reason': reason}
        else:
            C.failed_trades += 1
            return {'success': False, 'reason': '订单提交失败'}
            
    except Exception as e:
        print(f"❌ 卖出订单执行异常: {e}")
        C.failed_trades += 1
        return {'success': False, 'reason': f'执行异常: {e}'}

# ============================================================================
# 测试函数
# ============================================================================

def test_simple_trading():
    """简单交易测试"""
    print("="*60)
    print("🧪 简化版止盈止损和下单模块测试")
    print("="*60)
    
    # 创建策略上下文
    C = MockContext('000001.SZ')
    
    # 模拟价格序列
    prices = [10.0, 10.2, 10.5, 11.0, 10.8, 10.6, 11.2, 11.5, 11.0, 10.5]
    
    print("📊 模拟交易过程:")
    
    for i, price in enumerate(prices):
        print(f"\n📅 第{i+1}天，价格: {price:.1f}")
        
        # 第3天买入
        if i == 2 and C.position == 0:
            print("🎯 触发买入信号")
            buy_result = execute_buy_order_simple(C, price, 1000)
            if buy_result['success']:
                print(f"✅ 买入成功: {buy_result['volume']}股@{buy_result['price']:.3f}")
        
        # 如果有持仓，检查止盈止损
        elif C.position == 1:
            exit_result = check_exit_conditions_simple(C, price)
            print(f"🔍 平仓检查: {exit_result['reason']}")
            
            if exit_result['should_exit']:
                print(f"🎯 触发平仓信号: {exit_result['reason']}")
                sell_result = execute_sell_order_simple(C, price, exit_result['reason'])
                if sell_result['success']:
                    profit = (sell_result['price'] - C.entry_price) * sell_result['volume']
                    profit_pct = (sell_result['price'] - C.entry_price) / C.entry_price * 100
                    print(f"✅ 卖出成功: {sell_result['volume']}股@{sell_result['price']:.3f}")
                    print(f"💰 盈亏: {profit:.2f}元 ({profit_pct:.2f}%)")
                    break
    
    print(f"\n📊 交易统计:")
    print(f"   总交易次数: {C.total_trades}")
    print(f"   成功交易: {C.successful_trades}")
    print(f"   失败交易: {C.failed_trades}")
    print(f"   账户余额: {mock_env.account_cash:.2f}")
    print(f"   持仓情况: {mock_env.positions}")

if __name__ == "__main__":
    test_simple_trading()
