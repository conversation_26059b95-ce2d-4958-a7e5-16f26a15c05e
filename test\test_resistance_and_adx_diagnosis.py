#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
诊断阻力线和ADX计算问题
"""

def test_resistance_calculation():
    """测试阻力线计算是否合理"""
    try:
        print("=== 诊断阻力线计算问题 ===")
        
        import numpy as np
        
        # 构造真实的股票数据模拟
        print("📊 测试场景1: 正常上涨行情")
        
        # 模拟正常上涨的K线数据
        highs = np.array([10.50, 10.60, 10.70, 10.80, 10.90])
        lows = np.array([10.00, 10.10, 10.20, 10.30, 10.40])
        closes = np.array([10.20, 10.30, 10.40, 10.50, 10.60])
        
        print(f"📊 K线数据:")
        for i in range(len(highs)):
            print(f"   K线{i+1}: 高{highs[i]}, 低{lows[i]}, 收{closes[i]}")
        
        # 计算阻力线
        K线加权均值 = (highs + lows + 2 * closes) / 4
        阻力线 = K线加权均值 + (K线加权均值 - lows)
        
        print(f"\n📊 阻力线计算过程:")
        for i in range(len(highs)):
            print(f"   K线{i+1}: 加权均值={K线加权均值[i]:.3f}, 阻力线={阻力线[i]:.3f}, 收盘价={closes[i]:.3f}")
            if closes[i] > 阻力线[i]:
                print(f"          ✅ 突破！收盘价{closes[i]} > 阻力线{阻力线[i]:.3f}")
            else:
                print(f"          ❌ 未突破：收盘价{closes[i]} <= 阻力线{阻力线[i]:.3f}")
        
        # 分析阻力线是否过高
        最后阻力线 = 阻力线[-1]
        最后收盘价 = closes[-1]
        阻力线溢价 = (最后阻力线 - 最后收盘价) / 最后收盘价 * 100
        
        print(f"\n📊 阻力线分析:")
        print(f"   最后收盘价: {最后收盘价}")
        print(f"   最后阻力线: {最后阻力线:.3f}")
        print(f"   阻力线溢价: {阻力线溢价:.2f}%")
        
        if 阻力线溢价 > 10:
            print("⚠️ 阻力线可能设置过高，导致难以突破")
        elif 阻力线溢价 < 1:
            print("⚠️ 阻力线可能设置过低，容易误触发")
        else:
            print("✅ 阻力线设置合理")
        
        # 测试极端情况
        print("\n📊 测试场景2: 强势突破行情")
        
        # 构造强势突破数据
        highs_strong = np.array([10.50, 10.80, 11.20, 11.50, 12.00])
        lows_strong = np.array([10.00, 10.30, 10.70, 11.00, 11.30])
        closes_strong = np.array([10.20, 10.60, 11.00, 11.30, 11.80])
        
        K线加权均值_strong = (highs_strong + lows_strong + 2 * closes_strong) / 4
        阻力线_strong = K线加权均值_strong + (K线加权均值_strong - lows_strong)
        
        print(f"📊 强势行情最后K线:")
        print(f"   收盘价: {closes_strong[-1]}")
        print(f"   阻力线: {阻力线_strong[-1]:.3f}")
        print(f"   突破情况: {'✅ 突破' if closes_strong[-1] > 阻力线_strong[-1] else '❌ 未突破'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 阻力线测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_adx_calculation():
    """测试ADX计算是否正确"""
    try:
        print("\n=== 诊断ADX计算问题 ===")
        
        import importlib.util
        import numpy as np
        import talib
        
        # 加载策略文件
        spec = importlib.util.spec_from_file_location("strategy", "框架/6sk线.py")
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        
        # 创建检测器
        detector = strategy_module.CMFBIASDivergenceDetector(ADX_N=14, ADX_M=7)
        
        print(f"📊 ADX参数: ADX_N={detector.ADX_N}, ADX_M={detector.ADX_M}")
        
        # 构造有明显趋势的数据
        print("📊 测试场景1: 明显上升趋势")
        
        # 生成明显上升趋势数据
        base_price = 10.0
        trend_strength = 0.05  # 每根K线上涨5%
        
        test_count = 30  # 足够的数据量
        highs = np.array([base_price * (1 + trend_strength * i) + 0.1 for i in range(test_count)])
        lows = np.array([base_price * (1 + trend_strength * i) - 0.1 for i in range(test_count)])
        closes = np.array([base_price * (1 + trend_strength * i) for i in range(test_count)])
        
        print(f"📊 趋势数据: {test_count}根K线")
        print(f"   起始价格: {closes[0]:.2f}")
        print(f"   结束价格: {closes[-1]:.2f}")
        print(f"   总涨幅: {(closes[-1]/closes[0]-1)*100:.1f}%")
        
        # 计算ADX
        adx_result = detector.calculate_ADX(highs, lows, closes)
        
        print(f"\n📊 ADX计算结果:")
        print(f"   ADX数组长度: {len(adx_result)}")
        print(f"   ADX范围: {adx_result.min():.2f} ~ {adx_result.max():.2f}")
        print(f"   最后5个ADX值: {adx_result[-5:]}")
        
        # 检查ADX是否全为0
        if np.all(adx_result == 0):
            print("❌ ADX全为0，计算有问题！")
            
            # 详细诊断ADX计算步骤
            print("\n🔍 详细诊断ADX计算步骤:")
            
            # 手动计算前几步
            hl = highs - lows
            print(f"   HL (HIGH-LOW): {hl[:5]}")
            
            if len(closes) > 1:
                hc = np.abs(highs[1:] - closes[:-1])
                lc = np.abs(closes[:-1] - lows[1:])
                print(f"   HC前5个: {hc[:5]}")
                print(f"   LC前5个: {lc[:5]}")
                
                # 对齐数组
                hc_aligned = np.concatenate([[hl[0]], hc])
                lc_aligned = np.concatenate([[hl[0]], lc])
                
                # True Range
                tr = np.maximum(np.maximum(hl, hc_aligned), lc_aligned)
                print(f"   TR前5个: {tr[:5]}")
                
                # MTR
                if len(tr) >= detector.ADX_N:
                    MTR = talib.SUM(tr, timeperiod=detector.ADX_N)
                    print(f"   MTR前5个: {MTR[:5]}")
                    print(f"   MTR是否有非零值: {np.any(MTR > 0)}")
                else:
                    print(f"   ❌ 数据不足计算MTR，需要{detector.ADX_N}个，实际{len(tr)}个")
            
            return False
        else:
            print("✅ ADX计算正常，有非零值")
            
            # 验证ADX的合理性
            final_adx = adx_result[-1]
            if final_adx > 25:  # 强趋势通常ADX>25
                print(f"✅ ADX值合理，显示强趋势: {final_adx:.2f}")
            else:
                print(f"⚠️ ADX值偏低，可能趋势不够强: {final_adx:.2f}")
        
        # 对比talib的ADX
        print("\n📊 对比talib ADX:")
        talib_adx = talib.ADX(highs, lows, closes, timeperiod=14)
        talib_adx_clean = np.nan_to_num(talib_adx, nan=0.0)
        
        print(f"   talib ADX最后5个: {talib_adx_clean[-5:]}")
        print(f"   自定义ADX最后5个: {adx_result[-5:]}")
        
        # 计算差异
        diff = np.abs(adx_result - talib_adx_clean)
        avg_diff = np.mean(diff[-10:])
        print(f"   平均差异: {avg_diff:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ ADX测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comprehensive_diagnosis():
    """综合诊断测试"""
    try:
        print("\n=== 综合诊断测试 ===")
        
        import importlib.util
        import numpy as np
        
        # 加载策略文件
        spec = importlib.util.spec_from_file_location("strategy", "框架/6sk线.py")
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        
        # 创建检测器
        detector = strategy_module.CMFBIASDivergenceDetector()
        
        # 构造理想的买入场景数据
        print("📊 构造理想买入场景:")
        
        test_count = 80  # 足够的数据
        
        # 构造下跌后反弹的数据（容易产生背离和超卖）
        # 前40根：下跌趋势
        # 后40根：反弹趋势
        
        prices_down = np.linspace(12.0, 10.0, 40)  # 下跌阶段
        prices_up = np.linspace(10.0, 11.5, 40)   # 反弹阶段
        all_closes = np.concatenate([prices_down, prices_up])
        
        # 构造对应的高低价
        all_highs = all_closes + 0.1 + np.random.random(test_count) * 0.05
        all_lows = all_closes - 0.1 - np.random.random(test_count) * 0.05
        all_volumes = 1000 + np.random.random(test_count) * 500
        
        print(f"   数据特征: {test_count}根K线，前半段下跌，后半段反弹")
        print(f"   价格范围: {all_closes.min():.2f} ~ {all_closes.max():.2f}")
        
        # 构造K线数据
        merged_klines = []
        for i in range(test_count):
            kline = {
                'open': all_closes[i] - 0.02,
                'high': all_highs[i],
                'low': all_lows[i],
                'close': all_closes[i],
                'volume': all_volumes[i]
            }
            merged_klines.append(kline)
        
        # 测试综合信号
        result = detector.get_comprehensive_signals(merged_klines)
        
        print(f"\n📊 综合信号检测结果:")
        print(f"   状态: {result['status']}")
        
        if result['status'] == 'success':
            indicators = result.get('indicators', {})
            conditions = result.get('conditions', {})
            
            print(f"\n📊 关键指标值:")
            print(f"   ADX: {indicators.get('ADX', 0):.2f}")
            print(f"   CMF: {indicators.get('CMF', 0):.4f}")
            print(f"   BIAS: {indicators.get('BIAS', 0):.2f}%")
            print(f"   SKDJ_K: {indicators.get('K', 0):.2f}")
            print(f"   SKDJ_D: {indicators.get('D', 0):.2f}")
            
            print(f"\n📊 条件检查:")
            print(f"   SKDJ超卖: {conditions.get('SKDJ超卖', False)}")
            print(f"   双重背离: {conditions.get('双重背离', False)}")
            print(f"   强趋势确认: {conditions.get('强趋势确认', False)} (ADX>40)")
            print(f"   突破确认: {conditions.get('突破确认', False)}")
            
            # 分析问题
            adx_value = indicators.get('ADX', 0)
            if adx_value == 0:
                print("❌ 问题确认：ADX为0，计算有误")
            elif adx_value < 40:
                print(f"⚠️ ADX值{adx_value:.2f}小于40，强趋势条件不满足")
            
            if not conditions.get('突破确认', False):
                print("⚠️ 阻力线未突破，可能阻力线设置过高")
                
                # 手动检查阻力线
                last_kline = merged_klines[-1]
                close_price = last_kline['close']
                high_price = last_kline['high']
                low_price = last_kline['low']
                
                K线加权均值 = (high_price + low_price + 2 * close_price) / 4
                阻力线 = K线加权均值 + (K线加权均值 - low_price)
                
                print(f"   最后K线: 高{high_price:.2f}, 低{low_price:.2f}, 收{close_price:.2f}")
                print(f"   加权均值: {K线加权均值:.3f}")
                print(f"   阻力线: {阻力线:.3f}")
                print(f"   突破差距: {阻力线 - close_price:.3f} ({(阻力线/close_price-1)*100:.2f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 综合诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 开始阻力线和ADX问题诊断...")
    
    success_count = 0
    total_tests = 3
    
    if test_resistance_calculation():
        success_count += 1
    
    if test_adx_calculation():
        success_count += 1
    
    if test_comprehensive_diagnosis():
        success_count += 1
    
    print(f"\n📊 诊断结果: {success_count}/{total_tests} 完成")
    
    if success_count == total_tests:
        print("\n📋 诊断总结:")
        print("1. 🔍 阻力线计算逻辑已检查")
        print("2. 🔍 ADX计算步骤已验证")
        print("3. 🔍 综合信号流程已测试")
        print("4. 📊 问题原因已定位")
    else:
        print("❌ 部分诊断失败，需要进一步分析")
