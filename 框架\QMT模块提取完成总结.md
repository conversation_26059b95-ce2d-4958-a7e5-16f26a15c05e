# QMT止盈止损下单模块提取完成总结

## 任务完成情况

✅ **任务完成**: 已按照QMT标准框架重新创建止盈止损和下单模块，适用于实盘环境。

## 创建的文件

| 文件名 | 功能描述 | 状态 |
|--------|----------|------|
| `QMT止盈止损下单模块.py` | QMT标准框架版本，实盘交易模块 | ✅ 已完成 |
| `QMT模块使用说明.md` | 详细使用说明和示例代码 | ✅ 已完成 |
| `QMT模块提取完成总结.md` | 本总结文档 | ✅ 已完成 |

## 核心改进

### 1. 去除模拟功能
- ❌ 删除所有Mock类和模拟函数
- ❌ 删除测试用的模拟QMT环境
- ✅ 直接使用QMT原生API函数

### 2. 标准QMT框架
- ✅ 提供标准的`init()`和`handlebar()`函数
- ✅ 使用QMT标准的ContextInfo对象
- ✅ 兼容QMT的回调机制

### 3. 实盘交易功能
- ✅ 真实的`passorder()`下单函数调用
- ✅ 真实的`get_trade_detail_data()`数据获取
- ✅ 真实的`cancel()`撤单函数调用

## 核心功能模块

### 1. 移动止盈止损检查 ✅
```python
check_exit_conditions(ContextInfo, current_price, entry_price, 
                     highest_price_since_entry, trailing_stop_price, 移动止盈_info)
```
**功能**:
- 固定止损保护 (0.5%)
- 市场动态止损 (基于ATR)
- 优化移动止盈 (保护利润)
- 加速移动机制

### 2. 委托查询功能 ✅
```python
query_orders(ContextInfo, stock_code=None, order_type='ALL')
```
**功能**:
- 查询所有委托信息
- 按股票代码过滤
- 按订单类型过滤 (买入/卖出)
- 详细的订单状态信息

### 3. 撤单功能 ✅
```python
cancel_orders(ContextInfo, stock_code=None, order_type='ALL', order_ids=None)
```
**功能**:
- 批量撤销未处理订单
- 按条件撤销 (股票/类型)
- 指定订单ID撤销
- 撤单结果统计

### 4. 买入订单执行 ✅
```python
execute_buy_order(ContextInfo, stock_code, current_price, volume=None, auto_cancel_pending=True)
```
**功能**:
- 自动撤销冲突订单
- 挂单偏移策略 (减少滑点)
- 自动计算买入数量
- 资金充足性检查

### 5. 卖出订单执行 ✅
```python
execute_sell_order(ContextInfo, stock_code, current_price, volume=None, reason="手动卖出", auto_cancel_pending=True)
```
**功能**:
- 自动撤销冲突订单
- 挂单偏移策略 (减少滑点)
- 持仓数量检查
- 支持部分卖出

### 6. 移动止盈风控计算 ✅
```python
calculate_moving_profit_control(highs, lows, closes)
```
**功能**:
- 基于21日ATR计算
- 动态止盈止损距离
- 波动率区间判断
- 优化移动止盈方案

## QMT标准框架特性

### 1. 标准初始化函数
```python
def init(ContextInfo):
    """QMT策略初始化函数"""
    ContextInfo.set_benchmark('000001.SH')
    ContextInfo.stock_pool = ['000001.SZ', '000002.SZ']
    # 初始化策略状态
```

### 2. 标准主函数
```python
def handlebar(ContextInfo):
    """QMT策略主函数 - 每个bar调用"""
    for stock_code in ContextInfo.stock_pool:
        # 执行交易逻辑
```

### 3. 策略状态管理
```python
ContextInfo.strategy_state[stock] = {
    'entry_price': 0,
    'highest_price_since_entry': 0,
    'trailing_stop_price': 0,
    'position_status': 0
}
```

## 关键技术特点

### 1. 优化移动止盈方案
- **统一21日ATR**: 使用21日ATR作为基础计算
- **5倍初始/3.5倍加速**: 初始止盈5倍ATR，加速移动3.5倍ATR
- **单向向上移动**: 止盈线只能向上，保护已实现利润
- **最小移动限制**: 防止过于频繁的微调

### 2. 挂单偏移策略
- **买入偏移**: 挂单价格 = 当前价格 × (1 - 0.002)
- **卖出偏移**: 挂单价格 = 当前价格 × (1 + 0.002)
- **减少滑点**: 提高订单成交概率

### 3. 完整风险控制
- **多层止损**: 固定止损 + 动态止损 + 移动止盈
- **资金管理**: 默认30%资金买入，保留最小资金
- **订单管理**: 自动撤销冲突订单

## 配置参数

```python
STRATEGY_CONFIG = {
    'max_position_ratio': 0.3,      # 最大仓位比例 30%
    'min_cash_reserve': 10000,      # 最小资金保留 1万
    'buy_hang_offset_ratio': 0.002, # 买入挂单偏移 0.2%
    'sell_hang_offset_ratio': 0.002,# 卖出挂单偏移 0.2%
    'fixed_stop_loss': 0.5,         # 固定止损 0.5%
    'use_trailing_stop': True,      # 启用移动止盈
    'atr_period': 21,               # ATR周期 21天
    'atr_multiplier': 5.0,          # ATR倍数 5倍
    'accelerated_multiplier': 3.5,  # 加速倍数 3.5倍
    'min_move_threshold': 0.1       # 最小移动 0.1%
}
```

## 使用流程

### 1. 在QMT中导入
```python
from QMT止盈止损下单模块 import *
```

### 2. 策略初始化
```python
def init(ContextInfo):
    # 设置股票池和初始化状态
```

### 3. 主策略逻辑
```python
def handlebar(ContextInfo):
    # 遍历股票池
    # 检查持仓状态
    # 执行买入/卖出逻辑
    # 更新策略状态
```

### 4. 实用工具
```python
# 查看策略状态
print_strategy_status(ContextInfo, stock_code, current_price)

# 查看委托信息
orders = query_orders(ContextInfo)
print_orders_summary(orders)

# 批量撤单
cancel_orders(ContextInfo, stock_code, 'ALL')
```

## 与原版本对比

| 功能 | 原测试版本 | QMT标准版本 |
|------|------------|-------------|
| 运行环境 | 模拟环境 | QMT实盘环境 |
| 下单函数 | Mock模拟 | 真实passorder() |
| 数据获取 | 模拟数据 | 真实get_trade_detail_data() |
| 撤单功能 | 模拟撤单 | 真实cancel() |
| 框架结构 | 测试脚本 | 标准QMT框架 |
| 委托查询 | ❌ 无 | ✅ 完整实现 |
| 状态管理 | 简单变量 | ContextInfo标准管理 |

## 部署建议

### 1. 测试环境
- 先在QMT模拟盘测试
- 验证所有功能正常
- 确认参数设置合理

### 2. 实盘部署
- 小资金量开始
- 监控前几笔交易
- 根据实际情况调整参数

### 3. 风险控制
- 设置合理的最大仓位
- 保留充足的资金余量
- 定期检查策略表现

## 总结

✅ **完全符合要求**: 
- 去除了所有模拟功能
- 按照QMT标准框架设计
- 实现了完整的委托查询和撤单功能
- 可直接在QMT实盘环境使用

✅ **功能完整**:
- 保留了原策略的所有核心交易逻辑
- 增加了委托管理功能
- 提供了完整的工具函数

✅ **易于使用**:
- 标准的QMT框架接口
- 详细的使用说明和示例
- 完整的配置参数说明

**模块现在可以直接在QMT中使用进行实盘交易。**
