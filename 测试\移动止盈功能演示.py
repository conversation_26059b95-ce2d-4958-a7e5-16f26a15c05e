#coding:gbk

"""
移动止盈功能演示脚本
展示在不同市场波动环境下移动止盈的工作原理
"""

import numpy as np

def simulate_trailing_stop(entry_price, price_series, volatility_zone='正常波动区', base_trailing_ratio=0.02):
    """
    模拟移动止盈功能
    
    参数:
        entry_price: 入场价格
        price_series: 价格序列
        volatility_zone: 波动率区间
        base_trailing_ratio: 基础回撤比例
    
    返回:
        dict: 模拟结果
    """
    # 根据波动率区间调整回撤比例
    if volatility_zone == '低波动区':
        dynamic_trailing_ratio = base_trailing_ratio * 0.5  # 1%回撤
    elif volatility_zone == '正常波动区':
        dynamic_trailing_ratio = base_trailing_ratio  # 2%回撤
    elif volatility_zone == '高波动区':
        dynamic_trailing_ratio = base_trailing_ratio * 1.5  # 3%回撤
    else:  # 极高波动区
        dynamic_trailing_ratio = base_trailing_ratio * 2.0  # 4%回撤
    
    highest_price_since_entry = entry_price
    trailing_stop_price = 0
    exit_price = None
    exit_reason = ""
    
    results = []
    
    for i, current_price in enumerate(price_series):
        # 更新入场后最高价格
        if current_price > highest_price_since_entry:
            highest_price_since_entry = current_price
            
        # 计算移动止盈线价格
        new_trailing_stop_price = highest_price_since_entry * (1 - dynamic_trailing_ratio)
        
        # 移动止盈线只能向上移动（朝有利方向）
        if new_trailing_stop_price > trailing_stop_price:
            trailing_stop_price = new_trailing_stop_price
            
        # 检查是否触发移动止盈
        trailing_stop_triggered = trailing_stop_price > 0 and current_price <= trailing_stop_price
        
        # 计算当前盈亏
        profit_pct = (current_price - entry_price) / entry_price * 100
        
        results.append({
            'step': i + 1,
            'price': current_price,
            'highest_price': highest_price_since_entry,
            'trailing_stop': trailing_stop_price,
            'profit_pct': profit_pct,
            'triggered': trailing_stop_triggered
        })
        
        # 如果触发移动止盈，退出
        if trailing_stop_triggered:
            exit_price = current_price
            exit_reason = f"移动止盈触发({volatility_zone},{dynamic_trailing_ratio:.1%}回撤)"
            break
    
    return {
        'volatility_zone': volatility_zone,
        'dynamic_trailing_ratio': dynamic_trailing_ratio,
        'entry_price': entry_price,
        'exit_price': exit_price,
        'exit_reason': exit_reason,
        'final_profit_pct': (exit_price - entry_price) / entry_price * 100 if exit_price else (price_series[-1] - entry_price) / entry_price * 100,
        'max_profit_pct': (highest_price_since_entry - entry_price) / entry_price * 100,
        'results': results
    }

def demo_trailing_stop_scenarios():
    """演示不同场景下的移动止盈"""
    print("="*80)
    print("🎯 移动止盈功能演示")
    print("="*80)
    
    # 场景1：低波动市场 - 价格缓慢上涨后小幅回调
    print("\n📊 场景1：低波动市场 - 价格缓慢上涨后小幅回调")
    entry_price = 10.0
    price_series_low = [10.0, 10.05, 10.08, 10.12, 10.15, 10.18, 10.16, 10.14, 10.12, 10.10]
    
    result_low = simulate_trailing_stop(entry_price, price_series_low, '低波动区')
    print_simulation_result(result_low)
    
    # 场景2：正常波动市场 - 价格上涨后正常回调
    print("\n📊 场景2：正常波动市场 - 价格上涨后正常回调")
    price_series_normal = [10.0, 10.15, 10.25, 10.35, 10.45, 10.50, 10.40, 10.30, 10.20, 10.10]
    
    result_normal = simulate_trailing_stop(entry_price, price_series_normal, '正常波动区')
    print_simulation_result(result_normal)
    
    # 场景3：高波动市场 - 价格大幅上涨后较大回调
    print("\n📊 场景3：高波动市场 - 价格大幅上涨后较大回调")
    price_series_high = [10.0, 10.30, 10.60, 10.80, 11.00, 11.20, 10.90, 10.60, 10.30, 10.00]
    
    result_high = simulate_trailing_stop(entry_price, price_series_high, '高波动区')
    print_simulation_result(result_high)
    
    # 场景4：极高波动市场 - 价格剧烈波动
    print("\n📊 场景4：极高波动市场 - 价格剧烈波动")
    price_series_extreme = [10.0, 10.50, 11.00, 11.50, 12.00, 12.50, 12.00, 11.50, 11.00, 10.50]
    
    result_extreme = simulate_trailing_stop(entry_price, price_series_extreme, '极高波动区')
    print_simulation_result(result_extreme)

def print_simulation_result(result):
    """打印模拟结果"""
    print(f"   波动率区间: {result['volatility_zone']}")
    print(f"   动态回撤比例: {result['dynamic_trailing_ratio']:.1%}")
    print(f"   入场价格: {result['entry_price']:.2f}")
    print(f"   最高价格: {max([r['highest_price'] for r in result['results']]):.2f}")
    
    if result['exit_price']:
        print(f"   退出价格: {result['exit_price']:.2f}")
        print(f"   退出原因: {result['exit_reason']}")
        print(f"   实际收益: {result['final_profit_pct']:.2f}%")
        print(f"   最大收益: {result['max_profit_pct']:.2f}%")
        print(f"   收益保护: {result['final_profit_pct']/result['max_profit_pct']*100:.1f}%")
    else:
        print(f"   未触发退出")
        print(f"   当前收益: {result['final_profit_pct']:.2f}%")
        print(f"   最大收益: {result['max_profit_pct']:.2f}%")
    
    # 显示详细过程（前5步）
    print(f"   详细过程（前5步）:")
    for i, step in enumerate(result['results'][:5]):
        status = "🔴触发" if step['triggered'] else "🟢持仓"
        print(f"     步骤{step['step']}: 价格{step['price']:.2f}, 止盈线{step['trailing_stop']:.2f}, 收益{step['profit_pct']:.1f}%, {status}")

def compare_with_fixed_stop():
    """与固定止盈对比"""
    print("\n" + "="*80)
    print("📋 移动止盈 vs 固定止盈对比")
    print("="*80)
    
    entry_price = 10.0
    # 价格先涨到12元（+20%），然后回调到10.8元（+8%）
    price_series = [10.0, 10.5, 11.0, 11.5, 12.0, 11.8, 11.6, 11.4, 11.2, 11.0, 10.8]
    
    # 固定止盈：15%
    fixed_take_profit = 0.15
    fixed_exit_price = None
    for price in price_series:
        if (price - entry_price) / entry_price >= fixed_take_profit:
            fixed_exit_price = price
            break
    
    # 移动止盈
    trailing_result = simulate_trailing_stop(entry_price, price_series, '正常波动区')
    
    print(f"价格走势: {entry_price:.1f} → 12.0 → 10.8")
    print(f"最大涨幅: +20%")
    print()
    print(f"固定止盈策略 (15%):")
    if fixed_exit_price:
        fixed_profit = (fixed_exit_price - entry_price) / entry_price * 100
        print(f"   退出价格: {fixed_exit_price:.2f}")
        print(f"   实际收益: {fixed_profit:.1f}%")
    else:
        print(f"   未触发退出")
        print(f"   最终收益: {(price_series[-1] - entry_price) / entry_price * 100:.1f}%")
    
    print(f"\n移动止盈策略 (2%回撤):")
    if trailing_result['exit_price']:
        print(f"   退出价格: {trailing_result['exit_price']:.2f}")
        print(f"   实际收益: {trailing_result['final_profit_pct']:.1f}%")
        print(f"   收益保护: {trailing_result['final_profit_pct']/trailing_result['max_profit_pct']*100:.1f}%")
    else:
        print(f"   未触发退出")
        print(f"   最终收益: {trailing_result['final_profit_pct']:.1f}%")
    
    print(f"\n💡 移动止盈优势:")
    print(f"   ✅ 能够跟随价格上涨，不会过早退出")
    print(f"   ✅ 在价格回调时及时保护利润")
    print(f"   ✅ 根据市场波动动态调整回撤比例")
    print(f"   ✅ 最大化趋势收益，同时控制回撤风险")

if __name__ == "__main__":
    demo_trailing_stop_scenarios()
    compare_with_fixed_stop()
    
    print("\n" + "="*80)
    print("✅ 移动止盈功能演示完成")
    print("💡 建议：根据不同标的的波动特征调整基础回撤比例")
    print("🎯 使用方法：在策略中调用 set_trailing_stop_config(C, enable=True, ratio=0.02)")
    print("="*80)
