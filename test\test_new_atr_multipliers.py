#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新ATR倍数测试程序
验证基础使用21的1/3计算（7倍），加速时适当缩小倍数（4.9倍）
"""

import numpy as np

def simple_atr_calculation(highs, lows, closes, period=21):
    """简化的ATR计算"""
    if len(closes) < period + 1:
        return 0.05
    
    # 计算True Range
    tr_list = []
    for i in range(1, len(closes)):
        high_low = highs[i] - lows[i]
        high_close = abs(highs[i] - closes[i-1])
        low_close = abs(lows[i] - closes[i-1])
        tr = max(high_low, high_close, low_close)
        tr_list.append(tr)
    
    # 计算ATR (简单移动平均)
    if len(tr_list) >= period:
        atr = np.mean(tr_list[-period:])
        return atr
    else:
        return np.mean(tr_list) if tr_list else 0.05

def test_new_atr_multipliers():
    """测试新的ATR倍数设置"""
    print("=" * 80)
    print("🎯 新ATR倍数设置测试")
    print("=" * 80)
    
    # 创建不同波动率的测试数据
    test_scenarios = [
        {
            "name": "低波动股票",
            "volatility": 0.008,  # 0.8%日波动
            "trend": 0.002,       # 0.2%日趋势
            "days": 30
        },
        {
            "name": "中等波动股票", 
            "volatility": 0.015,  # 1.5%日波动
            "trend": 0.003,       # 0.3%日趋势
            "days": 30
        },
        {
            "name": "高波动股票",
            "volatility": 0.025,  # 2.5%日波动
            "trend": 0.005,       # 0.5%日趋势
            "days": 30
        },
        {
            "name": "极高波动股票",
            "volatility": 0.035,  # 3.5%日波动
            "trend": 0.008,       # 0.8%日趋势
            "days": 30
        }
    ]
    
    for scenario in test_scenarios:
        print(f"🔍 测试场景: {scenario['name']}")
        print("-" * 60)
        
        # 生成测试数据
        np.random.seed(42)  # 固定随机种子确保可重复
        base_price = 100.0
        days = scenario['days']
        volatility = scenario['volatility']
        trend = scenario['trend']
        
        # 生成价格序列
        trend_factor = np.linspace(0, trend * days, days)
        noise = np.random.normal(0, volatility, days)
        closes = base_price * (1 + trend_factor + noise)
        highs = closes * (1 + np.random.uniform(0.002, 0.008, days))
        lows = closes * (1 - np.random.uniform(0.002, 0.008, days))
        
        print(f"   📊 数据特征:")
        print(f"     起始价格: {closes[0]:.3f}")
        print(f"     结束价格: {closes[-1]:.3f}")
        print(f"     总变化: {(closes[-1] / closes[0] - 1) * 100:.2f}%")
        print(f"     设定波动率: {volatility * 100:.1f}%")
        print()
        
        # 新的ATR倍数设置
        ATR_PERIOD = 21
        base_multiplier = 21.0 / 3.0  # 21的1/3 = 7.0倍
        加速倍数 = base_multiplier * 0.7  # 加速时缩小到70%，即7×0.7=4.9倍
        最小移动幅度 = 0.1
        
        print(f"   🎯 新ATR倍数设置:")
        print(f"     ATR周期: {ATR_PERIOD}日")
        print(f"     基础倍数: {base_multiplier:.1f}倍 (21的1/3)")
        print(f"     加速倍数: {加速倍数:.1f}倍 (基础×0.7)")
        print(f"     最小移动: {最小移动幅度:.1f}%")
        
        # 计算ATR
        current_atr = simple_atr_calculation(highs, lows, closes, ATR_PERIOD)
        current_price = closes[-1]
        atr_percentage = (current_atr / current_price * 100) if current_price > 0 else 0.05
        
        print(f"     ATR值: {current_atr:.4f}")
        print(f"     ATR百分比: {atr_percentage:.3f}%")
        
        # 计算移动止盈参数
        止损距离 = atr_percentage * base_multiplier
        初始止盈距离 = atr_percentage * base_multiplier
        标准移动触发距离 = 初始止盈距离
        加速移动触发距离 = atr_percentage * 加速倍数
        
        # 合理性检查
        最小距离 = 0.15
        最大距离 = 8.0  # 提高最大限制以适应7倍数
        
        止损距离 = max(min(止损距离, 最大距离), 最小距离)
        初始止盈距离 = max(min(初始止盈距离, 最大距离), 最小距离)
        标准移动触发距离 = 初始止盈距离
        加速移动触发距离 = max(min(加速移动触发距离, 最大距离), 最小移动幅度)
        
        print(f"     止损距离: {止损距离:.3f}%")
        print(f"     初始止盈距离: {初始止盈距离:.3f}%")
        print(f"     标准移动触发: {标准移动触发距离:.3f}%")
        print(f"     加速移动触发: {加速移动触发距离:.3f}%")
        
        # 波动率区间判断
        if atr_percentage < 0.3:
            波动率区间 = '低波动区'
        elif atr_percentage < 0.8:
            波动率区间 = '正常波动区'
        elif atr_percentage < 1.5:
            波动率区间 = '高波动区'
        else:
            波动率区间 = '极高波动区'
        
        print(f"     波动率区间: {波动率区间}")
        
        # 计算加速移动的优势
        标准加速比 = 标准移动触发距离 / 加速移动触发距离 if 加速移动触发距离 > 0 else 1
        print(f"     加速优势: 移动频率提高{标准加速比:.1f}倍")
        print()

def test_multiplier_comparison():
    """对比不同倍数设置的效果"""
    print("=" * 80)
    print("🔧 ATR倍数对比测试")
    print("=" * 80)
    
    # 创建标准测试数据
    np.random.seed(123)
    base_price = 100.0
    days = 40
    
    # 生成价格序列
    trend_factor = np.linspace(0, 0.12, days)  # 12%上涨
    noise = np.random.normal(0, 0.018, days)   # 1.8%波动
    closes = base_price * (1 + trend_factor + noise)
    highs = closes * (1 + np.random.uniform(0.003, 0.010, days))
    lows = closes * (1 - np.random.uniform(0.003, 0.010, days))
    
    print(f"📊 标准测试数据:")
    print(f"   数据长度: {len(closes)}天")
    print(f"   起始价格: {closes[0]:.3f}")
    print(f"   结束价格: {closes[-1]:.3f}")
    print(f"   总涨幅: {(closes[-1] / closes[0] - 1) * 100:.2f}%")
    print()
    
    # 计算21日ATR
    atr_21 = simple_atr_calculation(highs, lows, closes, 21)
    atr_pct = (atr_21 / closes[-1] * 100)
    
    print(f"🔍 不同倍数方案对比:")
    print(f"   21日ATR: {atr_21:.4f} ({atr_pct:.3f}%)")
    print()
    
    # 对比不同倍数方案
    multiplier_schemes = [
        {"name": "原2倍方案", "base": 2.0, "accel": 1.5, "status": "❌ 已弃用"},
        {"name": "原4.67倍方案", "base": 4.67, "accel": 3.5, "status": "❌ 已弃用"},
        {"name": "新7倍方案", "base": 7.0, "accel": 4.9, "status": "✅ 当前使用"},
        {"name": "参考10倍方案", "base": 10.0, "accel": 7.0, "status": "⚪ 参考"},
    ]
    
    for scheme in multiplier_schemes:
        base_mult = scheme["base"]
        accel_mult = scheme["accel"]
        
        # 计算对应的移动止盈参数
        stop_loss = atr_pct * base_mult
        take_profit = atr_pct * base_mult
        standard_trigger = take_profit
        accel_trigger = atr_pct * accel_mult
        
        # 应用合理性限制
        max_limit = 8.0
        min_limit = 0.15
        
        stop_loss = max(min(stop_loss, max_limit), min_limit)
        take_profit = max(min(take_profit, max_limit), min_limit)
        standard_trigger = take_profit
        accel_trigger = max(min(accel_trigger, max_limit), 0.1)
        
        print(f"   {scheme['name']}: {scheme['status']}")
        print(f"     基础倍数: {base_mult:.1f} | 加速倍数: {accel_mult:.1f}")
        print(f"     止损: {stop_loss:.2f}% | 止盈: {take_profit:.2f}%")
        print(f"     标准触发: {standard_trigger:.2f}% | 加速触发: {accel_trigger:.2f}%")
        
        if accel_trigger > 0:
            accel_advantage = standard_trigger / accel_trigger
            print(f"     加速优势: {accel_advantage:.1f}倍移动频率")
        print()

def test_acceleration_mechanism():
    """测试加速移动机制"""
    print("=" * 80)
    print("🚀 加速移动机制测试")
    print("=" * 80)
    
    # 模拟交易场景
    entry_price = 100.0
    atr_percentage = 1.5  # 1.5% ATR
    
    # 新的倍数设置
    base_multiplier = 7.0
    accel_multiplier = 4.9
    
    初始止盈距离 = atr_percentage * base_multiplier
    标准移动触发距离 = 初始止盈距离
    加速移动触发距离 = atr_percentage * accel_multiplier
    最小移动幅度 = 0.1
    
    print(f"📊 测试参数:")
    print(f"   入场价格: {entry_price:.2f}")
    print(f"   ATR百分比: {atr_percentage:.1f}%")
    print(f"   基础倍数: {base_multiplier:.1f}倍")
    print(f"   加速倍数: {accel_multiplier:.1f}倍")
    print(f"   初始止盈距离: {初始止盈距离:.2f}%")
    print(f"   标准移动触发: {标准移动触发距离:.2f}%")
    print(f"   加速移动触发: {加速移动触发距离:.2f}%")
    print()
    
    # 测试不同价格水平的移动机制
    初始止盈价格 = entry_price * (1 + 初始止盈距离 / 100)
    
    test_prices = [
        初始止盈价格 * 1.02,   # 刚超过初始止盈
        初始止盈价格 * 1.05,   # 小幅超过
        初始止盈价格 * 1.10,   # 中等超过
        初始止盈价格 * 1.20,   # 大幅超过，应该触发加速
    ]
    
    print(f"🔍 不同价格水平的移动机制:")
    print(f"   初始止盈价格: {初始止盈价格:.3f}")
    print()
    
    for i, test_price in enumerate(test_prices):
        print(f"   测试价格 {i+1}: {test_price:.3f}")
        
        if test_price >= 初始止盈价格:
            price_gain_from_initial = test_price - 初始止盈价格
            三个移动间距 = 3 * (标准移动触发距离 / 100 * entry_price)
            is_accelerated = price_gain_from_initial >= 三个移动间距
            
            当前移动触发距离 = 加速移动触发距离 if is_accelerated else 标准移动触发距离
            移动模式 = "加速移动" if is_accelerated else "标准移动"
            
            if 当前移动触发距离 < 最小移动幅度:
                当前移动触发距离 = 最小移动幅度
                移动模式 += "(最小限制)"
            
            移动触发单位 = 当前移动触发距离 / 100 * entry_price
            
            if 移动触发单位 > 0:
                move_times = int(price_gain_from_initial / 移动触发单位)
                initial_stop_price = entry_price + (初始止盈距离 / 100 * entry_price)
                new_trailing_stop_price = initial_stop_price + (move_times * 移动触发单位)
                
                print(f"     价格增幅: {price_gain_from_initial:.3f}")
                print(f"     三个间距: {三个移动间距:.3f}")
                print(f"     移动模式: {移动模式}")
                print(f"     触发距离: {当前移动触发距离:.2f}%")
                print(f"     移动次数: {move_times}")
                print(f"     新止盈线: {new_trailing_stop_price:.3f}")
                
                if is_accelerated:
                    standard_moves = int(price_gain_from_initial / (标准移动触发距离 / 100 * entry_price))
                    print(f"     加速优势: 标准模式仅移动{standard_moves}次，加速模式移动{move_times}次")
            else:
                print(f"     移动触发单位计算错误")
        else:
            print(f"     未达到初始止盈价格")
        print()

if __name__ == "__main__":
    try:
        test_new_atr_multipliers()
        test_multiplier_comparison()
        test_acceleration_mechanism()
        
        print("=" * 80)
        print("✅ 新ATR倍数设置测试完成")
        print("=" * 80)
        print()
        print("📋 新倍数方案总结:")
        print("1. ✅ 基础倍数: 21的1/3 = 7.0倍")
        print("2. ✅ 加速倍数: 基础×0.7 = 4.9倍")
        print("3. ✅ 加速优势: 移动频率提高约1.4倍")
        print("4. ✅ 适应性强: 适合不同波动率的股票")
        print("5. ✅ 风险控制: 保持风险收益对称设计")
        print("6. ✅ 最小限制: 0.1%最小移动幅度")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
