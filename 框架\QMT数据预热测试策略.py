# -*- coding: utf-8 -*-
"""
QMT数据预热测试策略
专门用于测试历史数据获取和ATR计算的QMT策略

功能:
1. 测试历史K线数据获取能力
2. 验证ATR计算所需的数据充足性
3. 检查数据质量和完整性
4. 为主策略运行提供数据预热验证

使用方法:
1. 在QMT中加载此策略文件
2. 设置要测试的股票代码
3. 运行策略查看数据预热测试结果

作者: QMT策略开发
版本: 1.0.0
日期: 2025-07-31
"""

import numpy as np
from datetime import datetime

# ============================================================================
# QMT策略标准接口
# ============================================================================

def init(ContextInfo):
    """
    策略初始化函数 - 执行数据预热测试
    """
    print("🚀 QMT数据预热测试策略启动")
    print("="*60)
    
    # 获取股票代码
    stock_code = getattr(ContextInfo, 'stock', '123171.SZ')
    print(f"📊 测试股票: {stock_code}")
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行数据预热测试
    print(f"\n🔍 开始数据预热测试...")
    
    # 测试1: 历史数据获取测试
    print(f"\n{'='*50}")
    print(f"📋 测试1: 历史数据获取")
    test1_result = test_historical_data(ContextInfo)
    
    # 测试2: ATR计算测试
    print(f"\n{'='*50}")
    print(f"📋 测试2: ATR计算验证")
    test2_result = test_atr_calculation(ContextInfo)
    
    # 测试3: 实时数据测试
    print(f"\n{'='*50}")
    print(f"📋 测试3: 实时数据获取")
    test3_result = test_realtime_data(ContextInfo)
    
    # 测试总结
    print(f"\n{'='*60}")
    print(f"📋 数据预热测试总结")
    print(f"{'='*60}")
    
    success_count = sum([test1_result, test2_result, test3_result])
    
    print(f"✅ 历史数据获取: {'通过' if test1_result else '失败'}")
    print(f"✅ ATR计算验证: {'通过' if test2_result else '失败'}")
    print(f"✅ 实时数据获取: {'通过' if test3_result else '失败'}")
    print(f"\n🎯 总体结果: {success_count}/3 项测试通过")
    
    if success_count == 3:
        print(f"🎉 数据预热测试全部通过！")
        print(f"💡 建议: 数据环境良好，可以开始使用主策略")
    elif success_count >= 2:
        print(f"⚠️ 大部分测试通过，数据基本可用")
        print(f"💡 建议: 可以谨慎使用主策略，注意监控")
    else:
        print(f"❌ 数据预热测试未通过")
        print(f"💡 建议: 检查网络连接、股票代码或等待更多数据")
    
    print("="*60)

def handlebar(ContextInfo):
    """
    K线数据处理函数 - 持续监控数据质量
    """
    # 每10根K线进行一次数据质量检查
    bar_count = getattr(ContextInfo, 'bar_count', 0) + 1
    ContextInfo.bar_count = bar_count
    
    if bar_count % 10 == 0:
        print(f"\n🔍 第{bar_count}根K线 - 数据质量检查")
        quick_quality_check(ContextInfo)

# ============================================================================
# 数据测试函数
# ============================================================================

def test_historical_data(ContextInfo):
    """
    测试历史数据获取
    """
    try:
        stock_code = getattr(ContextInfo, 'stock', '123171.SZ')
        
        # 尝试获取30天历史数据
        hist_data = get_market_data_ex(
            stock_list=[stock_code],
            period='1d',
            count=30,
            dividend_type='none'
        )
        
        if hist_data and stock_code in hist_data:
            data_list = hist_data[stock_code]
            data_count = len(data_list)
            
            print(f"✅ 历史数据获取成功: {data_count}条")
            
            if data_count >= 15:
                print(f"🎯 数据量充足 (≥15条)，满足ATR计算需求")
                
                # 检查数据质量
                valid_count = 0
                for item in data_list[-10:]:  # 检查最近10条
                    price = float(item.get('close', 0))
                    volume = float(item.get('volume', 0))
                    if price > 0 and volume > 0:
                        valid_count += 1
                
                print(f"📊 数据质量: 最近10条中{valid_count}条有效")
                
                if valid_count >= 8:
                    print(f"✅ 数据质量良好")
                    return True
                else:
                    print(f"⚠️ 数据质量需要关注")
                    return False
            else:
                print(f"⚠️ 数据量不足: {data_count}/15")
                return False
        else:
            print(f"❌ 历史数据获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 历史数据测试异常: {e}")
        return False

def test_atr_calculation(ContextInfo):
    """
    测试ATR计算
    """
    try:
        stock_code = getattr(ContextInfo, 'stock', '123171.SZ')
        
        # 获取用于ATR计算的数据
        hist_data = get_market_data_ex(
            stock_list=[stock_code],
            period='1d',
            count=20,
            dividend_type='none'
        )
        
        if hist_data and stock_code in hist_data:
            data_list = hist_data[stock_code]
            
            if len(data_list) >= 15:
                # 提取OHLC数据
                closes = [float(item.get('close', 0)) for item in data_list]
                highs = [float(item.get('high', 0)) for item in data_list]
                lows = [float(item.get('low', 0)) for item in data_list]
                
                print(f"📊 提取OHLC数据: {len(closes)}条")
                
                # 计算ATR
                tr_list = []
                for i in range(1, len(highs)):
                    tr1 = highs[i] - lows[i]
                    tr2 = abs(highs[i] - closes[i-1])
                    tr3 = abs(lows[i] - closes[i-1])
                    tr = max(tr1, tr2, tr3)
                    tr_list.append(tr)
                
                if len(tr_list) >= 14:
                    atr = np.mean(tr_list[-14:])
                    atr_percentage = (atr / closes[-1]) * 100 if closes[-1] > 0 else 0
                    
                    print(f"✅ ATR计算成功: {atr:.4f}")
                    print(f"📊 ATR百分比: {atr_percentage:.2f}%")
                    print(f"🎯 数据充足，ATR计算可靠")
                    
                    if atr > 0:
                        # 计算基于ATR的阈值
                        trigger_threshold = atr * 4.0  # 使用您更新的配置
                        stop_loss_threshold = atr * 3.0
                        
                        print(f"📈 移动止盈触发阈值: {trigger_threshold:.4f}")
                        print(f"🛡️ ATR止损阈值: {stop_loss_threshold:.4f}")
                        
                        return True
                    else:
                        print(f"❌ ATR计算结果为0")
                        return False
                else:
                    print(f"⚠️ TR数据不足: {len(tr_list)}/14")
                    return False
            else:
                print(f"⚠️ 数据不足，无法计算ATR: {len(data_list)}/15")
                return False
        else:
            print(f"❌ 无法获取ATR计算数据")
            return False
            
    except Exception as e:
        print(f"❌ ATR计算测试异常: {e}")
        return False

def test_realtime_data(ContextInfo):
    """
    测试实时数据获取
    """
    try:
        stock_code = getattr(ContextInfo, 'stock', '123171.SZ')
        
        # 获取最新数据
        current_data = get_market_data_ex(
            stock_list=[stock_code],
            period='1d',
            count=1,
            dividend_type='none'
        )
        
        if current_data and stock_code in current_data:
            latest_data = current_data[stock_code]
            if len(latest_data) > 0:
                latest = latest_data[0]
                
                price = float(latest.get('close', 0))
                volume = float(latest.get('volume', 0))
                high = float(latest.get('high', 0))
                low = float(latest.get('low', 0))
                
                print(f"💰 当前价格: {price:.3f}")
                print(f"📈 今日最高: {high:.3f}")
                print(f"📉 今日最低: {low:.3f}")
                print(f"📦 成交量: {volume:.0f}")
                
                if price > 0 and volume > 0 and high >= low:
                    print(f"✅ 实时数据正常")
                    return True
                else:
                    print(f"⚠️ 实时数据异常")
                    return False
            else:
                print(f"❌ 实时数据为空")
                return False
        else:
            print(f"❌ 无法获取实时数据")
            return False
            
    except Exception as e:
        print(f"❌ 实时数据测试异常: {e}")
        return False

def quick_quality_check(ContextInfo):
    """
    快速数据质量检查
    """
    try:
        stock_code = getattr(ContextInfo, 'stock', '123171.SZ')
        
        # 获取最近5根K线
        recent_data = get_market_data_ex(
            stock_list=[stock_code],
            period='1d',
            count=5,
            dividend_type='none'
        )
        
        if recent_data and stock_code in recent_data:
            data_list = recent_data[stock_code]
            valid_count = 0
            
            for item in data_list:
                price = float(item.get('close', 0))
                volume = float(item.get('volume', 0))
                if price > 0 and volume > 0:
                    valid_count += 1
            
            print(f"   📊 最近5根K线质量: {valid_count}/5 有效")
            
            if valid_count >= 4:
                print(f"   ✅ 数据质量良好")
            else:
                print(f"   ⚠️ 数据质量需要关注")
        
    except Exception as e:
        print(f"   ❌ 质量检查异常: {e}")

# ============================================================================
# 策略信息
# ============================================================================

if __name__ == "__main__":
    print("🔍 QMT数据预热测试策略")
    print("📋 这是一个QMT策略文件，请在QMT中加载运行")
    print("\n💡 主要功能:")
    print("1. 测试历史数据获取能力")
    print("2. 验证ATR计算所需数据")
    print("3. 检查数据质量和完整性")
    print("4. 为主策略运行提供数据预热验证")
    print("\n🚀 使用方法:")
    print("1. 在QMT中加载此策略文件")
    print("2. 设置要测试的股票代码")
    print("3. 运行策略查看测试结果")
    print("4. 根据测试结果决定是否启用主策略")
