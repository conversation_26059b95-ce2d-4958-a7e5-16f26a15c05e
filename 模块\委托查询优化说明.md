# QMT委托查询优化说明

## 🤔 为什么会查询到已撤销的委托？

### **QMT API的设计特点**：

1. **`get_trade_detail_data(account, 'stock', 'order')`** 返回**所有委托记录**
   - 包括：未报、待报、已报待成交、部分成交、全部成交、已撤销、部分撤销
   - **不区分状态**，一次性返回当日所有委托历史

2. **这是QMT的标准行为**：
   - ✅ 优点：完整的委托历史记录，便于审计和分析
   - ❌ 缺点：包含大量无需处理的历史委托

## 🎯 优化方案

### **方案1：状态过滤（已实现）**

在获取委托后，根据状态码过滤：

```python
# 配置参数
STRATEGY_CONFIG = {
    'filter_cancelled_orders': True,    # 过滤已撤销委托 (53, 54)
    'filter_completed_orders': True,    # 过滤已完成委托 (52)
}

# 过滤逻辑
for order_obj in orders:
    order_status = getattr(order_obj, 'm_nOrderStatus', -1)
    
    is_cancelled = order_status in [53, 54]  # 已撤销、部分撤销
    is_completed = order_status == 52        # 全部成交
    
    if filter_cancelled_orders and is_cancelled:
        continue  # 跳过已撤销委托
    if filter_completed_orders and is_completed:
        continue  # 跳过已完成委托
```

### **方案2：只关注未处理委托**

```python
# 只处理这些状态的委托
pending_status = [48, 49, 50, 51]  # 未报、待报、已报待成交、部分成交

for order_obj in orders:
    order_status = getattr(order_obj, 'm_nOrderStatus', -1)
    if order_status not in pending_status:
        continue  # 跳过非未处理委托
```

## 📊 效果对比

### **优化前**：
```
📋 找到 5 个委托
📋 委托 #1: 状态=54 (部分撤销) ❌ 无需处理
📋 委托 #2: 状态=54 (部分撤销) ❌ 无需处理  
📋 委托 #3: 状态=54 (部分撤销) ❌ 无需处理
📋 委托 #4: 状态=54 (部分撤销) ❌ 无需处理
📋 委托 #5: 状态=50 (已报待成交) ✅ 需要处理
```

### **优化后**：
```
📋 总委托: 5个, 有效委托: 1个
🔍 已过滤已撤销委托
🔍 已过滤已完成委托
📋 委托 #1: 状态=50 (已报待成交) ✅ 需要处理
```

## ⚡ 性能提升

### **处理效率**：
- **优化前**：处理5个委托，4个无效
- **优化后**：处理1个委托，1个有效
- **效率提升**：80% 减少无效处理

### **日志清洁度**：
- **优化前**：大量已撤销委托的调试信息
- **优化后**：只显示需要关注的委托信息

## 🔧 配置选项

### **测试文件配置**：
```python
TEST_CONFIG = {
    'filter_cancelled_orders': True,    # 过滤已撤销委托
    'show_only_pending': False,         # 是否只显示未处理委托
}
```

### **主策略配置**：
```python
STRATEGY_CONFIG = {
    'filter_cancelled_orders': True,    # 过滤已撤销委托
    'filter_completed_orders': True,    # 过滤已完成委托
}
```

## 🎯 建议设置

### **生产环境**：
```python
'filter_cancelled_orders': True,     # ✅ 推荐：过滤已撤销
'filter_completed_orders': True,     # ✅ 推荐：过滤已完成
```

### **调试环境**：
```python
'filter_cancelled_orders': False,    # 显示所有委托便于调试
'filter_completed_orders': False,    # 显示所有委托便于调试
```

## 📋 状态码参考

| 状态码 | 描述 | 是否需要处理 |
|--------|------|-------------|
| 48 | 未报 | ✅ 需要 |
| 49 | 待报 | ✅ 需要 |
| 50 | 已报待成交 | ✅ 需要 |
| 51 | 部分成交 | ✅ 需要 |
| 52 | 全部成交 | ❌ 已完成 |
| 53 | 已撤销 | ❌ 已撤销 |
| 54 | 部分撤销 | ❌ 已撤销 |

## 🚀 总结

通过状态过滤优化：
1. **减少无效处理**：只处理需要关注的委托
2. **提高执行效率**：减少循环和判断次数
3. **清洁日志输出**：减少干扰信息
4. **降低系统负载**：减少不必要的计算

**这是QMT委托管理的最佳实践！** ✨
