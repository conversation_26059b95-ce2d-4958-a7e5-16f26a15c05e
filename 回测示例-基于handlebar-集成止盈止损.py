#coding:gbk

"""
回测示例-基于handlebar-集成止盈止损策略
==========================================

策略概述：
本策略基于回测示例框架，集成了QMT止盈止损下单模块的核心功能，
包括K线合成、数据预热、ATR动态止盈止损和唐奇安通道移动止盈。

策略特点：
- 基础框架：双均线交叉系统（回测版本）
- K线合成：支持多根K线合成，增量成交量处理
- 数据预热：智能数据获取和预处理
- 止盈止损：ATR动态止损 + 唐奇安通道移动止盈
- 适用环境：回测环境（非实盘）

核心功能：
✅ K线合成功能（2根或3根合成1根）
✅ 累积成交量转增量成交量
✅ ATR动态止损计算
✅ 唐奇安通道移动止盈
✅ 策略状态持久化
✅ 数据预热和异常处理

技术指标：
- 快线：10日简单移动平均线
- 慢线：20日简单移动平均线
- ATR周期：14日
- 唐奇安通道周期：20日

交易逻辑：
- 买入：快线上穿慢线 + 数据预热完成
- 卖出：ATR动态止损 或 唐奇安移动止盈触发

注意事项：
- 本策略用于回测环境，已适配回测API
- 集成了实盘模块的核心算法，但简化了委托管理
- 建议在充分回测后再考虑实盘应用
"""

# 导入必要的库
import pandas as pd
import numpy as np
# 注意：timetag_to_datetime, get_trade_detail_data, passorder 等是QMT内置函数
# 这些函数在QMT环境中会自动可用

# ============================================================================
# 策略配置参数（从QMT止盈止损模块提取）
# ============================================================================

STRATEGY_CONFIG = {
    # 基础交易参数
    'max_position_ratio': 0.7,         # 最大仓位比例
    'min_cash_reserve': 100,            # 最小资金保留
    'fixed_stop_loss': 0.5,             # 固定止损百分比
    
    # K线合成配置
    'enable_kline_merge': True,         # 是否启用K线合成
    'merge_ratio': 2,                   # 合成比例 (2根合成1根)
    'max_buffer_size': 10,              # 最大缓冲区大小
    'max_history_bars': 200,            # 最大历史K线数量
    'convert_cumulative_volume': True,  # 是否将累积成交量转换为增量成交量
    
    # 技术指标参数
    'donchian_period': 20,              # 唐奇安通道周期
    'atr_period': 14,                   # ATR计算周期
    'atr_stop_loss_multiplier': 3.0,    # ATR止损倍数
    'atr_trigger_multiplier': 4.0,      # ATR移动止盈触发倍数
    'min_move_threshold': 0.1,          # 最小移动幅度
    
    # 数据缓冲区配置（Tick数据预热功能）
    'enable_data_buffer': True,          # 是否启用数据缓冲区功能
    'buffer_size': 100,                  # tick数据缓冲区大小
    'history_data_count': 60,            # 预加载历史tick数据数量（30根K线=60个tick）
    'min_trading_periods': 20,           # 开始交易所需的最少tick数据周期（10根K线=20个tick）
    'debug_mode': False,                 # 调试模式

    # 数据预热配置（传统模式，保持兼容）
    'data_warmup_bars': 3,              # 数据预热需要的K线数量
    'enable_data_warmup': True,         # 是否启用数据预热
}

# ============================================================================
# 数据缓冲区管理类（Tick数据预热功能）
# ============================================================================

from collections import deque
import datetime

class MarketDataBuffer:
    """
    市场数据缓冲区管理类 - 支持tick数据预加载和实时数据增量更新
    适配回测环境的版本
    """

    def __init__(self, buffer_size=100):
        """
        初始化数据缓冲区

        Args:
            buffer_size: 缓冲区大小，默认100根K线
        """
        self.buffer_size = buffer_size

        # 价格数据缓冲区（存储合成后的K线数据）
        self.close_buffer = deque(maxlen=buffer_size)
        self.high_buffer = deque(maxlen=buffer_size)
        self.low_buffer = deque(maxlen=buffer_size)
        self.open_buffer = deque(maxlen=buffer_size)

        # 成交量数据缓冲区
        self.volume_buffer = deque(maxlen=buffer_size)

        # 时间戳缓冲区
        self.time_buffer = deque(maxlen=buffer_size)

        # Tick数据临时缓冲区（用于合成K线）
        self.tick_buffer = deque(maxlen=10)  # 临时存储tick数据
        self.pending_ticks = []  # 等待合成的tick数据

        # 指标缓存
        self.indicators = {}

        # 初始化状态
        self.is_initialized = False

        print(f"📊 数据缓冲区初始化完成，容量: {buffer_size}根K线（基于tick合成）")

    def preload_history_data(self, ContextInfo, stock_code, period='tick', count=100):
        """
        预加载历史数据填充缓冲区 - 专门针对tick数据优化（回测版本）

        Args:
            ContextInfo: QMT上下文对象
            stock_code: 股票代码
            period: 数据周期（默认tick）
            count: 获取数量
        """
        try:
            print(f"📥 开始预加载历史数据: {stock_code}, 周期: {period}, 数量: {count}")

            # 针对tick数据的特殊处理
            if period == 'tick':
                print(f"  📊 Tick数据模式 - 直接获取最近{count}个tick数据")

                # 回测环境中使用get_market_data_ex获取tick数据
                hist_data = ContextInfo.get_market_data_ex(
                    ['lastPrice', 'volume'],  # 回测环境中明确指定字段
                    [stock_code],
                    period='tick',
                    count=count,  # 只获取需要的数量
                    subscribe=False  # 回测模式不订阅
                )
            else:
                # 非tick数据的处理（保持原有逻辑）
                hist_data = ContextInfo.get_market_data_ex(
                    ['close', 'high', 'low', 'open', 'volume'],  # 明确指定字段
                    [stock_code],
                    period=period,
                    count=count,
                    subscribe=False  # 回测模式
                )

            if hist_data is None or stock_code not in hist_data:
                print(f"⚠️ 历史数据获取失败，尝试备用方案...")
                # 备用方案：获取最近数据
                hist_data = ContextInfo.get_market_data_ex(
                    ['close'],  # 最小字段集
                    [stock_code],
                    period=period,
                    count=min(count, 50),  # 减少数量
                    subscribe=False
                )

            if hist_data is None or stock_code not in hist_data:
                print(f"❌ 历史数据获取失败，将使用实时数据逐步填充缓冲区")
                return False

            # 解析历史数据
            data_df = hist_data[stock_code]
            data_count = len(data_df)

            if period == 'tick':
                print(f"✅ 成功获取 {data_count} 个历史tick数据")
            else:
                print(f"✅ 成功获取 {data_count} 根历史K线数据")

            # 填充缓冲区 - 针对tick数据特殊处理
            if period == 'tick':
                print(f"📊 处理历史tick数据，将合成K线...")
                # 调试：打印数据结构信息
                print(f"📊 数据列名: {list(data_df.columns) if hasattr(data_df, 'columns') else 'N/A'}")
                if data_count > 0:
                    first_row = data_df.iloc[0]
                    print(f"📊 第一行数据字段: {list(first_row.keys()) if hasattr(first_row, 'keys') else 'N/A'}")

                # Tick数据需要合成K线
                tick_data_list = []

                for i in range(data_count):
                    row = data_df.iloc[i]

                    # 构造tick数据 - 尝试多种可能的字段名（回测环境适配）
                    tick_price = 0
                    for price_field in ['lastPrice', 'last_price', 'price', 'close', 'last']:
                        if price_field in row and row[price_field] is not None:
                            tick_price = float(row[price_field])
                            break

                    tick_volume = 0
                    for volume_field in ['volume', 'lastVolume', 'last_volume', 'vol']:
                        if volume_field in row and row[volume_field] is not None:
                            tick_volume = float(row[volume_field])
                            break

                    time_val = row.get('time', row.get('timetag', ''))

                    tick_data_list.append({
                        'price': tick_price,
                        'volume': tick_volume,
                        'time': time_val
                    })

                # 将tick数据两两合成K线
                kline_count = 0
                for i in range(0, len(tick_data_list) - 1, 2):
                    if i + 1 < len(tick_data_list):
                        # 合成K线
                        tick_pair = tick_data_list[i:i+2]
                        kline_data = self._merge_ticks_to_kline(tick_pair)

                        # 添加到缓冲区
                        self._add_kline_to_buffer(kline_data)
                        kline_count += 1

                print(f"✅ 历史tick数据合成完成：{data_count}个tick → {kline_count}根K线")

            else:
                # K线数据处理（原有逻辑）
                for i in range(data_count):
                    row = data_df.iloc[i]

                    self.close_buffer.append(float(row.get('close', row.get('lastPrice', 0))))
                    self.high_buffer.append(float(row.get('high', row.get('lastPrice', 0))))
                    self.low_buffer.append(float(row.get('low', row.get('lastPrice', 0))))
                    self.open_buffer.append(float(row.get('open', row.get('lastPrice', 0))))
                    self.volume_buffer.append(float(row.get('volume', 0)))

                    # 时间戳处理
                    time_val = row.get('time', row.get('timetag', ''))
                    self.time_buffer.append(str(time_val))

            self.is_initialized = True
            print(f"📊 缓冲区预加载完成，当前数据量: {len(self.close_buffer)}根K线")
            return True

        except Exception as e:
            print(f"❌ 历史数据预加载异常: {e}")
            import traceback
            traceback.print_exc()
            return False

    def add_tick_data(self, tick_price, tick_volume, timestamp=None):
        """
        添加tick数据并尝试合成K线

        Args:
            tick_price: tick价格
            tick_volume: tick成交量
            timestamp: 时间戳
        """
        try:
            # 构造tick数据
            tick_data = {
                'price': tick_price,
                'volume': tick_volume,
                'time': timestamp or datetime.datetime.now()
            }

            # 添加到tick缓冲区
            self.pending_ticks.append(tick_data)

            # 检查是否可以合成K线（两个tick合成一个K线）
            if len(self.pending_ticks) >= 2:
                # 合成K线
                kline_data = self._merge_ticks_to_kline(self.pending_ticks[:2])

                # 将合成的K线添加到缓冲区
                self._add_kline_to_buffer(kline_data)

                # 移除已合成的tick
                self.pending_ticks = self.pending_ticks[2:]

                # 清除指标缓存
                self.indicators.clear()

                # 显示详细的成交量计算信息
                original_vols = kline_data.get('original_volumes', [])
                if len(original_vols) >= 2:
                    print(f"📊 Tick合成K线: OHLC[{kline_data['open']:.3f}, {kline_data['high']:.3f}, {kline_data['low']:.3f}, {kline_data['close']:.3f}]")
                    print(f"   📈 成交量计算: {original_vols[1]:.0f} - {original_vols[0]:.0f} = {kline_data['volume']:.0f} (增量)")
                else:
                    print(f"📊 Tick合成K线: OHLC[{kline_data['open']:.3f}, {kline_data['high']:.3f}, {kline_data['low']:.3f}, {kline_data['close']:.3f}], 成交量={kline_data['volume']:.0f}")

        except Exception as e:
            print(f"❌ Tick数据处理异常: {e}")

    def _merge_ticks_to_kline(self, tick_list):
        """
        将tick数据合成为K线

        Args:
            tick_list: tick数据列表

        Returns:
            dict: 合成的K线数据
        """
        if len(tick_list) < 2:
            raise ValueError("至少需要2个tick数据来合成K线")

        # 计算OHLC
        prices = [tick['price'] for tick in tick_list]
        open_price = tick_list[0]['price']
        close_price = tick_list[-1]['price']
        high_price = max(prices)
        low_price = min(prices)

        # 计算增量成交量（tick数据是累积成交量，需要计算差值）
        if len(tick_list) == 2:
            # 两个tick的情况：第二个tick的累积成交量 - 第一个tick的累积成交量
            volume_increment = tick_list[1]['volume'] - tick_list[0]['volume']
            # 如果增量为负或为0，使用最小增量
            if volume_increment <= 0:
                volume_increment = max(1, tick_list[1]['volume'] / 1000)  # 保守估算
        else:
            # 多个tick的情况：最后一个 - 第一个
            volume_increment = tick_list[-1]['volume'] - tick_list[0]['volume']
            if volume_increment <= 0:
                volume_increment = max(1, tick_list[-1]['volume'] / 1000)

        return {
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume_increment,  # 使用增量成交量
            'start_time': tick_list[0]['time'],
            'end_time': tick_list[-1]['time'],
            'original_volumes': [tick['volume'] for tick in tick_list]  # 保留原始累积成交量用于调试
        }

    def _add_kline_to_buffer(self, kline_data):
        """
        将K线数据添加到缓冲区

        Args:
            kline_data: K线数据字典
        """
        self.open_buffer.append(kline_data['open'])
        self.high_buffer.append(kline_data['high'])
        self.low_buffer.append(kline_data['low'])
        self.close_buffer.append(kline_data['close'])
        self.volume_buffer.append(kline_data['volume'])
        self.time_buffer.append(str(kline_data.get('end_time', '')))

    def calculate_ma(self, period):
        """计算移动平均线"""
        if len(self.close_buffer) < period:
            return None

        cache_key = f'ma_{period}'
        if cache_key not in self.indicators:
            recent_prices = list(self.close_buffer)[-period:]
            self.indicators[cache_key] = sum(recent_prices) / period

        return self.indicators[cache_key]

    def calculate_atr(self, period=14):
        """计算ATR（平均真实波幅）"""
        if len(self.close_buffer) < period + 1:
            return None

        cache_key = f'atr_{period}'
        if cache_key not in self.indicators:
            highs = list(self.high_buffer)[-period-1:]
            lows = list(self.low_buffer)[-period-1:]
            closes = list(self.close_buffer)[-period-1:]

            true_ranges = []
            for i in range(1, len(highs)):
                tr1 = highs[i] - lows[i]
                tr2 = abs(highs[i] - closes[i-1])
                tr3 = abs(lows[i] - closes[i-1])
                true_ranges.append(max(tr1, tr2, tr3))

            if true_ranges:
                self.indicators[cache_key] = sum(true_ranges) / len(true_ranges)
            else:
                self.indicators[cache_key] = 0

        return self.indicators[cache_key]

    def get_current_price(self):
        """获取当前价格"""
        return self.close_buffer[-1] if self.close_buffer else None

    def get_current_volume(self):
        """获取当前成交量"""
        return self.volume_buffer[-1] if self.volume_buffer else 0

    def get_data_count(self):
        """获取缓冲区数据量"""
        return len(self.close_buffer)

    def is_ready_for_trading(self, min_periods=20):
        """检查是否有足够数据进行交易"""
        return len(self.close_buffer) >= min_periods

    def get_price_data(self, count=None):
        """获取价格数据"""
        if count is None:
            return {
                'close': list(self.close_buffer),
                'high': list(self.high_buffer),
                'low': list(self.low_buffer),
                'open': list(self.open_buffer),
                'volume': list(self.volume_buffer)
            }
        else:
            return {
                'close': list(self.close_buffer)[-count:],
                'high': list(self.high_buffer)[-count:],
                'low': list(self.low_buffer)[-count:],
                'open': list(self.open_buffer)[-count:],
                'volume': list(self.volume_buffer)[-count:]
            }

# ============================================================================
# K线合成模块（从QMT止盈止损模块提取并适配回测）
# ============================================================================

def convert_cumulative_to_incremental_volume(data_list):
    """
    将累积成交量转换为增量成交量
    
    参数:
        data_list: 包含成交量数据的列表，每个元素应有'volume'字段
    
    返回:
        list: 转换后的数据列表，成交量已转换为增量值
    """
    if not data_list or len(data_list) == 0:
        return data_list
    
    converted_data = []
    prev_volume = 0
    
    # 计算平均成交量用于异常检测
    volumes = [data.get('volume', 0) for data in data_list]
    avg_volume = sum(volumes) / len(volumes) if volumes else 0
    
    print(f"📊 成交量转换开始: {len(data_list)}个数据点，平均成交量={avg_volume:.0f}")
    
    for i, data in enumerate(data_list):
        current_volume = float(data.get('volume', 0))
        
        if i == 0:
            # 第一个数据点处理
            if prev_volume == 0:
                # 无历史数据，使用保守估算
                incremental_volume = max(1, current_volume / 1000)
                print(f"📊 首次数据点，累积成交量: {current_volume:.0f}, 估算增量: {incremental_volume:.0f}")
            else:
                incremental_volume = current_volume
        else:
            # 后续数据点 - 计算增量成交量
            raw_increment = current_volume - prev_volume
            
            if raw_increment > 0:
                incremental_volume = raw_increment
            elif raw_increment == 0:
                # 成交量没有变化，使用最小增量
                incremental_volume = 1
            else:
                # 负增量，可能是数据重置或异常
                incremental_volume = max(1, current_volume / 1000)
                print(f"⚠️ 检测到负增量: {raw_increment:.0f}, 使用保守估算: {incremental_volume:.0f}")
        
        # 异常值检测和修正
        if avg_volume > 0 and incremental_volume > avg_volume * 10:
            print(f"🚨 检测到异常巨大的增量成交量: {incremental_volume:.0f}")
            incremental_volume = min(incremental_volume, avg_volume * 3)
            print(f"   🔧 修正为保守值: {incremental_volume:.0f}")
        
        # 最小值保护
        incremental_volume = max(1, incremental_volume)
        
        # 创建新的数据副本，更新成交量为增量值
        new_data = data.copy()
        new_data['volume'] = incremental_volume
        new_data['original_volume'] = current_volume
        converted_data.append(new_data)
        
        prev_volume = current_volume
    
    print(f"✅ 成交量转换完成: 处理{len(converted_data)}个数据点")
    return converted_data

def merge_two_bars(data_list, convert_volume=True):
    """
    将两个数据合成为一个OHLC+V K线
    
    参数:
        data_list: 包含两个数据点的列表
        convert_volume: 是否将累积成交量转换为增量成交量
    
    返回:
        dict: 合成后的K线数据
    """
    if len(data_list) != 2:
        raise ValueError("需要恰好两个数据进行合成")
    
    # 如果需要转换成交量，先进行转换
    if convert_volume:
        converted_data = convert_cumulative_to_incremental_volume(data_list)
        data1, data2 = converted_data
    else:
        data1, data2 = data_list
    
    # 计算OHLC
    open_price = data1['price']
    close_price = data2['price']
    high_price = max(data1['price'], data2['price'])
    low_price = min(data1['price'], data2['price'])
    
    # 使用增量成交量进行合成
    total_volume = data1['volume'] + data2['volume']
    
    print(f"📊 K线合成: 成交量 {data1['volume']} + {data2['volume']} = {total_volume}")
    
    return {
        'start_time': data1.get('time', ''),
        'end_time': data2.get('time', ''),
        'open': round(open_price, 3),
        'high': round(high_price, 3),
        'low': round(low_price, 3),
        'close': round(close_price, 3),
        'volume': total_volume,
        'original_volumes': [data1.get('original_volume', data1['volume']),
                           data2.get('original_volume', data2['volume'])]
    }

def process_kline_merge(ContextInfo, bar_time, last_price, last_volume):
    """
    处理K线合成逻辑（回测版本）
    
    参数:
        ContextInfo: QMT上下文对象
        bar_time: 时间字符串
        last_price: 当前价格
        last_volume: 当前成交量
    """
    if not STRATEGY_CONFIG['enable_kline_merge']:
        return
    
    # 初始化K线合成相关变量
    if not hasattr(ContextInfo, 'kline_buffer'):
        ContextInfo.kline_buffer = []
        ContextInfo.merged_klines = []
        ContextInfo.bar_count = 0
    
    # 构建当前数据
    current_data = {
        'time': bar_time,
        'price': last_price,
        'volume': last_volume
    }
    
    ContextInfo.kline_buffer.append(current_data)
    ContextInfo.bar_count += 1
    
    print(f"📈 K线缓冲区: {len(ContextInfo.kline_buffer)}个数据点")
    
    # 根据配置的合成比例进行合成
    merge_ratio = STRATEGY_CONFIG['merge_ratio']
    if len(ContextInfo.kline_buffer) >= merge_ratio:
        merge_count = 0
        while len(ContextInfo.kline_buffer) >= merge_ratio:
            convert_volume = STRATEGY_CONFIG['convert_cumulative_volume']
            merged_kline = merge_two_bars(ContextInfo.kline_buffer[:2], convert_volume=convert_volume)
            
            ContextInfo.merged_klines.append(merged_kline)
            
            print(f"✅ 合成K线{merge_count + 1}: OHLC[{merged_kline['open']}, {merged_kline['high']}, {merged_kline['low']}, {merged_kline['close']}], 成交量={merged_kline['volume']}")
            
            ContextInfo.kline_buffer = ContextInfo.kline_buffer[merge_ratio:]
            merge_count += 1
        
        print(f"🔄 本次合成{merge_count}个K线，剩余缓冲区: {len(ContextInfo.kline_buffer)}")
    
    # 缓冲区异常清理
    max_buffer_size = STRATEGY_CONFIG['max_buffer_size']
    if len(ContextInfo.kline_buffer) > max_buffer_size:
        ContextInfo.kline_buffer = ContextInfo.kline_buffer[-1:]
        print("⚠️ 缓冲区异常增长，已清理")
    
    # 滑动窗口数据管理
    maintain_sliding_window(ContextInfo)

def maintain_sliding_window(ContextInfo):
    """
    维护滑动窗口数据管理
    
    参数:
        ContextInfo: QMT上下文对象
    """
    try:
        if not hasattr(ContextInfo, 'merged_klines'):
            return
        
        # 计算最大K线数量
        MAX_KLINES = STRATEGY_CONFIG['max_history_bars']
        current_count = len(ContextInfo.merged_klines)
        
        if current_count > MAX_KLINES:
            # FIFO：移除最旧的数据，保留最新的数据
            removed_count = current_count - MAX_KLINES
            ContextInfo.merged_klines = ContextInfo.merged_klines[-MAX_KLINES:]
            
            print(f"🔄 滑动窗口清理: 移除{removed_count}个旧K线，保留{len(ContextInfo.merged_klines)}个")
    
    except Exception as e:
        print(f"❌ 滑动窗口维护失败: {e}")

def get_merged_kline_data(ContextInfo):
    """
    获取合成K线数据用于技术指标计算
    
    参数:
        ContextInfo: QMT上下文对象
    
    返回:
        dict: 包含OHLCV数组的字典
    """
    try:
        if not hasattr(ContextInfo, 'merged_klines') or not ContextInfo.merged_klines:
            return None
        
        merged_klines = ContextInfo.merged_klines
        
        return {
            'opens': [kline['open'] for kline in merged_klines],
            'highs': [kline['high'] for kline in merged_klines],
            'lows': [kline['low'] for kline in merged_klines],
            'closes': [kline['close'] for kline in merged_klines],
            'volumes': [kline['volume'] for kline in merged_klines],
            'count': len(merged_klines)
        }
    
    except Exception as e:
        print(f"❌ 获取合成K线数据失败: {e}")
        return None

# ============================================================================
# 技术指标计算模块（从QMT止盈止损模块提取）
# ============================================================================

def calculate_atr(highs, lows, closes, period=14):
    """
    计算平均真实波幅 (ATR)

    参数:
        highs: 最高价数组
        lows: 最低价数组
        closes: 收盘价数组
        period: ATR计算周期

    返回:
        dict: ATR信息
    """
    try:
        highs = np.asarray(highs, dtype=np.float64)
        lows = np.asarray(lows, dtype=np.float64)
        closes = np.asarray(closes, dtype=np.float64)

        if len(highs) < period + 1:
            # 数据不足时，使用保守的ATR估算
            if len(highs) >= 2:
                # 使用现有数据计算简单波幅
                price_range = max(highs) - min(lows)
                estimated_atr = price_range / len(highs) * 0.5  # 保守估算
                print(f"⚠️ ATR数据不足({len(highs)}个数据点，需要{period+1}个)，使用估算ATR: {estimated_atr:.4f}")
            else:
                estimated_atr = 0.01  # 最小保护值
                print(f"⚠️ ATR数据严重不足({len(highs)}个数据点)，使用最小保护值: {estimated_atr:.4f}")

            return {
                'atr': estimated_atr,
                'current_atr': estimated_atr,
                'data_sufficient': False,
                'period': period,
                'data_points': len(highs),
                'estimated': True
            }

        # 计算真实波幅 (True Range)
        tr_list = []
        for i in range(1, len(highs)):
            tr1 = highs[i] - lows[i]  # 当日最高价 - 当日最低价
            tr2 = abs(highs[i] - closes[i-1])  # 当日最高价 - 前日收盘价
            tr3 = abs(lows[i] - closes[i-1])   # 当日最低价 - 前日收盘价
            tr = max(tr1, tr2, tr3)
            tr_list.append(tr)

        tr_array = np.array(tr_list)

        # 计算ATR (简单移动平均)
        if len(tr_array) >= period:
            atr = np.mean(tr_array[-period:])
            current_atr = atr
        else:
            atr = np.mean(tr_array)
            current_atr = atr

        return {
            'atr': round(atr, 4),
            'current_atr': round(current_atr, 4),
            'data_sufficient': len(tr_array) >= period,
            'period': period,
            'tr_values': tr_list[-5:] if len(tr_list) >= 5 else tr_list  # 最近5个TR值
        }

    except Exception as e:
        print(f"❌ ATR计算失败: {e}")
        return {
            'atr': 0,
            'current_atr': 0,
            'data_sufficient': False,
            'period': period
        }

def calculate_donchian_channel(highs, lows, period=20):
    """
    计算唐奇安通道

    参数:
        highs: 最高价数组
        lows: 最低价数组
        period: 计算周期

    返回:
        dict: 唐奇安通道信息
    """
    try:
        highs = np.asarray(highs, dtype=np.float64)
        lows = np.asarray(lows, dtype=np.float64)

        if len(highs) < period:
            return {
                'upper_channel': highs[-1] if len(highs) > 0 else 100,
                'lower_channel': lows[-1] if len(lows) > 0 else 100,
                'middle_channel': (highs[-1] + lows[-1]) / 2 if len(highs) > 0 else 100,
                'period': period,
                'data_sufficient': False
            }

        # 计算唐奇安通道
        upper_channel = np.max(highs[-period:])  # 最高价
        lower_channel = np.min(lows[-period:])   # 最低价
        middle_channel = (upper_channel + lower_channel) / 2  # 中轨

        return {
            'upper_channel': upper_channel,
            'lower_channel': lower_channel,
            'middle_channel': middle_channel,
            'period': period,
            'data_sufficient': True,
            'channel_width': upper_channel - lower_channel,
            'channel_width_pct': (upper_channel - lower_channel) / middle_channel * 100 if middle_channel > 0 else 0
        }

    except Exception as e:
        print(f"❌ 唐奇安通道计算失败: {e}")
        return {
            'upper_channel': highs[-1] if len(highs) > 0 else 100,
            'lower_channel': lows[-1] if len(lows) > 0 else 100,
            'middle_channel': (highs[-1] + lows[-1]) / 2 if len(highs) > 0 else 100,
            'period': period,
            'data_sufficient': False
        }

# ============================================================================
# 止盈止损检查模块（从QMT止盈止损模块提取并适配回测）
# ============================================================================

def check_exit_conditions_donchian(ContextInfo, current_price, entry_price, highest_price_since_entry, trailing_stop_price, market_data=None):
    """
    检查平仓条件 - 基于唐奇安通道和ATR的移动止盈止损（回测版本）

    参数:
        ContextInfo: QMT上下文对象
        current_price: 当前价格
        entry_price: 入场价格
        highest_price_since_entry: 入场后最高价
        trailing_stop_price: 移动止盈价格
        market_data: 市场数据 {'highs': [], 'lows': [], 'closes': []}

    返回:
        dict: 平仓决策结果
    """
    if entry_price <= 0:
        return {'should_exit': False, 'reason': '无有效入场价格', 'new_trailing_stop': trailing_stop_price}

    # 计算盈亏比例
    profit_pct = (current_price - entry_price) / entry_price

    # 更新最高价
    new_highest_price = max(highest_price_since_entry, current_price)
    new_trailing_stop = trailing_stop_price

    # 计算ATR
    atr_info = {'atr': 0, 'data_sufficient': False}
    if market_data and 'highs' in market_data and 'lows' in market_data and 'closes' in market_data:
        atr_info = calculate_atr(
            market_data['highs'],
            market_data['lows'],
            market_data['closes'],
            STRATEGY_CONFIG['atr_period']
        )

    # ATR动态止损检查
    if atr_info['data_sufficient'] and atr_info['atr'] > 0:
        atr_stop_loss = entry_price - (atr_info['atr'] * STRATEGY_CONFIG['atr_stop_loss_multiplier'])
        if current_price <= atr_stop_loss:
            return {
                'should_exit': True,
                'reason': f'ATR动态止损触发 (ATR={atr_info["atr"]:.3f}, 止损价={atr_stop_loss:.3f})',
                'price': current_price,
                'new_trailing_stop': new_trailing_stop,
                'new_highest_price': new_highest_price,
                'atr_info': atr_info
            }
    else:
        # 如果ATR数据不足，使用固定止损
        fixed_stop_loss_pct = STRATEGY_CONFIG['fixed_stop_loss'] / 100
        if profit_pct <= -fixed_stop_loss_pct:
            return {
                'should_exit': True,
                'reason': f'固定止损触发 ({profit_pct:.2%})',
                'price': current_price,
                'new_trailing_stop': new_trailing_stop,
                'new_highest_price': new_highest_price
            }

    # 计算唐奇安通道
    donchian_info = {'data_sufficient': False}
    if market_data and 'highs' in market_data and 'lows' in market_data:
        donchian_info = calculate_donchian_channel(
            market_data['highs'],
            market_data['lows'],
            STRATEGY_CONFIG['donchian_period']
        )

    # 唐奇安通道移动止盈逻辑
    if market_data and donchian_info['data_sufficient']:
        # 计算移动止盈触发阈值
        if atr_info['data_sufficient'] and atr_info['atr'] > 0:
            # 使用ATR计算触发阈值
            atr_trigger_threshold = atr_info['atr'] * STRATEGY_CONFIG['atr_trigger_multiplier']
        else:
            # ATR数据不足时，使用固定百分比作为触发阈值
            atr_trigger_threshold = entry_price * 0.005  # 0.5%作为默认触发阈值
            print(f"⚠️ ATR数据不足，使用固定触发阈值: {atr_trigger_threshold:.3f} (0.5%)")

        profit_amount = current_price - entry_price

        # 确保触发阈值不为0，避免任何微小利润都触发
        if atr_trigger_threshold <= 0:
            atr_trigger_threshold = entry_price * 0.01  # 最小1%触发阈值
            print(f"⚠️ 触发阈值为0，使用最小保护阈值: {atr_trigger_threshold:.3f} (1.0%)")

        print(f"🔍 移动止盈检查: 利润={profit_amount:.3f}, 触发阈值={atr_trigger_threshold:.3f}")

        if profit_amount >= atr_trigger_threshold:
            print(f"✅ 达到移动止盈触发条件，开始跟踪唐奇安下轨")
            # 直接使用唐奇安下轨作为移动止盈线
            donchian_stop = donchian_info['lower_channel']

            # 移动止盈线只能向上移动
            if donchian_stop > new_trailing_stop:
                new_trailing_stop = donchian_stop
                print(f"📈 更新移动止盈线: {new_trailing_stop:.3f}")

            # 检查是否触发移动止盈 (收盘价小于唐奇安下轨)
            if new_trailing_stop > 0 and current_price <= new_trailing_stop:
                protected_profit = (new_trailing_stop - entry_price) / entry_price * 100
                return {
                    'should_exit': True,
                    'reason': f'唐奇安移动止盈触发 (保护{protected_profit:.1f}%利润, ATR触发阈值={atr_trigger_threshold:.3f})',
                    'price': current_price,
                    'new_trailing_stop': new_trailing_stop,
                    'new_highest_price': new_highest_price,
                    'atr_info': atr_info,
                    'donchian_info': donchian_info
                }
        else:
            print(f"⏳ 未达到移动止盈触发条件，继续等待")

    # 打印调试信息
    print(f"🔍 ATR唐奇安移动止盈检查:")
    print(f"   💰 当前价格: {current_price:.3f}")
    print(f"   📊 入场价格: {entry_price:.3f}")
    print(f"   📈 盈亏比例: {profit_pct:.2%}")

    if atr_info['data_sufficient']:
        atr_trigger_threshold = atr_info['atr'] * STRATEGY_CONFIG['atr_trigger_multiplier']
        print(f"   📊 ATR值: {atr_info['atr']:.3f}")
        print(f"   🎯 ATR触发阈值: {atr_trigger_threshold:.3f}")
        print(f"   🛡️ ATR止损价: {entry_price - (atr_info['atr'] * STRATEGY_CONFIG['atr_stop_loss_multiplier']):.3f}")

    print(f"   📊 当前移动止盈线: {new_trailing_stop:.3f}")

    if market_data and donchian_info['data_sufficient']:
        print(f"   📈 唐奇安上轨: {donchian_info['upper_channel']:.3f}")
        print(f"   📉 唐奇安下轨: {donchian_info['lower_channel']:.3f}")
        print(f"   📊 通道宽度: {donchian_info['channel_width_pct']:.2f}%")

    # 继续持仓
    return {
        'should_exit': False,
        'reason': f'继续持仓 (盈亏: {profit_pct:.2%}, ATR={atr_info.get("atr", 0):.3f})',
        'new_trailing_stop': new_trailing_stop,
        'new_highest_price': new_highest_price,
        'atr_info': atr_info,
        'donchian_info': donchian_info
    }

# ============================================================================
# 数据预热模块（适配回测环境）
# ============================================================================

def get_market_data_for_backtest(C, count=None):
    """
    获取回测环境的市场数据（适配版本）
    模拟源文件的tick数据获取方式

    参数:
        C: ContextInfo对象
        count: 获取数据的数量

    返回:
        dict: 包含价格和成交量数据
    """
    try:
        # 获取当前K线时间（使用原文件的方式）
        bar_date = str(C.get_bar_timetag(C.barpos))

        # 确定获取数据的数量
        if count is None:
            count = max(STRATEGY_CONFIG['atr_period'], STRATEGY_CONFIG['donchian_period']) + 10

        # 首先尝试获取tick数据（模拟源文件的方式）
        # 在回测环境中，我们获取lastPrice和volume来模拟tick数据
        tick_data = C.get_market_data_ex(
            ['lastPrice', 'volume'],
            [C.stock],
            end_time=bar_date,
            period=C.period,
            count=1,
            subscribe=False  # 回测模式不订阅实时数据
        )

        # 同时获取历史OHLC数据用于技术指标计算
        historical_data = C.get_market_data_ex(
            ['close', 'high', 'low', 'volume'],
            [C.stock],
            end_time=bar_date,
            period=C.period,
            count=count,
            subscribe=False  # 回测模式不订阅实时数据
        )

        # 处理tick数据（当前价格和成交量）
        current_price = None
        current_volume = None

        if tick_data and C.stock in tick_data:
            tick_stock_data = tick_data[C.stock]
            print(f"📊 Tick数据结构: {tick_stock_data.shape}, 列数: {len(tick_stock_data.columns)}")

            # 安全提取tick数据 - 检查行数和列数
            if len(tick_stock_data) > 0 and len(tick_stock_data.columns) >= 2:
                current_price = float(tick_stock_data.iloc[0, 0])  # lastPrice
                current_volume = float(tick_stock_data.iloc[0, 1])  # volume
                print(f"✅ 获取tick数据: 价格={current_price:.3f}, 成交量={current_volume}")
            elif len(tick_stock_data) > 0 and len(tick_stock_data.columns) >= 1:
                current_price = float(tick_stock_data.iloc[0, 0])  # 只有价格
                current_volume = 1  # 默认成交量
                print(f"⚠️ 只获取到价格数据: {current_price:.3f}")
            else:
                print(f"⚠️ Tick数据为空或无效")

        # 处理历史数据（用于技术指标计算）
        closes = []
        highs = []
        lows = []
        volumes = []

        if historical_data and C.stock in historical_data:
            stock_data = historical_data[C.stock]
            print(f"📊 历史数据结构: {stock_data.shape}, 列数: {len(stock_data.columns)}")
            print(f"📊 历史数据列名: {list(stock_data.columns)}")

            # 根据实际行数和列数安全提取数据
            if len(stock_data) > 0 and len(stock_data.columns) >= 4:
                closes = list(stock_data.iloc[:, 0])  # close列
                highs = list(stock_data.iloc[:, 1])   # high列
                lows = list(stock_data.iloc[:, 2])    # low列
                volumes = list(stock_data.iloc[:, 3]) # volume列
                print("✅ 获取完整的历史OHLCV数据")
            elif len(stock_data) > 0 and len(stock_data.columns) >= 1:
                closes = list(stock_data.iloc[:, 0])  # 只有一列数据
                highs = closes.copy()   # 使用收盘价作为最高价
                lows = closes.copy()    # 使用收盘价作为最低价
                volumes = [1] * len(closes)  # 默认成交量
                print("⚠️ 历史数据列数不足，使用收盘价填充其他数据")
            else:
                print("⚠️ 历史数据为空或无效")

        # 如果没有获取到当前价格，使用历史数据的最后一个价格
        if current_price is None and closes:
            current_price = closes[-1]
            current_volume = volumes[-1] if volumes else 1
            print(f"📊 使用历史数据作为当前价格: {current_price:.3f}")

        # 检查数据有效性
        if current_price is None:
            print("❌ 无法获取任何价格数据")
            return None

        # 数据有效性检查
        if len(closes) < 1:
            print(f"⚠️ 数据长度不足: {len(closes)}")
            return None

        current_price = closes[-1]
        current_volume = volumes[-1]

        print(f"📊 获取市场数据成功: 价格={current_price:.3f}, 数据长度={len(closes)}")

        return {
            'current_price': current_price,
            'current_volume': current_volume,
            'closes': closes,
            'highs': highs,
            'lows': lows,
            'volumes': volumes,
            'data_count': len(closes)
        }

    except Exception as e:
        print(f"❌ 获取市场数据失败: {e}")
        return None

def check_data_warmup(ContextInfo):
    """
    检查数据预热状态

    参数:
        ContextInfo: QMT上下文对象

    返回:
        dict: 预热状态信息
    """
    try:
        # 如果启用了数据缓冲区且已初始化，则跳过传统数据预热
        if (STRATEGY_CONFIG.get('enable_data_buffer', False) and
            hasattr(ContextInfo, 'data_buffer') and
            getattr(ContextInfo, 'buffer_initialized', False)):

            # 检查数据缓冲区是否有足够数据
            min_periods = STRATEGY_CONFIG.get('min_trading_periods', 20)
            if ContextInfo.data_buffer.is_ready_for_trading(min_periods):
                return {
                    'warmup_complete': True,
                    'reason': f'数据缓冲区已就绪 ({ContextInfo.data_buffer.get_data_count()}根K线)'
                }
            else:
                return {
                    'warmup_complete': False,
                    'reason': f'数据缓冲区预热中 ({ContextInfo.data_buffer.get_data_count()}/{min_periods})'
                }

        # 传统数据预热模式
        if not STRATEGY_CONFIG['enable_data_warmup']:
            return {'warmup_complete': True, 'reason': '数据预热已禁用'}

        # 使用barpos作为当前K线数量
        current_bars = ContextInfo.barpos + 1  # barpos从0开始，所以+1
        required_bars = STRATEGY_CONFIG['data_warmup_bars']

        # 初始化预热状态（仅用于记录）
        if not hasattr(ContextInfo, 'warmup_state'):
            ContextInfo.warmup_state = {
                'start_time': str(ContextInfo.get_bar_timetag(ContextInfo.barpos)),
                'warmup_complete': False
            }
            print(f"📊 开始数据预热: {ContextInfo.warmup_state['start_time']}")

        print(f"📈 数据预热进度: {current_bars}/{required_bars}")

        # 检查是否完成预热
        if current_bars >= required_bars:
            if not ContextInfo.warmup_state['warmup_complete']:
                ContextInfo.warmup_state['warmup_complete'] = True
                print(f"✅ 数据预热完成: 已接收{current_bars}根K线")

            return {
                'warmup_complete': True,
                'reason': f'数据预热完成 ({current_bars}根K线)',
                'bar_count': current_bars
            }

        # 未完成预热
        return {
            'warmup_complete': False,
            'reason': f'数据预热中 ({current_bars}/{required_bars})',
            'bar_count': current_bars,
            'remaining': required_bars - current_bars
        }

    except Exception as e:
        print(f"❌ 数据预热检查失败: {e}")
        return {
            'warmup_complete': False,
            'reason': f'检查异常: {e}',
            'bar_count': 0
        }

# ============================================================================
# 策略状态管理
# ============================================================================

def update_strategy_state(ContextInfo, current_price):
    """
    更新策略状态

    参数:
        ContextInfo: QMT上下文对象
        current_price: 当前价格
    """
    try:
        # 初始化策略状态
        if not hasattr(ContextInfo, 'strategy_state'):
            ContextInfo.strategy_state = {
                'entry_price': 0,
                'highest_price_since_entry': 0,
                'trailing_stop_price': 0,
                'position_status': 'empty',  # empty, long
                'bars_since_entry': 0
            }

        state = ContextInfo.strategy_state

        # 获取当前持仓信息（回测版本）
        account = get_trade_detail_data('test', 'stock', 'account')
        holdings = get_trade_detail_data('test', 'stock', 'position')
        holdings = {i.m_strInstrumentID + '.' + i.m_strExchangeID : i.m_nVolume for i in holdings}
        holding_vol = holdings[ContextInfo.stock] if ContextInfo.stock in holdings else 0

        if holding_vol > 0:
            # 有持仓
            if state['position_status'] == 'empty':
                # 新建仓位
                state['entry_price'] = current_price  # 简化处理，使用当前价格作为入场价
                state['highest_price_since_entry'] = current_price
                state['trailing_stop_price'] = 0
                state['position_status'] = 'long'
                state['bars_since_entry'] = 0
                print(f"📊 新建仓位: 入场价={state['entry_price']:.3f}")

            # 更新最高价
            if current_price > state['highest_price_since_entry']:
                state['highest_price_since_entry'] = current_price
                print(f"📈 更新最高价: {current_price:.3f}")

            state['bars_since_entry'] += 1
        else:
            # 无持仓
            if state['position_status'] == 'long':
                print(f"📊 清空仓位状态")
            state['entry_price'] = 0
            state['highest_price_since_entry'] = 0
            state['trailing_stop_price'] = 0
            state['position_status'] = 'empty'
            state['bars_since_entry'] = 0

    except Exception as e:
        print(f"❌ 策略状态更新异常: {e}")

# ============================================================================
# QMT策略框架函数（基于回测示例）
# ============================================================================

def init(C):
    """
    策略初始化函数（回测版本，集成止盈止损功能）

    功能：设置策略的基本参数和配置信息

    参数：
        C: ContextInfo对象，包含策略运行环境的所有信息
    """
    # 构建完整的股票代码
    C.stock = C.stockcode + '.' + C.market

    # 设置双均线系统的参数
    C.line1 = 10   # 快速移动平均线周期
    C.line2 = 20   # 慢速移动平均线周期

    # 设置回测账户ID
    C.accountid = "testS"

    # 初始化策略状态
    C.strategy_state = {
        'entry_price': 0,
        'highest_price_since_entry': 0,
        'trailing_stop_price': 0,
        'position_status': 'empty',
        'bars_since_entry': 0
    }

    # 初始化K线合成状态
    C.kline_buffer = []
    C.merged_klines = []
    C.bar_count = 0

    # 数据缓冲区功能（Tick数据预热）
    if STRATEGY_CONFIG.get('enable_data_buffer', False):
        print(f"\n📥 数据缓冲区功能已启用，开始初始化...")

        try:
            # 创建数据缓冲区
            buffer_size = STRATEGY_CONFIG.get('buffer_size', 100)
            C.data_buffer = MarketDataBuffer(buffer_size)
            print(f"📊 数据缓冲区初始化完成，容量: {buffer_size}根K线(基于tick合成)")

            # 直接获取历史tick数据填充缓冲区
            print("📥 获取历史tick数据填充缓冲区...")
            history_count = STRATEGY_CONFIG.get('history_data_count', 60)

            success = C.data_buffer.preload_history_data(
                C, C.stock, 'tick', history_count
            )

            if success:
                print(f"✅ 历史tick数据预加载成功，缓冲区包含 {C.data_buffer.get_data_count()} 根K线")
            else:
                print("⚠️ 历史tick数据预加载失败，将使用实时tick数据逐步填充")

            # 标记缓冲区已初始化
            C.buffer_initialized = True

        except Exception as e:
            print(f"⚠️ 数据缓冲区初始化异常: {e}")
            import traceback
            traceback.print_exc()
            # 即使失败也标记为已初始化，避免重复尝试
            C.buffer_initialized = True
    else:
        print(f"\n📊 数据缓冲区功能已禁用，使用传统模式")
        C.buffer_initialized = False

    # 初始化数据预热状态（传统模式，保持兼容）
    C.warmup_state = {
        'bar_count': 0,
        'warmup_complete': False,
        'start_time': None
    }

    print(f"\n🚀 策略初始化完成")
    print(f"📊 交易标的: {C.stock}")
    print(f"📈 快线周期: {C.line1}, 慢线周期: {C.line2}")
    print(f"🔧 K线合成: {'启用' if STRATEGY_CONFIG['enable_kline_merge'] else '禁用'}")
    print(f"🔧 数据缓冲区: {'启用' if STRATEGY_CONFIG['enable_data_buffer'] else '禁用'}")
    print(f"🔧 数据预热: {'启用' if STRATEGY_CONFIG['enable_data_warmup'] else '禁用'}")
    print(f"🎯 ATR周期: {STRATEGY_CONFIG['atr_period']}, 唐奇安周期: {STRATEGY_CONFIG['donchian_period']}")
    print("✅ 初始化完成")

def handlebar(C):
    """
    K线数据处理函数（集成止盈止损版本）

    功能：在每根K线完成时执行集成了止盈止损的交易策略

    参数：
        C: ContextInfo对象，包含当前K线位置、市场数据等信息

    执行流程：
    1. 获取当前K线时间和市场数据
    2. 数据预热检查
    3. K线合成处理
    4. 计算技术指标（双均线、ATR、唐奇安通道）
    5. 获取账户和持仓信息
    6. 执行交易逻辑（买入/止盈止损）
    7. 更新策略状态
    """

    # === 第一步：获取当前K线时间和市场数据 ===

    # 获取当前K线的时间戳并转换为可读格式
    bar_date = str(C.get_bar_timetag(C.barpos))

    # 数据缓冲区模式 vs 传统模式
    use_data_buffer = (STRATEGY_CONFIG.get('enable_data_buffer', False) and
                      hasattr(C, 'data_buffer') and
                      getattr(C, 'buffer_initialized', False))

    if use_data_buffer:
        # === 数据缓冲区模式：使用预加载的数据和实时更新 ===
        print(f"📊 使用数据缓冲区模式")

        # 获取当前tick数据并更新缓冲区
        tick_data = C.get_market_data_ex(['lastPrice', 'volume'], [C.stock], end_time=bar_date, period=C.period, count=1, subscribe=False)

        current_price = None
        current_volume = None

        if tick_data and C.stock in tick_data:
            tick_stock_data = tick_data[C.stock]
            if len(tick_stock_data) > 0 and len(tick_stock_data.columns) >= 2:
                current_price = float(tick_stock_data.iloc[0, 0])  # lastPrice
                current_volume = float(tick_stock_data.iloc[0, 1])  # volume

                # 将新的tick数据添加到缓冲区
                C.data_buffer.add_tick_data(current_price, current_volume, bar_date)

            elif len(tick_stock_data) > 0 and len(tick_stock_data.columns) >= 1:
                current_price = float(tick_stock_data.iloc[0, 0])  # 只有价格
                current_volume = 1  # 默认成交量

                # 将新的tick数据添加到缓冲区
                C.data_buffer.add_tick_data(current_price, current_volume, bar_date)

        # 检查是否有足够数据进行交易
        min_periods = STRATEGY_CONFIG.get('min_trading_periods', 20)
        if not C.data_buffer.is_ready_for_trading(min_periods):
            print(f"📊 数据缓冲区数据不足: {C.data_buffer.get_data_count()}/{min_periods}，等待更多数据")
            return

        # 从缓冲区获取当前价格和成交量
        if current_price is None:
            current_price = C.data_buffer.get_current_price()
            current_volume = C.data_buffer.get_current_volume()

        # 从缓冲区获取历史数据用于计算移动平均线
        buffer_data = C.data_buffer.get_price_data(max(C.line1, C.line2))
        close_list = buffer_data['close']

    else:
        # === 传统模式：实时获取数据 ===

        # 获取tick数据（模拟源文件的方式）
        tick_data = C.get_market_data_ex(['lastPrice', 'volume'], [C.stock], end_time=bar_date, period=C.period, count=1, subscribe=False)

        # 获取历史数据用于计算移动平均线
        historical_data = C.get_market_data_ex(['close'], [C.stock], end_time=bar_date, period=C.period, count=max(C.line1, C.line2), subscribe=False)

        # 处理tick数据
        current_price = None
        current_volume = None

        if tick_data and C.stock in tick_data:
            tick_stock_data = tick_data[C.stock]
            if len(tick_stock_data) > 0 and len(tick_stock_data.columns) >= 2:
                current_price = float(tick_stock_data.iloc[0, 0])  # lastPrice
                current_volume = float(tick_stock_data.iloc[0, 1])  # volume
            elif len(tick_stock_data) > 0 and len(tick_stock_data.columns) >= 1:
                current_price = float(tick_stock_data.iloc[0, 0])  # 只有价格
                current_volume = 1  # 默认成交量

        # 处理历史数据
        close_list = []
        if historical_data and C.stock in historical_data:
            hist_data = historical_data[C.stock]
            if len(hist_data) > 0 and len(hist_data.columns) >= 1:
                close_list = list(hist_data.iloc[:, 0])

    # 如果没有获取到当前价格，使用历史数据的最后一个价格
    if current_price is None and close_list:
        current_price = close_list[-1]
        current_volume = 1

    # 检查数据是否充足
    if current_price is None or len(close_list) < 1:
        print(bar_date, '行情不足 跳过')
        return

    print(f"\n📊 {bar_date} 策略执行")
    print(f"💰 当前价格: {current_price:.3f}, 成交量: {current_volume}")

    # === 第二步：数据预热检查 ===

    warmup_result = check_data_warmup(C)
    print(f"🔥 数据预热: {warmup_result['reason']}")

    if not warmup_result['warmup_complete']:
        print("⏳ 数据预热未完成，跳过交易逻辑")
        return

    # === 第三步：获取完整市场数据用于技术指标计算 ===

    # 获取更多历史数据用于ATR和唐奇安通道计算
    extended_count = max(STRATEGY_CONFIG['atr_period'], STRATEGY_CONFIG['donchian_period']) + 10

    # 尝试获取完整的OHLCV数据
    extended_data = C.get_market_data_ex(['close', 'high', 'low', 'volume'], [C.stock], end_time=bar_date, period=C.period, count=extended_count, subscribe=False)

    if extended_data and C.stock in extended_data:
        stock_data = extended_data[C.stock]
        print(f"📊 扩展数据结构: {stock_data.shape}, 列数: {len(stock_data.columns)}")
        print(f"📊 扩展数据列名: {list(stock_data.columns)}")

        # 根据实际行数和列数安全提取数据
        if len(stock_data) > 0 and len(stock_data.columns) >= 4:
            extended_closes = list(stock_data.iloc[:, 0])
            extended_highs = list(stock_data.iloc[:, 1])
            extended_lows = list(stock_data.iloc[:, 2])
            extended_volumes = list(stock_data.iloc[:, 3])
            print("✅ 获取完整的扩展OHLCV数据")
        elif len(stock_data) > 0 and len(stock_data.columns) >= 1:
            extended_closes = list(stock_data.iloc[:, 0])
            extended_highs = extended_closes.copy()
            extended_lows = extended_closes.copy()
            extended_volumes = [1] * len(extended_closes)
            print("⚠️ 扩展数据列数不足，使用收盘价填充")
        else:
            print("⚠️ 扩展数据为空，使用基础数据")
            extended_closes = close_list
            extended_highs = close_list
            extended_lows = close_list
            extended_volumes = [1] * len(close_list)
    else:
        print("❌ 无法获取扩展数据，使用基础数据")
        extended_closes = close_list
        extended_highs = close_list
        extended_lows = close_list
        extended_volumes = [1] * len(close_list)

    # K线合成处理（如果启用）
    if STRATEGY_CONFIG['enable_kline_merge'] and len(extended_volumes) > 0:
        # 处理K线合成
        process_kline_merge(C, bar_date, current_price, current_volume)

        # 尝试使用合成K线数据
        merged_data = get_merged_kline_data(C)
        if merged_data and merged_data['count'] > 0:
            print(f"📈 使用合成K线数据: {merged_data['count']}根K线")
            # 使用合成数据
            extended_closes = merged_data['closes']
            extended_highs = merged_data['highs']
            extended_lows = merged_data['lows']
            current_price = merged_data['closes'][-1]  # 更新当前价格

    # === 第四步：计算技术指标 ===

    # 检查数据充足性
    if len(close_list) < max(C.line1, C.line2):
        print(f'{bar_date} 行情不足 跳过 (需要{max(C.line1, C.line2)}根，实际{len(close_list)}根)')
        return

    # 计算快速和慢速移动平均线
    line1_mean = round(np.mean(close_list[-C.line1:]), 2)
    line2_mean = round(np.mean(close_list[-C.line2:]), 2)

    print(f"📊 技术指标: 快线{line1_mean} 慢线{line2_mean}")

    # 计算ATR和唐奇安通道（用于止盈止损）
    atr_info = calculate_atr(extended_highs, extended_lows, extended_closes)
    donchian_info = calculate_donchian_channel(extended_highs, extended_lows)

    print(f"🔧 ATR: {atr_info['atr']:.3f} ({'充足' if atr_info['data_sufficient'] else '不足'})")
    if donchian_info['data_sufficient']:
        print(f"📈 唐奇安通道: 上轨{donchian_info['upper_channel']:.3f} 下轨{donchian_info['lower_channel']:.3f}")

    # === 第五步：获取账户和持仓信息 ===

    # 获取回测账户信息
    account = get_trade_detail_data('test', 'stock', 'account')
    if not account:
        print(f"{bar_date} 无法获取账户信息，跳过")
        return

    account = account[0]
    available_cash = int(account.m_dAvailable)

    # 获取当前持仓信息
    holdings = get_trade_detail_data('test', 'stock', 'position')
    holdings = {i.m_strInstrumentID + '.' + i.m_strExchangeID : i.m_nVolume for i in holdings}
    holding_vol = holdings[C.stock] if C.stock in holdings else 0

    print(f"💼 账户状态: 可用资金{available_cash:.2f}, 持仓{holding_vol}股")

    # === 第六步：更新策略状态 ===

    update_strategy_state(C, current_price)
    state = C.strategy_state

    # === 第七步：执行交易逻辑 ===

    if holding_vol == 0:
        # 无持仓，检查买入条件：金叉信号
        if line1_mean > line2_mean:
            # 计算买入数量
            vol = int(available_cash * STRATEGY_CONFIG['max_position_ratio'] / current_price / 100) * 100

            if vol >= 100 and available_cash > STRATEGY_CONFIG['min_cash_reserve']:
                # 执行买入操作
                passorder(23, 1101, C.accountid, C.stock, 5, -1, vol, C)

                print(f"✅ {bar_date} 买入信号触发")
                print(f"   📊 买入条件: 快线{line1_mean} > 慢线{line2_mean}")
                print(f"   💰 买入数量: {vol}股")
                print(f"   💵 预计金额: {vol * current_price:.2f}")

                # 在图表上标记买入点位
                C.draw_text(1, 1, '买')
            else:
                print(f"⚠️ 买入条件满足但资金不足: 需要{vol}股，可用资金{available_cash}")

    else:
        # 有持仓，检查止盈止损条件
        print(f"📊 持仓监控: {holding_vol}股")

        # 获取策略状态
        entry_price = state['entry_price']
        highest_price = state['highest_price_since_entry']
        trailing_stop = state['trailing_stop_price']

        print(f"📊 策略状态: 入场价{entry_price:.3f}, 最高价{highest_price:.3f}, 移动止盈{trailing_stop:.3f}")

        # 检查止盈止损条件
        market_data_dict = {
            'highs': extended_highs,
            'lows': extended_lows,
            'closes': extended_closes
        }
        exit_result = check_exit_conditions_donchian(
            C, current_price, entry_price, highest_price, trailing_stop, market_data_dict
        )

        if exit_result['should_exit']:
            # 触发止盈止损，执行卖出
            passorder(24, 1101, C.accountid, C.stock, 5, -1, holding_vol, C)

            print(f"🎯 {bar_date} 止盈止损触发")
            print(f"   📊 平仓原因: {exit_result['reason']}")
            print(f"   💰 卖出数量: {holding_vol}股")

            # 在图表上标记卖出点位
            C.draw_text(1, 1, '卖')
        else:
            # 继续持仓，更新策略状态
            print(f"📈 继续持仓: {exit_result['reason']}")

            # 更新移动止盈价格
            if 'new_trailing_stop' in exit_result:
                C.strategy_state['trailing_stop_price'] = exit_result['new_trailing_stop']
            if 'new_highest_price' in exit_result:
                C.strategy_state['highest_price_since_entry'] = exit_result['new_highest_price']

        # 检查传统死叉信号作为备用平仓条件
        if line1_mean < line2_mean:
            print(f"⚠️ 检测到死叉信号: 快线{line1_mean} < 慢线{line2_mean}")
            print(f"   当前使用ATR+唐奇安止盈止损，死叉信号仅作参考")

    print(f"📊 {bar_date} 策略执行完成\n" + "="*60)

# ============================================================================
# 辅助函数
# ============================================================================

# 注意：timetag_to_datetime 是QMT内置函数，无需重新定义

# ============================================================================
# 策略说明和使用指南
# ============================================================================

"""
策略使用指南
============

1. 策略概述：
   - 基于双均线交叉的趋势跟踪策略
   - 集成ATR动态止损和唐奇安通道移动止盈
   - 支持K线合成功能，提高信号质量
   - 适用于回测环境，已优化回测API调用

2. 核心功能：
   ✅ 双均线交叉买入信号（10日线上穿20日线）
   ✅ ATR动态止损（3倍ATR止损）
   ✅ 唐奇安通道移动止盈（4倍ATR触发，跟踪下轨）
   ✅ K线合成（2根合成1根，可配置）
   ✅ 增量成交量处理
   ✅ 数据预热机制
   ✅ 策略状态持久化

3. 参数配置：
   - 快线周期：10日
   - 慢线周期：20日
   - ATR周期：14日
   - 唐奇安通道周期：20日
   - ATR止损倍数：3.0
   - ATR移动止盈触发倍数：4.0
   - 最大仓位比例：70%
   - K线合成比例：2:1

4. 风险控制：
   - 固定止损：0.5%（ATR数据不足时使用）
   - 动态止损：3倍ATR
   - 移动止盈：唐奇安下轨跟踪
   - 最大仓位限制：70%
   - 最小资金保留：100元

5. 适用场景：
   - 趋势性较强的股票
   - 波动率适中的标的
   - 中长期持仓策略
   - 回测验证和参数优化

6. 注意事项：
   - 本策略适用于回测环境
   - 实盘使用前请充分回测验证
   - 建议根据具体标的调整参数
   - 关注市场环境变化，适时调整策略

7. 性能监控：
   - 关注ATR数据充足性
   - 监控唐奇安通道宽度
   - 跟踪移动止盈触发频率
   - 观察K线合成效果

8. 优化建议：
   - 根据标的特性调整ATR倍数
   - 优化唐奇安通道周期
   - 调整K线合成比例
   - 考虑加入成交量过滤条件

使用方法：
1. 在QMT回测环境中加载此策略文件
2. 设置回测参数（时间范围、初始资金等）
3. 选择目标股票代码
4. 运行回测并分析结果
5. 根据回测结果优化参数设置

技术支持：
- 基于QMT平台的handlebar框架
- 集成实盘模块的核心算法
- 适配回测环境的API调用
- 完整的错误处理和日志输出

版本信息：
- 版本：1.0
- 创建日期：2025年
- 基于：QMT止盈止损下单模块 + 回测示例框架
- 适用环境：QMT回测平台
"""
