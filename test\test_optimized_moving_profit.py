#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化移动止盈方案测试程序
测试动态ATR周期调整、加速移动机制、最小移动限制等优化功能
"""

import numpy as np
import talib
import sys
import os

# 添加框架路径
sys.path.append(os.path.join(os.path.dirname(__file__), '框架'))

# 导入策略类
from sk线 import CMFBIASDivergenceStrategy

def create_test_data():
    """创建测试数据"""
    # 创建模拟价格数据
    np.random.seed(42)
    
    # 基础价格
    base_price = 100.0
    days = 50
    
    # 生成价格序列 - 模拟趋势市场
    trend_factor = np.linspace(0, 0.3, days)  # 30%的上涨趋势
    noise = np.random.normal(0, 0.02, days)   # 2%的随机波动
    
    prices = base_price * (1 + trend_factor + noise)
    
    # 生成OHLC数据
    highs = prices * (1 + np.random.uniform(0.005, 0.02, days))
    lows = prices * (1 - np.random.uniform(0.005, 0.02, days))
    closes = prices
    
    # 生成成交量
    volumes = np.random.uniform(1000000, 5000000, days)
    
    return highs, lows, closes, volumes

def test_optimized_moving_profit():
    """测试优化移动止盈方案"""
    print("=" * 80)
    print("🚀 优化移动止盈方案测试")
    print("=" * 80)
    
    # 创建策略实例
    strategy = CMFBIASDivergenceStrategy()
    
    # 创建测试数据
    highs, lows, closes, volumes = create_test_data()
    
    print(f"📊 测试数据: {len(closes)}天价格数据")
    print(f"   起始价格: {closes[0]:.3f}")
    print(f"   结束价格: {closes[-1]:.3f}")
    print(f"   总涨幅: {(closes[-1] / closes[0] - 1) * 100:.2f}%")
    print()
    
    # 测试不同市场状态下的移动止盈计算
    test_scenarios = [
        {"name": "震荡市场数据", "data_slice": slice(0, 25)},
        {"name": "趋势市场数据", "data_slice": slice(25, 50)},
        {"name": "完整数据", "data_slice": slice(0, 50)}
    ]
    
    for scenario in test_scenarios:
        print(f"🔍 测试场景: {scenario['name']}")
        print("-" * 60)
        
        # 获取数据切片
        data_slice = scenario['data_slice']
        test_highs = highs[data_slice]
        test_lows = lows[data_slice]
        test_closes = closes[data_slice]
        
        if len(test_closes) < 25:
            print("   ⚠️ 数据不足，跳过测试")
            print()
            continue
        
        # 计算优化移动止盈风控
        moving_profit_info = strategy.calculate_moving_profit_control(
            test_highs, test_lows, test_closes
        )
        
        # 显示结果
        print(f"   📊 市场模式: {moving_profit_info.get('市场模式', '未知')}")
        print(f"   📈 ATR周期: {moving_profit_info.get('ATR_周期', 0)}日")
        print(f"   🌊 ATR百分比: {moving_profit_info.get('ATR_百分比', 0):.3f}%")
        print(f"   🎯 ATR倍数: {moving_profit_info.get('ATR_倍数', 0):.2f}")
        print(f"   💰 止损距离: {moving_profit_info.get('止损距离', 0):.3f}%")
        print(f"   🎯 初始止盈距离: {moving_profit_info.get('初始止盈距离', 0):.3f}%")
        print(f"   📈 标准移动触发: {moving_profit_info.get('标准移动触发距离', 0):.3f}%")
        print(f"   🚀 加速移动触发: {moving_profit_info.get('加速移动触发距离', 0):.3f}%")
        print(f"   🎯 最小移动幅度: {moving_profit_info.get('最小移动幅度', 0):.1f}%")
        print(f"   📊 趋势强度: {moving_profit_info.get('趋势强度', 0):.2f}%")
        print(f"   🔧 计算模式: {moving_profit_info.get('计算模式', '未知')}")
        
        # 优化功能状态
        加速移动 = moving_profit_info.get('加速移动', False)
        最小移动限制 = moving_profit_info.get('最小移动限制', False)
        print(f"   ✨ 优化功能: 加速移动{'✅' if 加速移动 else '❌'} | 最小限制{'✅' if 最小移动限制 else '❌'}")
        print()

def test_market_state_identification():
    """测试市场状态识别功能"""
    print("=" * 80)
    print("🎯 市场状态识别测试")
    print("=" * 80)
    
    strategy = CMFBIASDivergenceStrategy()
    
    # 测试不同的市场状态
    test_cases = [
        {
            "name": "强趋势市场",
            "ma20": 100.0,
            "current_price": 110.0,  # 价格高于均线10%
            "expected_mode": "趋势市场"
        },
        {
            "name": "弱趋势市场", 
            "ma20": 100.0,
            "current_price": 102.0,  # 价格高于均线2%
            "expected_mode": "趋势市场"
        },
        {
            "name": "震荡市场",
            "ma20": 100.0,
            "current_price": 98.0,   # 价格低于均线
            "expected_mode": "震荡市场"
        }
    ]
    
    for case in test_cases:
        print(f"🔍 测试: {case['name']}")
        print(f"   20日均线: {case['ma20']:.2f}")
        print(f"   当前价格: {case['current_price']:.2f}")
        
        # 构造测试数据
        closes = np.full(25, case['ma20'])  # 前20天均为均线价格
        closes = np.append(closes, [case['current_price']] * 5)  # 最后5天为当前价格
        
        highs = closes * 1.01
        lows = closes * 0.99
        
        # 计算移动止盈风控
        result = strategy.calculate_moving_profit_control(highs, lows, closes)
        
        actual_mode = result.get('市场模式', '未知')
        atr_period = result.get('ATR_周期', 0)
        atr_multiplier = result.get('ATR_倍数', 0)
        trend_strength = result.get('趋势强度', 0)
        
        print(f"   📊 识别结果: {actual_mode}")
        print(f"   📈 ATR周期: {atr_period}日")
        print(f"   🎯 ATR倍数: {atr_multiplier:.2f}")
        print(f"   💪 趋势强度: {trend_strength:.2f}%")
        
        # 验证结果
        if actual_mode == case['expected_mode']:
            print(f"   ✅ 识别正确")
        else:
            print(f"   ❌ 识别错误，期望: {case['expected_mode']}")
        print()

def test_acceleration_mechanism():
    """测试加速移动机制"""
    print("=" * 80)
    print("🚀 加速移动机制测试")
    print("=" * 80)
    
    strategy = CMFBIASDivergenceStrategy()
    
    # 创建模拟强趋势数据
    base_price = 100.0
    days = 30
    
    # 生成强上涨趋势
    prices = base_price * (1 + np.linspace(0, 0.15, days))  # 15%上涨
    highs = prices * 1.005
    lows = prices * 0.995
    closes = prices
    
    print(f"📊 强趋势测试数据:")
    print(f"   起始价格: {closes[0]:.3f}")
    print(f"   结束价格: {closes[-1]:.3f}")
    print(f"   总涨幅: {(closes[-1] / closes[0] - 1) * 100:.2f}%")
    print()
    
    # 计算移动止盈参数
    result = strategy.calculate_moving_profit_control(highs, lows, closes)
    
    标准触发距离 = result.get('标准移动触发距离', 0)
    加速触发距离 = result.get('加速移动触发距离', 0)
    最小移动幅度 = result.get('最小移动幅度', 0)
    
    print(f"🎯 移动触发参数:")
    print(f"   标准移动触发距离: {标准触发距离:.3f}%")
    print(f"   加速移动触发距离: {加速触发距离:.3f}%")
    print(f"   最小移动幅度: {最小移动幅度:.1f}%")
    print()
    
    # 模拟移动止盈过程
    entry_price = closes[0]
    initial_profit_distance = result.get('初始止盈距离', 0)
    initial_profit_price = entry_price * (1 + initial_profit_distance / 100)
    
    print(f"📈 移动止盈模拟:")
    print(f"   入场价格: {entry_price:.3f}")
    print(f"   初始止盈价格: {initial_profit_price:.3f}")
    print()
    
    # 检查不同价格水平的移动模式
    test_prices = [
        initial_profit_price * 1.02,  # 小幅超过初始止盈
        initial_profit_price * 1.05,  # 中等超过
        initial_profit_price * 1.10,  # 大幅超过，应触发加速
    ]
    
    for i, test_price in enumerate(test_prices):
        price_gain = test_price - initial_profit_price
        三个移动间距 = 3 * (标准触发距离 / 100 * entry_price)
        is_accelerated = price_gain >= 三个移动间距
        
        当前触发距离 = 加速触发距离 if is_accelerated else 标准触发距离
        移动模式 = "加速移动" if is_accelerated else "标准移动"
        
        print(f"   测试价格 {i+1}: {test_price:.3f}")
        print(f"     价格增幅: {price_gain:.3f} (三个间距: {三个移动间距:.3f})")
        print(f"     移动模式: {移动模式}")
        print(f"     触发距离: {当前触发距离:.3f}%")
        print()

if __name__ == "__main__":
    try:
        # 运行所有测试
        test_optimized_moving_profit()
        test_market_state_identification()
        test_acceleration_mechanism()
        
        print("=" * 80)
        print("✅ 优化移动止盈方案测试完成")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
