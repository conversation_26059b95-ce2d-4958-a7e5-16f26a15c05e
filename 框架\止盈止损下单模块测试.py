# coding: utf-8
"""
止盈止损和下单模块测试
从6sk线.py中提取的核心交易模块，用于独立测试

主要功能：
1. 移动止盈止损逻辑
2. 买入/卖出订单执行
3. 订单管理和撤单
4. 风险控制机制

使用方法：
1. 运行测试用例：python 止盈止损下单模块测试.py
2. 导入模块使用：from 止盈止损下单模块测试 import *
"""

import numpy as np
import pandas as pd
import datetime
import time
from typing import Dict, List, Tuple, Optional

# ============================================================================
# 模拟QMT环境 - 用于测试
# ============================================================================

class MockQMTEnvironment:
    """模拟QMT交易环境"""
    
    def __init__(self):
        self.orders = []  # 模拟订单列表
        self.account_cash = 100000.0  # 模拟账户资金
        self.positions = {}  # 模拟持仓 {stock: volume}
        self.order_id_counter = 1000
        
    def passorder(self, op_type, order_type, account, stock, price_type, price, volume, strategy, action, msg, context):
        """模拟QMT下单函数"""
        order_id = f"ORD_{self.order_id_counter}"
        self.order_id_counter += 1
        
        order = {
            'order_id': order_id,
            'op_type': op_type,  # 23=买入, 24=卖出
            'stock': stock,
            'price': price,
            'volume': volume,
            'status': 50,  # 50=已报待成交
            'submit_time': datetime.datetime.now(),
            'msg': msg
        }
        
        self.orders.append(order)
        print(f"📤 模拟下单: {msg}")
        print(f"   订单ID: {order_id}")
        
        # 模拟立即成交（简化）
        if op_type == 23:  # 买入
            cost = price * volume
            if self.account_cash >= cost:
                self.account_cash -= cost
                self.positions[stock] = self.positions.get(stock, 0) + volume
                order['status'] = 52  # 全部成交
                print(f"✅ 模拟买入成交: {volume}股@{price:.3f}")
            else:
                order['status'] = 53  # 已撤销（资金不足）
                print(f"❌ 模拟买入失败: 资金不足")
        
        elif op_type == 24:  # 卖出
            if self.positions.get(stock, 0) >= volume:
                self.account_cash += price * volume
                self.positions[stock] = self.positions.get(stock, 0) - volume
                order['status'] = 52  # 全部成交
                print(f"✅ 模拟卖出成交: {volume}股@{price:.3f}")
            else:
                order['status'] = 53  # 已撤销（持仓不足）
                print(f"❌ 模拟卖出失败: 持仓不足")
        
        return order_id
    
    def get_trade_detail_data(self, account, data_type, detail_type):
        """模拟获取交易详情"""
        if detail_type == 'account':
            # 返回账户信息
            class MockAccount:
                def __init__(self, cash):
                    self.m_dAvailable = cash
            return [MockAccount(self.account_cash)]
        
        elif detail_type == 'position':
            # 返回持仓信息
            positions = []
            for stock, volume in self.positions.items():
                if volume > 0:
                    class MockPosition:
                        def __init__(self, stock, volume):
                            parts = stock.split('.')
                            self.m_strInstrumentID = parts[0] if len(parts) > 0 else stock
                            self.m_strExchangeID = parts[1] if len(parts) > 1 else 'SZ'
                            self.m_nVolume = volume
                    positions.append(MockPosition(stock, volume))
            return positions
        
        elif detail_type == 'order':
            # 返回订单信息
            orders = []
            for order in self.orders[-10:]:  # 只返回最近10个订单
                class MockOrder:
                    def __init__(self, order_data):
                        self.m_strOrderSysID = order_data['order_id']
                        self.m_nOrderStatus = order_data['status']
                        self.m_nOffsetFlag = 1 if order_data['op_type'] == 23 else 2
                        parts = order_data['stock'].split('.')
                        self.m_strInstrumentID = parts[0] if len(parts) > 0 else order_data['stock']
                        self.m_strExchangeID = parts[1] if len(parts) > 1 else 'SZ'
                        self.m_nVolumeTotalOriginal = order_data['volume']
                        self.m_nVolumeTraded = order_data['volume'] if order_data['status'] == 52 else 0
                orders.append(MockOrder(order))
            return orders
        
        return []
    
    def cancel(self, order_id, account, market, context):
        """模拟撤单"""
        for order in self.orders:
            if order['order_id'] == order_id and order['status'] in [48, 49, 50, 51]:
                order['status'] = 53  # 已撤销
                print(f"✅ 模拟撤单成功: {order_id}")
                return True
        print(f"❌ 模拟撤单失败: {order_id}")
        return False
    
    def get_last_order_id(self, account, market, order_type):
        """模拟获取最后订单ID"""
        if self.orders:
            return self.orders[-1]['order_id']
        return '-1'

# 全局模拟环境实例
mock_env = MockQMTEnvironment()

# 模拟QMT函数
def passorder(op_type, order_type, account, stock, price_type, price, volume, strategy, action, msg, context):
    return mock_env.passorder(op_type, order_type, account, stock, price_type, price, volume, strategy, action, msg, context)

def get_trade_detail_data(account, data_type, detail_type):
    return mock_env.get_trade_detail_data(account, data_type, detail_type)

def cancel(order_id, account, market, context):
    return mock_env.cancel(order_id, account, market, context)

def get_last_order_id(account, market, order_type):
    return mock_env.get_last_order_id(account, market, order_type)

# ============================================================================
# 模拟策略上下文
# ============================================================================

class MockContext:
    """模拟策略上下文对象"""
    
    def __init__(self, stock='000001.SZ'):
        # 基本信息
        self.stock = stock
        self.acct = 'TEST_ACCOUNT'
        
        # 持仓状态
        self.position = 0
        self.entry_price = 0
        self.exit_price = 0
        self.bars_since_entry = 0
        
        # 移动止盈相关
        self.highest_price_since_entry = 0
        self.trailing_stop_price = 0
        self.use_trailing_stop = True
        self.trailing_stop_ratio = 0.02
        
        # 风险控制参数
        self.stop_loss_pct = 0.05
        self.take_profit_pct = 0.10
        self.固定止损 = 0.5
        
        # 挂单偏移参数
        self.buy_hang_offset_ratio = 0.002
        self.sell_hang_offset_ratio = 0.002
        
        # 统计信息
        self.total_trades = 0
        self.successful_trades = 0
        self.failed_trades = 0
        self.last_trade_time = None

# ============================================================================
# 移动止盈风控计算模块
# ============================================================================

def calculate_moving_profit_control(highs, lows, closes):
    """
    计算移动止盈风控 - 基于动态ATR的优化移动止盈机制
    
    参数:
        highs: 最高价数组
        lows: 最低价数组  
        closes: 收盘价数组
        
    返回:
        dict: 优化移动止盈风控信息
    """
    try:
        ATR_PERIOD = 21  # 统一使用21日ATR
        base_multiplier = 5.0  # 初始止盈倍数改为5倍
        
        if len(closes) < ATR_PERIOD + 5:
            return {
                'ATR_周期': ATR_PERIOD,
                'ATR值': 0.05,
                '止损距离': 0.25,
                '初始止盈距离': 0.25,
                '移动触发距离': 0.25,
                '加速移动距离': 0.175,
                '最小移动幅度': 0.1,
                '当前价格': closes[-1] if len(closes) > 0 else 100,
                '市场模式': '统一21日ATR',
                '波动率区间': '正常波动区',
                '计算模式': '优化移动止盈方案(数据不足)'
            }
        
        # 计算ATR
        TR_值 = calculate_true_range(highs, lows, closes)
        ATR_值 = np.mean(TR_值[-ATR_PERIOD:])  # 简化的ATR计算
        当前ATR = ATR_值
        当前价格 = closes[-1]
        
        # 计算ATR百分比
        ATR_百分比 = (当前ATR / 当前价格 * 100) if 当前价格 > 0 else 0.05
        
        # 根据优化移动止盈方案计算距离
        止损距离 = ATR_百分比 * base_multiplier
        初始止盈距离 = ATR_百分比 * base_multiplier
        
        # 加速移动机制
        加速倍数 = base_multiplier * 0.7
        加速移动触发距离 = ATR_百分比 * 加速倍数
        
        # 最小移动限制
        最小移动幅度 = 0.1
        
        # 合理性检查
        最小距离 = 0.15
        最大距离 = 5.0
        
        止损距离 = max(min(止损距离, 最大距离), 最小距离)
        初始止盈距离 = max(min(初始止盈距离, 最大距离), 最小距离)
        
        # 波动率区间判断
        if ATR_百分比 < 0.3:
            波动率区间 = '低波动区'
        elif ATR_百分比 < 0.8:
            波动率区间 = '正常波动区'
        elif ATR_百分比 < 1.5:
            波动率区间 = '高波动区'
        else:
            波动率区间 = '极高波动区'
            
        return {
            'ATR_周期': ATR_PERIOD,
            'ATR值': 当前ATR,
            'ATR_百分比': ATR_百分比,
            '止损距离': 止损距离,
            '初始止盈距离': 初始止盈距离,
            '标准移动触发距离': 初始止盈距离,
            '加速移动触发距离': 加速移动触发距离,
            '最小移动幅度': 最小移动幅度,
            '当前价格': 当前价格,
            '市场模式': '统一21日ATR',
            '波动率区间': 波动率区间,
            '计算模式': '优化移动止盈方案'
        }
        
    except Exception as e:
        print(f"⚠️ 移动止盈风控计算失败: {e}")
        return {
            '止损距离': 0.23,
            '初始止盈距离': 0.23,
            '移动触发距离': 0.23,
            '当前价格': closes[-1] if len(closes) > 0 else 100,
            '波动率区间': '正常波动区',
            '计算模式': '移动止盈方案(默认)'
        }

def calculate_true_range(highs, lows, closes):
    """计算真实波动范围（True Range）"""
    try:
        highs = np.asarray(highs, dtype=np.float64)
        lows = np.asarray(lows, dtype=np.float64)
        closes = np.asarray(closes, dtype=np.float64)
        
        data_len = len(closes)
        if data_len < 2:
            return np.zeros(data_len)
        
        # HL = HIGH - LOW
        hl = highs - lows
        
        # HC = ABS(HIGH - REF(CLOSE,1))
        hc = np.zeros(data_len, dtype=np.float64)
        hc[0] = hl[0]
        for i in range(1, data_len):
            hc[i] = abs(highs[i] - closes[i-1])
        
        # LC = ABS(REF(CLOSE,1) - LOW)
        lc = np.zeros(data_len, dtype=np.float64)
        lc[0] = hl[0]
        for i in range(1, data_len):
            lc[i] = abs(closes[i-1] - lows[i])
        
        # TR = MAX(MAX(HL, HC), LC)
        tr = np.maximum(np.maximum(hl, hc), lc)
        
        return tr

    except Exception as e:
        print(f"❌ True Range计算失败: {e}")
        return np.zeros(len(closes))

# ============================================================================
# 止盈止损检查模块
# ============================================================================

def check_exit_conditions(C, current_price, 移动止盈_info=None):
    """
    检查平仓条件 - 基于实际市场波动的动态止盈止损 + 移动止盈

    参数:
        C: 策略上下文
        current_price: 当前价格
        移动止盈_info: 移动止盈风控信息

    返回:
        dict: 平仓决策结果
    """
    if C.position == 0:
        return {'should_exit': False, 'reason': '无持仓'}

    # 计算盈亏比例
    profit_pct = (current_price - C.entry_price) / C.entry_price

    # 获取风控参数
    if 移动止盈_info is None:
        移动止盈_info = {
            '波动率区间': '正常波动区',
            'ATR_百分比': 0.23,
            '止损距离': 0.23,
            '初始止盈距离': 0.23,
            '移动触发距离': 0.23
        }

    波动率区间 = 移动止盈_info.get('波动率区间', '正常波动区')
    ATR_百分比 = 移动止盈_info.get('ATR_百分比', 0.23)
    止损距离 = 移动止盈_info.get('止损距离', 0.23)
    初始止盈距离 = 移动止盈_info.get('初始止盈距离', 0.23)
    移动触发距离 = 移动止盈_info.get('移动触发距离', 0.23)

    # 移动止盈功能
    trailing_stop_triggered = False
    trailing_stop_reason = ""

    if C.use_trailing_stop and C.position == 1:
        # 更新入场后最高价格
        if current_price > C.highest_price_since_entry:
            C.highest_price_since_entry = current_price

        # 优化移动止盈方案实现
        初始止盈价格 = C.entry_price * (1 + 初始止盈距离 / 100)

        # 获取优化参数
        标准移动触发距离 = 移动止盈_info.get('标准移动触发距离', 移动触发距离)
        加速移动触发距离 = 移动止盈_info.get('加速移动触发距离', 移动触发距离 * 0.65)
        最小移动幅度 = 移动止盈_info.get('最小移动幅度', 0.1)

        # 检查是否达到启动条件
        if current_price >= 初始止盈价格:
            # 移动止盈已启动
            price_gain_from_initial = current_price - 初始止盈价格

            # 加速移动机制
            三个移动间距 = 3 * (标准移动触发距离 / 100 * C.entry_price)
            is_accelerated = price_gain_from_initial >= 三个移动间距

            # 选择移动触发距离
            if is_accelerated:
                当前移动触发距离 = 加速移动触发距离
                移动模式 = "加速移动"
            else:
                当前移动触发距离 = 标准移动触发距离
                移动模式 = "标准移动"

            # 最小移动限制
            if 当前移动触发距离 < 最小移动幅度:
                当前移动触发距离 = 最小移动幅度
                移动模式 += "(最小限制)"

            移动触发单位 = 当前移动触发距离 / 100 * C.entry_price

            if 移动触发单位 > 0:
                # 计算应该移动的次数
                move_times = int(price_gain_from_initial / 移动触发单位)

                # 计算新的止盈线位置
                initial_stop_price = C.entry_price + (初始止盈距离 / 100 * C.entry_price)
                new_trailing_stop_price = initial_stop_price + (move_times * 移动触发单位)

                # 移动止盈线只能向上移动
                if new_trailing_stop_price > C.trailing_stop_price:
                    C.trailing_stop_price = new_trailing_stop_price

                # 检查是否触发移动止盈
                if current_price <= C.trailing_stop_price:
                    trailing_stop_triggered = True
                    actual_profit = (C.trailing_stop_price - C.entry_price) / C.entry_price * 100
                    trailing_stop_reason = f"优化移动止盈触发({移动模式},移动{move_times}次,保护{actual_profit:.1f}%利润)"
        else:
            # 尚未达到初始止盈价格
            C.trailing_stop_price = 0

    # 平仓条件检查
    market_based_take_profit_exit = profit_pct >= (初始止盈距离 / 100)
    market_based_stop_loss_exit = profit_pct <= -(止损距离 / 100)
    fixed_stop_loss_exit = profit_pct <= -(C.固定止损 / 100)

    print(f"🔍 平仓条件检查:")
    print(f"   💰 当前价格: {current_price:.3f}")
    print(f"   📊 入场价格: {C.entry_price:.3f}")
    print(f"   📈 盈亏比例: {profit_pct:.2%}")
    print(f"   🌊 波动率区间: {波动率区间}")
    print(f"   🎯 市场动态止盈: {market_based_take_profit_exit} (目标: {初始止盈距离:.2f}%)")
    print(f"   🛡️ 市场动态止损: {market_based_stop_loss_exit} (目标: {止损距离:.2f}%)")
    print(f"   ⚠️ 固定止损保护: {fixed_stop_loss_exit} (目标: {C.固定止损:.1f}%)")
    print(f"   ⚡ 移动止盈触发: {trailing_stop_triggered}")

    # 执行平仓决策
    if fixed_stop_loss_exit:
        return {'should_exit': True, 'reason': '固定止损保护平仓', 'price': current_price}
    elif market_based_stop_loss_exit:
        return {'should_exit': True, 'reason': f'市场动态止损平仓({波动率区间},{止损距离:.2f}%)', 'price': current_price}
    elif trailing_stop_triggered:
        return {'should_exit': True, 'reason': trailing_stop_reason, 'price': current_price}
    elif market_based_take_profit_exit:
        return {'should_exit': True, 'reason': f'市场动态止盈平仓({波动率区间},{初始止盈距离:.2f}%)', 'price': current_price}
    else:
        return {'should_exit': False, 'reason': f'继续持仓 (盈亏: {profit_pct:.2%})'}

# ============================================================================
# 订单管理模块
# ============================================================================

def check_has_pending_orders(C, order_type='ALL'):
    """
    快速检查是否有未处理的订单

    参数:
        C: 策略上下文
        order_type: 订单类型 ('BUY', 'SELL', 'ALL')

    返回:
        bool: True表示有未处理订单
    """
    try:
        orders = get_trade_detail_data(C.acct, 'stock', 'order')
        if not orders or len(orders) == 0:
            return False

        # 检查是否有未完成的订单
        for order_obj in orders:
            try:
                order_status = getattr(order_obj, 'm_nOrderStatus', -1)
                order_op_type = getattr(order_obj, 'm_nOffsetFlag', -1)
                order_stock = getattr(order_obj, 'm_strInstrumentID', '') + '.' + getattr(order_obj, 'm_strExchangeID', '')

                # 检查是否为当前股票的未完成订单
                if order_stock == C.stock and order_status in [48, 49, 50, 51]:
                    # 检查订单类型
                    if order_type == 'ALL':
                        return True
                    elif order_type == 'BUY' and order_op_type == 1:
                        return True
                    elif order_type == 'SELL' and order_op_type == 2:
                        return True
            except Exception as e:
                continue

        return False

    except Exception as e:
        print(f"❌ 检查未处理订单异常: {e}")
        return False

def cancel_pending_orders(C, order_type='ALL'):
    """
    撤销未成交的挂单

    参数:
        C: 策略上下文
        order_type: 订单类型 ('BUY', 'SELL', 'ALL')

    返回:
        dict: 撤单结果统计
    """
    cancel_stats = {
        'total_checked': 0,
        'cancelled_count': 0,
        'cancel_success': 0,
        'cancel_failed': 0,
        'errors': []
    }

    try:
        orders = get_trade_detail_data(C.acct, 'stock', 'order')
        if not orders or len(orders) == 0:
            print("📋 没有找到待处理的委托")
            return cancel_stats

        print(f"📋 查询到 {len(orders)} 个委托")
        cancel_stats['total_checked'] = len(orders)

        # 遍历所有委托，找到需要撤销的
        for order_obj in orders:
            try:
                order_status = getattr(order_obj, 'm_nOrderStatus', -1)
                order_op_type = getattr(order_obj, 'm_nOffsetFlag', -1)
                order_stock = getattr(order_obj, 'm_strInstrumentID', '') + '.' + getattr(order_obj, 'm_strExchangeID', '')
                order_id = getattr(order_obj, 'm_strOrderSysID', '')

                # 判断是否需要撤销
                should_cancel = False
                cancel_reason = ""

                # 检查委托状态
                if order_status in [48, 49, 50, 51]:
                    should_cancel = True
                    cancel_reason = f"委托状态={order_status}(未成交)"

                # 检查委托类型过滤
                if should_cancel and order_type != 'ALL':
                    if order_type == 'BUY' and order_op_type != 1:
                        should_cancel = False
                    elif order_type == 'SELL' and order_op_type != 2:
                        should_cancel = False

                # 检查是否为当前股票
                if should_cancel and order_stock != C.stock:
                    should_cancel = False

                if should_cancel and order_id:
                    cancel_stats['cancelled_count'] += 1
                    print(f"🔄 准备撤销委托: {cancel_reason}")

                    # 执行撤单
                    try:
                        cancel_result = cancel(order_id, C.acct, 'STOCK', C)
                        if cancel_result:
                            cancel_stats['cancel_success'] += 1
                            print(f"✅ 撤单成功: ID={order_id}")
                        else:
                            cancel_stats['cancel_failed'] += 1
                            print(f"❌ 撤单失败: ID={order_id}")
                    except Exception as e:
                        cancel_stats['cancel_failed'] += 1
                        cancel_stats['errors'].append(f"撤单异常: {e}")
                        print(f"❌ 撤单异常: {e}")

            except Exception as e:
                cancel_stats['errors'].append(f"处理委托异常: {e}")
                print(f"❌ 处理委托异常: {e}")

    except Exception as e:
        cancel_stats['errors'].append(f"撤单流程异常: {e}")
        print(f"❌ 撤单流程异常: {e}")

    return cancel_stats

# ============================================================================
# 买入订单执行模块
# ============================================================================

def execute_buy_order(C, current_price, volume=None):
    """
    执行买入订单 - 带挂单偏移策略

    参数:
        C: 策略上下文
        current_price: 当前价格
        volume: 买入数量，如果为None则自动计算

    返回:
        dict: 买入结果
    """
    try:
        print(f"\n📤 准备执行买入订单")

        # 检查是否已有持仓
        if C.position != 0:
            return {'success': False, 'reason': '已有持仓，无法买入'}

        # 检查是否有未处理的买入订单
        if check_has_pending_orders(C, 'BUY'):
            print("⚠️ 发现未处理的买入订单，先撤销")
            cancel_result = cancel_pending_orders(C, 'BUY')
            print(f"   撤单结果: 成功{cancel_result['cancel_success']}个")

        # 获取账户信息
        account_info = get_trade_detail_data(C.acct, 'stock', 'account')
        if not account_info:
            return {'success': False, 'reason': '无法获取账户信息'}

        available_cash = getattr(account_info[0], 'm_dAvailable', 0)
        print(f"💰 可用资金: {available_cash:.2f}")

        # 计算买入数量
        if volume is None:
            # 自动计算买入数量（使用30%资金）
            max_investment = available_cash * 0.3
            volume = int(max_investment / current_price / 100) * 100  # 整百股

        if volume <= 0:
            return {'success': False, 'reason': '计算买入数量为0'}

        # 计算挂单价格（略低于当前价格）
        hang_offset = current_price * C.buy_hang_offset_ratio
        hang_price = current_price - hang_offset
        hang_price = round(hang_price, 3)

        print(f"📊 买入参数:")
        print(f"   当前价格: {current_price:.3f}")
        print(f"   挂单价格: {hang_price:.3f} (偏移: -{hang_offset:.3f})")
        print(f"   买入数量: {volume}股")
        print(f"   预计金额: {hang_price * volume:.2f}")

        # 执行买入订单
        order_msg = f"买入{C.stock} {volume}股@{hang_price:.3f}"

        order_result = passorder(
            23,  # 买入
            1101,  # 限价单
            C.acct,
            C.stock,
            11,  # 限价
            hang_price,
            volume,
            "CMF+BIAS背离策略",
            "买入开仓",
            order_msg,
            C
        )

        if order_result and order_result != -1:
            print(f"✅ 买入订单提交成功")
            print(f"   订单ID: {order_result}")

            # 更新策略状态
            C.entry_price = hang_price
            C.position = 1  # 标记为多头持仓
            C.bars_since_entry = 0
            C.highest_price_since_entry = current_price
            C.trailing_stop_price = 0
            C.total_trades += 1
            C.last_trade_time = datetime.datetime.now()

            return {
                'success': True,
                'order_id': order_result,
                'price': hang_price,
                'volume': volume,
                'amount': hang_price * volume
            }
        else:
            print(f"❌ 买入订单提交失败")
            C.failed_trades += 1
            return {'success': False, 'reason': '订单提交失败'}

    except Exception as e:
        print(f"❌ 买入订单执行异常: {e}")
        C.failed_trades += 1
        return {'success': False, 'reason': f'执行异常: {e}'}

# ============================================================================
# 卖出订单执行模块
# ============================================================================

def execute_sell_order(C, current_price, reason="手动卖出"):
    """
    执行卖出订单 - 带挂单偏移策略

    参数:
        C: 策略上下文
        current_price: 当前价格
        reason: 卖出原因

    返回:
        dict: 卖出结果
    """
    try:
        print(f"\n📤 准备执行卖出订单")
        print(f"   卖出原因: {reason}")

        # 检查是否有持仓
        if C.position == 0:
            return {'success': False, 'reason': '无持仓，无法卖出'}

        # 检查是否有未处理的卖出订单
        if check_has_pending_orders(C, 'SELL'):
            print("⚠️ 发现未处理的卖出订单，先撤销")
            cancel_result = cancel_pending_orders(C, 'SELL')
            print(f"   撤单结果: 成功{cancel_result['cancel_success']}个")

        # 获取持仓信息
        position_info = get_trade_detail_data(C.acct, 'stock', 'position')
        if not position_info:
            return {'success': False, 'reason': '无法获取持仓信息'}

        # 查找当前股票的持仓
        current_position = None
        for pos in position_info:
            pos_stock = getattr(pos, 'm_strInstrumentID', '') + '.' + getattr(pos, 'm_strExchangeID', '')
            if pos_stock == C.stock:
                current_position = pos
                break

        if not current_position:
            return {'success': False, 'reason': '未找到当前股票持仓'}

        volume = getattr(current_position, 'm_nVolume', 0)
        if volume <= 0:
            return {'success': False, 'reason': '持仓数量为0'}

        # 计算挂单价格（略高于当前价格）
        hang_offset = current_price * C.sell_hang_offset_ratio
        hang_price = current_price + hang_offset
        hang_price = round(hang_price, 3)

        print(f"📊 卖出参数:")
        print(f"   当前价格: {current_price:.3f}")
        print(f"   挂单价格: {hang_price:.3f} (偏移: +{hang_offset:.3f})")
        print(f"   卖出数量: {volume}股")
        print(f"   预计金额: {hang_price * volume:.2f}")

        # 计算盈亏
        if C.entry_price > 0:
            profit_pct = (hang_price - C.entry_price) / C.entry_price * 100
            profit_amount = (hang_price - C.entry_price) * volume
            print(f"   盈亏比例: {profit_pct:.2f}%")
            print(f"   盈亏金额: {profit_amount:.2f}")

        # 执行卖出订单
        order_msg = f"卖出{C.stock} {volume}股@{hang_price:.3f} ({reason})"

        order_result = passorder(
            24,  # 卖出
            1101,  # 限价单
            C.acct,
            C.stock,
            11,  # 限价
            hang_price,
            volume,
            "CMF+BIAS背离策略",
            "卖出平仓",
            order_msg,
            C
        )

        if order_result and order_result != -1:
            print(f"✅ 卖出订单提交成功")
            print(f"   订单ID: {order_result}")

            # 更新策略状态
            C.exit_price = hang_price
            C.position = 0  # 清空持仓
            C.entry_price = 0
            C.bars_since_entry = 0
            C.highest_price_since_entry = 0
            C.trailing_stop_price = 0
            C.successful_trades += 1
            C.last_trade_time = datetime.datetime.now()

            return {
                'success': True,
                'order_id': order_result,
                'price': hang_price,
                'volume': volume,
                'amount': hang_price * volume,
                'reason': reason
            }
        else:
            print(f"❌ 卖出订单提交失败")
            C.failed_trades += 1
            return {'success': False, 'reason': '订单提交失败'}

    except Exception as e:
        print(f"❌ 卖出订单执行异常: {e}")
        C.failed_trades += 1
        return {'success': False, 'reason': f'执行异常: {e}'}

def reset_position_state(C):
    """重置持仓状态"""
    C.position = 0
    C.entry_price = 0
    C.exit_price = 0
    C.bars_since_entry = 0
    C.highest_price_since_entry = 0
    C.trailing_stop_price = 0
    print("🔄 持仓状态已重置")

# ============================================================================
# 测试用例和使用示例
# ============================================================================

def create_test_data(length=50):
    """创建测试用的市场数据"""
    np.random.seed(42)  # 固定随机种子，确保结果可重复

    # 生成模拟价格数据
    base_price = 10.0
    prices = [base_price]

    for i in range(length - 1):
        # 随机游走，带有轻微上涨趋势
        change = np.random.normal(0.001, 0.02)  # 均值0.1%，标准差2%
        new_price = prices[-1] * (1 + change)
        new_price = max(new_price, 5.0)  # 最低价格限制
        prices.append(new_price)

    # 生成OHLC数据
    highs = []
    lows = []
    closes = prices
    opens = [prices[0]] + prices[:-1]

    for i, close in enumerate(closes):
        # 生成合理的高低价
        volatility = abs(np.random.normal(0, 0.01))
        high = close * (1 + volatility)
        low = close * (1 - volatility)

        # 确保价格关系合理
        if i > 0:
            high = max(high, opens[i], close)
            low = min(low, opens[i], close)

        highs.append(high)
        lows.append(low)

    return {
        'opens': opens,
        'highs': highs,
        'lows': lows,
        'closes': closes
    }

def test_moving_profit_control():
    """测试移动止盈风控计算"""
    print("\n" + "="*60)
    print("🧪 测试移动止盈风控计算")
    print("="*60)

    # 创建测试数据
    test_data = create_test_data(30)

    # 计算移动止盈风控
    result = calculate_moving_profit_control(
        test_data['highs'],
        test_data['lows'],
        test_data['closes']
    )

    print("📊 移动止盈风控计算结果:")
    for key, value in result.items():
        if isinstance(value, float):
            print(f"   {key}: {value:.3f}")
        else:
            print(f"   {key}: {value}")

    return result

def test_exit_conditions():
    """测试止盈止损条件检查"""
    print("\n" + "="*60)
    print("🧪 测试止盈止损条件检查")
    print("="*60)

    # 创建测试上下文
    C = MockContext()
    C.position = 1  # 模拟持仓
    C.entry_price = 10.0
    C.highest_price_since_entry = 10.0
    C.trailing_stop_price = 0

    # 创建移动止盈信息
    test_data = create_test_data(30)
    移动止盈_info = calculate_moving_profit_control(
        test_data['highs'],
        test_data['lows'],
        test_data['closes']
    )

    # 测试不同价格情况
    test_prices = [9.5, 10.0, 10.5, 11.0, 11.5]  # 亏损、平价、小盈利、大盈利

    for price in test_prices:
        print(f"\n🔍 测试价格: {price:.1f}")
        result = check_exit_conditions(C, price, 移动止盈_info)
        print(f"   结果: {result}")

def test_order_execution():
    """测试订单执行"""
    print("\n" + "="*60)
    print("🧪 测试订单执行")
    print("="*60)

    # 创建测试上下文
    C = MockContext()
    current_price = 10.0

    print("📤 测试买入订单:")
    buy_result = execute_buy_order(C, current_price, 1000)
    print(f"买入结果: {buy_result}")

    if buy_result['success']:
        print(f"\n📊 当前持仓状态:")
        print(f"   持仓: {C.position}")
        print(f"   入场价: {C.entry_price:.3f}")

        # 模拟价格上涨后卖出
        new_price = 10.5
        print(f"\n📤 测试卖出订单 (价格上涨到{new_price}):")
        sell_result = execute_sell_order(C, new_price, "止盈平仓")
        print(f"卖出结果: {sell_result}")

def test_order_management():
    """测试订单管理"""
    print("\n" + "="*60)
    print("🧪 测试订单管理")
    print("="*60)

    C = MockContext()

    # 测试检查未处理订单
    print("🔍 检查未处理订单:")
    has_pending = check_has_pending_orders(C, 'ALL')
    print(f"   有未处理订单: {has_pending}")

    # 提交一个测试订单
    print("\n📤 提交测试订单:")
    buy_result = execute_buy_order(C, 10.0, 500)

    # 再次检查
    print("\n🔍 再次检查未处理订单:")
    has_pending = check_has_pending_orders(C, 'BUY')
    print(f"   有未处理买入订单: {has_pending}")

    # 测试撤单
    print("\n🔄 测试撤单:")
    cancel_result = cancel_pending_orders(C, 'ALL')
    print(f"撤单结果: {cancel_result}")

def run_comprehensive_test():
    """运行综合测试"""
    print("\n" + "="*80)
    print("🚀 止盈止损和下单模块综合测试")
    print("="*80)

    # 运行各项测试
    test_moving_profit_control()
    test_exit_conditions()
    test_order_execution()
    test_order_management()

    print("\n" + "="*80)
    print("✅ 所有测试完成")
    print("="*80)

def demo_trading_scenario():
    """演示完整的交易场景"""
    print("\n" + "="*80)
    print("🎯 完整交易场景演示")
    print("="*80)

    # 创建策略上下文
    C = MockContext('000001.SZ')

    # 创建市场数据
    market_data = create_test_data(50)

    print("📊 模拟交易过程:")

    # 模拟交易过程
    for i, price in enumerate(market_data['closes'][-10:]):  # 只看最后10个价格点
        print(f"\n📅 第{i+1}天，价格: {price:.3f}")

        # 如果没有持仓，考虑买入
        if C.position == 0 and i == 2:  # 第3天买入
            print("🎯 触发买入信号")
            buy_result = execute_buy_order(C, price, 1000)
            if buy_result['success']:
                print(f"✅ 买入成功: {buy_result['volume']}股@{buy_result['price']:.3f}")

        # 如果有持仓，检查止盈止损
        elif C.position == 1:
            # 计算移动止盈信息
            移动止盈_info = calculate_moving_profit_control(
                market_data['highs'][:i+40],  # 使用到当前为止的数据
                market_data['lows'][:i+40],
                market_data['closes'][:i+40]
            )

            # 检查平仓条件
            exit_result = check_exit_conditions(C, price, 移动止盈_info)

            if exit_result['should_exit']:
                print(f"🎯 触发平仓信号: {exit_result['reason']}")
                sell_result = execute_sell_order(C, price, exit_result['reason'])
                if sell_result['success']:
                    profit = (sell_result['price'] - C.entry_price) * sell_result['volume']
                    profit_pct = (sell_result['price'] - C.entry_price) / C.entry_price * 100
                    print(f"✅ 卖出成功: {sell_result['volume']}股@{sell_result['price']:.3f}")
                    print(f"💰 盈亏: {profit:.2f}元 ({profit_pct:.2f}%)")
                    break

    print(f"\n📊 交易统计:")
    print(f"   总交易次数: {C.total_trades}")
    print(f"   成功交易: {C.successful_trades}")
    print(f"   失败交易: {C.failed_trades}")

if __name__ == "__main__":
    # 运行测试
    run_comprehensive_test()

    # 运行交易场景演示
    demo_trading_scenario()
