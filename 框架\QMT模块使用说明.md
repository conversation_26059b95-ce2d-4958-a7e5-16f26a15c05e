# QMT止盈止损下单模块使用说明 (唐奇安通道版本)

## 概述

本模块是从原始6sk线.py策略中提取的核心交易功能，专门为QMT实盘环境设计。主要特点：
- **支持主图标的**: 可以直接对当前图表的股票进行交易
- **唐奇安通道移动止盈**: 使用唐奇安通道下轨作为移动止盈线
- **K线合成功能**: 2根K线合成1根，减少市场噪音
- **动态触发阈值**: 根据股票波动率自动调整止盈触发点
- **完整的委托管理**: 查询、撤单、订单执行一体化

## 文件说明

- **`QMT止盈止损下单模块.py`**: 标准QMT框架版本，适用于实盘交易

## 核心功能

### 1. 主图标的支持

#### `get_main_stock_code(ContextInfo)`

**功能**: 自动获取当前图表的股票代码

**特点**:
- 自动识别当前图表股票
- 支持多种QMT属性名称
- 提供默认值保护

### 2. K线合成模块

#### `process_kline_merge(ContextInfo, bar_time, last_price, last_volume, dt_obj)`

**功能**: 处理K线合成逻辑，将多根K线合成为一根

**特点**:
- 2根K线合成1根 (非重叠模式)
- 实时缓冲区管理
- 自动滑动窗口维护
- 支持OHLCV数据合成

#### `merge_two_bars(data_list)` / `merge_three_bars(data_list)`

**功能**: K线数据合成函数

**合成规则**:
- **开盘价**: 第一根K线的价格
- **收盘价**: 最后一根K线的价格
- **最高价**: 所有K线中的最高价
- **最低价**: 所有K线中的最低价
- **成交量**: 所有K线成交量之和

#### `get_merged_kline_data(ContextInfo)`

**功能**: 获取合成K线数据用于技术指标计算

**返回**:
```python
{
    'opens': [开盘价数组],
    'highs': [最高价数组],
    'lows': [最低价数组],
    'closes': [收盘价数组],
    'volumes': [成交量数组],
    'count': 数据数量
}
```

### 3. 动态触发阈值模块

#### `calculate_dynamic_trigger_threshold(closes, period=20)`

**功能**: 根据股票历史波动率计算动态移动止盈触发阈值

**阈值分级**:
- **低波动率** (< 1.5%): 使用3.0%触发阈值
- **中等波动率** (1.5% - 4.0%): 线性插值计算
- **高波动率** (> 4.0%): 使用8.0%触发阈值

**优势**:
- 适应不同股票特性
- 减少低波动股票的过早止盈
- 提高高波动股票的风险控制

### 4. 唐奇安通道移动止盈模块

#### `check_exit_conditions_donchian(ContextInfo, current_price, entry_price, highest_price_since_entry, trailing_stop_price, market_data=None)`

**功能**: 基于唐奇安通道的移动止盈止损检查

**平仓条件**:
- 固定止损保护 (默认0.5%)
- 唐奇安通道移动止盈 (盈利>5%时启动)
- 下轨跟踪止盈 (唐奇安下轨-2%偏移)

#### `calculate_donchian_channel(highs, lows, period=20)`

**功能**: 计算唐奇安通道

**返回**:
- `upper_channel`: 最高价通道
- `lower_channel`: 最低价通道
- `middle_channel`: 中轨
- `channel_width_pct`: 通道宽度百分比

**返回值**:
```python
{
    'should_exit': True/False,           # 是否应该平仓
    'reason': '平仓原因',                # 详细原因
    'price': 当前价格,                   # 建议平仓价格
    'new_trailing_stop': 新移动止盈价格,  # 更新后的移动止盈价格
    'new_highest_price': 新最高价        # 更新后的最高价
}
```

### 2. 委托查询和撤单模块

#### `query_orders(ContextInfo, stock_code=None, order_type='ALL')`

**功能**: 查询委托信息

**参数**:
- `stock_code`: 股票代码，None表示查询所有
- `order_type`: 'BUY', 'SELL', 'ALL'

**返回**: 委托信息列表，包含订单状态、价格、数量等详细信息

#### `cancel_orders(ContextInfo, stock_code=None, order_type='ALL', order_ids=None)`

**功能**: 撤销委托

**参数**:
- `stock_code`: 股票代码
- `order_type`: 订单类型
- `order_ids`: 指定订单ID列表

**返回**: 撤单结果统计

#### `check_pending_orders(ContextInfo, stock_code, order_type='ALL')`

**功能**: 检查是否有未处理的订单

### 3. 订单执行模块

#### `execute_buy_order(ContextInfo, stock_code, current_price, volume=None, auto_cancel_pending=True)`

**功能**: 执行买入订单，带挂单偏移策略

**特点**:
- 自动撤销冲突的买入订单
- 挂单价格略低于当前价格（减少滑点）
- 自动计算买入数量（默认30%资金）
- 完整的资金检查

#### `execute_sell_order(ContextInfo, stock_code, current_price, volume=None, reason="手动卖出", auto_cancel_pending=True)`

**功能**: 执行卖出订单，带挂单偏移策略

**特点**:
- 自动撤销冲突的卖出订单
- 挂单价格略高于当前价格（减少滑点）
- 自动获取持仓数量
- 支持部分卖出

### 4. 移动止盈风控计算

#### `calculate_moving_profit_control(highs, lows, closes)`

**功能**: 计算基于ATR的优化移动止盈风控参数

**返回**: 包含ATR值、止损距离、止盈距离等完整风控参数

## QMT标准框架使用

### 1. 在QMT中导入模块

```python
# 在QMT策略文件中导入
from QMT止盈止损下单模块 import *

def init(ContextInfo):
    """策略初始化"""
    # 设置股票池
    ContextInfo.stock_pool = ['000001.SZ', '000002.SZ']
    
    # 初始化策略状态
    ContextInfo.strategy_state = {}
    for stock in ContextInfo.stock_pool:
        ContextInfo.strategy_state[stock] = {
            'entry_price': 0,
            'highest_price_since_entry': 0,
            'trailing_stop_price': 0,
            'position_status': 0
        }

def handlebar(ContextInfo):
    """主策略函数"""
    for stock_code in ContextInfo.stock_pool:
        # 获取当前价格
        current_price = ContextInfo.get_market_data([stock_code], period='1d', count=1)
        
        # 检查持仓状态
        position = get_current_position_info(ContextInfo, stock_code)
        
        if not position['has_position']:
            # 无持仓时的买入逻辑
            if check_buy_signal():  # 你的买入信号
                buy_result = execute_buy_order(ContextInfo, stock_code, current_price)
                if buy_result['success']:
                    # 更新策略状态
                    ContextInfo.strategy_state[stock_code]['entry_price'] = buy_result['price']
                    ContextInfo.strategy_state[stock_code]['position_status'] = 1
        else:
            # 有持仓时的止盈止损检查
            state = ContextInfo.strategy_state[stock_code]
            
            # 获取市场数据用于计算移动止盈
            market_data = get_market_data(stock_code, 50)  # 获取50个周期数据
            移动止盈_info = calculate_moving_profit_control(
                market_data['highs'], market_data['lows'], market_data['closes']
            )
            
            # 检查平仓条件
            exit_result = check_exit_conditions(
                ContextInfo, current_price, 
                state['entry_price'],
                state['highest_price_since_entry'],
                state['trailing_stop_price'],
                移动止盈_info
            )
            
            if exit_result['should_exit']:
                # 执行卖出
                sell_result = execute_sell_order(
                    ContextInfo, stock_code, current_price, 
                    reason=exit_result['reason']
                )
                if sell_result['success']:
                    # 重置策略状态
                    ContextInfo.strategy_state[stock_code] = {
                        'entry_price': 0,
                        'highest_price_since_entry': 0,
                        'trailing_stop_price': 0,
                        'position_status': 0
                    }
            else:
                # 更新策略状态
                state['highest_price_since_entry'] = exit_result['new_highest_price']
                state['trailing_stop_price'] = exit_result['new_trailing_stop']
```

### 2. 配置参数

```python
# 修改全局配置
STRATEGY_CONFIG = {
    'max_position_ratio': 0.5,      # 最大仓位比例 (50%)
    'min_cash_reserve': 10000,      # 最小资金保留
    'buy_hang_offset_ratio': 0.002, # 买入挂单偏移比例
    'sell_hang_offset_ratio': 0.002,# 卖出挂单偏移比例
    'fixed_stop_loss': 0.5,         # 固定止损百分比
    'use_trailing_stop': True,      # 是否使用移动止盈
    'use_main_stock': True,         # 是否使用主图标的
    'donchian_period': 20,          # 唐奇安通道周期
    'trailing_trigger_pct': 5.0,    # 移动止盈触发阈值(%)
    'donchian_offset_pct': 2.0,     # 唐奇安通道偏移百分比
    'min_move_threshold': 0.1       # 最小移动幅度
}
```

## 实用工具函数

### 1. 查看策略状态

```python
# 打印策略状态
print_strategy_status(ContextInfo, stock_code, current_price, entry_price, highest_price, trailing_stop)
```

### 2. 查看委托信息

```python
# 查询所有委托
orders = query_orders(ContextInfo)
print_orders_summary(orders)

# 查询特定股票的买入委托
buy_orders = query_orders(ContextInfo, '000001.SZ', 'BUY')
```

### 3. 批量撤单

```python
# 撤销所有未处理订单
cancel_result = cancel_orders(ContextInfo)

# 撤销特定股票的卖出订单
cancel_result = cancel_orders(ContextInfo, '000001.SZ', 'SELL')
```

## 注意事项

1. **实盘环境**: 本模块专为QMT实盘环境设计，不包含任何模拟功能
2. **风险控制**: 使用前请充分测试，确保参数设置合理
3. **资金管理**: 默认使用30%资金买入，可根据需要调整
4. **挂单策略**: 买入挂单略低于市价，卖出挂单略高于市价，减少滑点
5. **移动止盈**: 基于ATR的动态移动止盈，只能向上移动，保护已实现利润

## 常见问题

### Q: 如何调整止盈止损参数？
A: 修改`STRATEGY_CONFIG`中的相关参数，如`fixed_stop_loss`、`atr_multiplier`等。

### Q: 如何处理多股票交易？
A: 在`init()`中设置`ContextInfo.stock_pool`，模块会自动处理多股票的状态管理。

### Q: 如何查看当前未处理订单？
A: 使用`check_pending_orders()`或`query_orders()`函数查看订单状态。

### Q: 移动止盈如何工作？
A: 基于唐奇安通道下轨计算移动止盈线，当盈利达到触发阈值后启动移动机制，止盈线只能向上移动。

### Q: K线合成有什么好处？
A: 2根K线合成1根可以减少市场噪音，提高信号质量，降低假突破的影响。

### Q: 动态触发阈值如何工作？
A: 根据股票历史波动率自动调整移动止盈触发点，低波动股票使用较低阈值，高波动股票使用较高阈值。

### Q: 买入信号如何触发？
A: 接收3次数据后自动触发买入信号，可以通过配置参数调整所需的数据次数。

### Q: 买入价格如何计算？
A: 买入价格 = 当前价格 - (当前价格 × 买入挂单偏移比例)，默认偏移0.2%，确保更好的成交概率。

### Q: 如何在QMT中使用？
A: 参考6sk线的简洁风格，使用标准的init(C)和handlebar(C)函数即可。

## 📊 动态触发阈值配置详解

### 什么是动态触发阈值？
动态触发阈值是基于ATR（平均真实波动率）计算的自适应止盈止损触发条件，能够根据市场波动性自动调整触发敏感度。

### 核心配置参数

#### **1. ATR基础参数**
```python
'atr_period': 14,              # ATR计算周期
'atr_stop_loss_multiplier': 2.0,    # ATR止损倍数
'atr_trigger_threshold_multiplier': 1.5,  # ATR触发阈值倍数
```

#### **2. 动态触发机制**
- **低波动市场** (ATR < 平均值): 使用较小的触发阈值，提高敏感度
- **高波动市场** (ATR > 平均值): 使用较大的触发阈值，避免频繁触发
- **极端波动市场**: 自动调整到保护性阈值

#### **3. 计算公式**
```python
# 动态止损价格
dynamic_stop_loss = entry_price - (current_atr * atr_stop_loss_multiplier)

# 动态触发阈值
trigger_threshold = current_atr * atr_trigger_threshold_multiplier

# 移动止盈触发条件
if (current_price - donchian_lower) > trigger_threshold:
    # 启动移动止盈机制
```

### 实际应用示例

#### **场景1: 低波动环境**
- ATR = 0.15 (1.5%)
- 触发阈值 = 0.15 × 1.5 = 0.225 (2.25%)
- 当价格超过唐奇安下轨2.25%时启动移动止盈

#### **场景2: 高波动环境**
- ATR = 0.35 (3.5%)
- 触发阈值 = 0.35 × 1.5 = 0.525 (5.25%)
- 当价格超过唐奇安下轨5.25%时启动移动止盈

### 优势特点
1. **自适应性**: 根据市场波动自动调整
2. **减少噪音**: 避免在高波动期频繁触发
3. **保护利润**: 在低波动期及时锁定收益
4. **风险控制**: 动态调整止损距离

## 🔧 常见错误修复

### Q: 遇到"__PyContext object has no attribute accID"错误？
A: 已修复，现在使用正确的账户属性访问方式：
```python
# 错误方式
account_info = get_trade_detail_data(ContextInfo.accID, 'stock', 'position')

# 正确方式 - 参考交易实时主推示例
account_id = getattr(C, 'acct', '*********')
account_info = get_trade_detail_data(account_id, 'stock', 'position')
```

### Q: 如何正确设置账户？
A: 现在支持自动获取主图账户ID：
```python
def init(C):
    # 自动获取主图账户ID - 优先级顺序：
    # 1. C.accountid (主图账户ID)
    # 2. 全局变量 account
    # 3. 默认值 '*********'
    C.acct = get_main_account_id(C)
    C.set_account(C.acct)
```

### Q: 主图账户ID获取逻辑？
A: 按以下优先级自动获取：
1. **主图账户ID**: `C.accountid` (最高优先级)
2. **全局账户变量**: `globals()['account']`
3. **默认账户**: `'*********'` (示例账户)

## 配置参数说明

### 基础配置
```python
STRATEGY_CONFIG = {
    'max_position_ratio': 0.5,      # 最大仓位比例 (50%)
    'min_cash_reserve': 10000,      # 最小资金保留 (元)
    'buy_hang_offset_ratio': 0.002, # 买入挂单偏移比例 (0.2%)
    'sell_hang_offset_ratio': 0.002,# 卖出挂单偏移比例 (0.2%)
    'fixed_stop_loss': 0.5,         # 固定止损百分比 (0.5%)
    'use_trailing_stop': True,      # 是否使用移动止盈
    'use_main_stock': True,         # 是否使用主图标的
}
```

### 唐奇安通道和ATR配置
```python
{
    'donchian_period': 20,          # 唐奇安通道周期 (20日)
    'atr_period': 14,               # ATR计算周期 (14日)
    'atr_stop_loss_multiplier': 2.0, # ATR止损倍数 (2倍ATR)
    'atr_trigger_multiplier': 3.0,  # ATR移动止盈触发倍数 (3倍ATR)
    'min_move_threshold': 0.1       # 最小移动幅度 (0.1%)
}
```

### K线合成配置
```python
{
    'enable_kline_merge': True,     # 是否启用K线合成
    'merge_ratio': 2,               # 合成比例 (2根合成1根)
    'max_buffer_size': 10,          # 最大缓冲区大小
    'max_history_bars': 200,        # 最大历史K线数量
}
```

### 动态触发阈值配置
```python
{
    'use_dynamic_trigger': True,    # 是否使用动态触发阈值
    'volatility_period': 20,        # 波动率计算周期
    'low_volatility_trigger': 3.0,  # 低波动率时的触发阈值 (3%)
    'high_volatility_trigger': 8.0, # 高波动率时的触发阈值 (8%)
}
```

### 买入信号配置
```python
{
    'buy_signal_count_required': 3, # 需要连续接收的数据次数
    'buy_signal_enabled': True,     # 是否启用买入信号检查
    'buy_hang_offset_ratio': 0.002, # 买入挂单偏移比例 (0.2%)
}
```

## ATR动态止盈止损说明

### ATR止损计算
**计算公式**: ATR止损价 = 入场价 - (ATR值 × ATR止损倍数)

**特点**:
- 根据股票真实波动幅度动态调整止损距离
- 高波动股票止损距离更大，低波动股票止损距离更小
- 避免因市场正常波动被止损出局

**示例**: 入场价10.00元，ATR=0.20元，止损倍数=2.0，则止损价=10.00-0.40=9.60元

### ATR移动止盈触发
**计算公式**: 触发阈值 = ATR值 × ATR触发倍数

**特点**:
- 当盈利金额达到ATR触发阈值时，启动移动止盈
- 直接使用唐奇安下轨作为移动止盈线（无偏移）
- 收盘价小于唐奇安下轨即触发卖出

**示例**: ATR=0.20元，触发倍数=3.0，则盈利达到0.60元时启动移动止盈

## 技术支持

如有问题，请检查：
1. QMT环境是否正常
2. 账户权限是否充足
3. 股票代码格式是否正确
4. 网络连接是否稳定
5. K线合成缓冲区是否正常
6. 动态阈值计算是否有足够历史数据

## 版本信息

- **版本**: 1.2.0
- **作者**: QMT止盈止损下单模块
- **更新**: 2024年版本，支持ATR动态止盈止损、唐奇安通道移动止盈、K线合成功能
