#coding:gbk
"""
QMT历史数据获取测试策略
用于测试历史数据获取功能，为策略提供数据预热

功能:
1. 测试历史K线数据获取
2. 验证数据完整性和质量
3. 计算ATR等技术指标
4. 为策略提供预热数据

作者: QMT策略开发
版本: 1.0.0
日期: 2025-07-31

使用方法:
1. 在QMT中加载此策略文件
2. 设置股票代码
3. 运行策略查看数据预热测试结果
"""

import numpy as np
from datetime import datetime, timedelta

# ============================================================================
# QMT策略标准接口
# ============================================================================

def init(ContextInfo):
    """
    策略初始化函数 - 执行数据预热测试
    """
    print("🚀 QMT历史数据预热测试策略启动")
    print("="*60)

    # 获取股票代码
    stock_code = getattr(ContextInfo, 'stock', '123171.SZ')
    print(f"📊 测试股票: {stock_code}")
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 先下载历史数据（这是QMT的要求）
    print(f"\n📥 下载历史数据...")
    try:
        # 使用QMT内置API下载历史数据（参考获取历史数据文件）
        download_history_data(stock_code, "1d", "20230101", "")
        print(f"✅ 日线数据下载完成")

        # 下载分钟数据用于更精确分析
        download_history_data(stock_code, "1m", "20241201", "")
        print(f"✅ 分钟数据下载完成")

    except Exception as e:
        print(f"⚠️ 历史数据下载异常: {e}")
        print(f"💡 提示: 在init()中只能下载数据，不能获取数据")

    # 执行简化的数据测试
    print(f"\n🔍 开始数据预热测试...")

    # 测试1: 简化数据测试
    test1_result = simple_data_test(ContextInfo)

    # 测试2: 当前市场数据测试
    test2_result = test_current_market_data(ContextInfo)

    # 测试总结
    print(f"\n{'='*50}")
    print(f"📋 测试结果总结")
    print(f"✅ 历史数据测试: {'通过' if test1_result else '失败'}")
    print(f"✅ 当前数据测试: {'通过' if test2_result else '失败'}")

    if test1_result and test2_result:
        print(f"🎉 数据预热测试全部通过，策略可以正常运行！")
        print(f"💡 建议: 可以开始使用主策略进行交易")
    elif test1_result:
        print(f"⚠️ 历史数据正常，但当前数据有问题")
        print(f"💡 建议: 检查实时数据连接")
    elif test2_result:
        print(f"⚠️ 当前数据正常，但历史数据不足")
        print(f"💡 建议: 等待更多历史数据积累")
    else:
        print(f"❌ 数据预热测试失败")
        print(f"💡 建议: 检查网络连接和股票代码")

    print("="*60)
    print("✅ 数据预热测试完成")

def handlebar(ContextInfo):
    """
    K线数据处理函数
    """
    # 每次收到新K线时进行简单的数据验证
    stock_code = getattr(ContextInfo, 'stock', '123171.SZ')

    try:
        # 获取当前价格
        current_data = ContextInfo.get_market_data_ex(
            ['close', 'volume'],  # 字段列表
            [stock_code],         # 证券代码列表
            period='1d',
            count=1,
            dividend_type='none'
        )

        if current_data and stock_code in current_data:
            latest_data = current_data[stock_code]
            if len(latest_data) > 0:
                latest = latest_data[-1]
                current_price = latest.get('close', 0)
                current_volume = latest.get('volume', 0)

                print(f"📈 实时数据更新: 价格={current_price:.3f}, 成交量={current_volume:.0f}")

                # 每10次K线进行一次完整的数据检查
                bar_count = getattr(ContextInfo, 'bar_count', 0) + 1
                ContextInfo.bar_count = bar_count

                if bar_count % 10 == 0:
                    print(f"🔍 第{bar_count}根K线，执行数据完整性检查...")
                    quick_data_validation(ContextInfo)

    except Exception as e:
        print(f"❌ K线处理异常: {e}")

def run_data_warmup_tests(ContextInfo):
    """
    运行数据预热测试
    """
    stock_code = getattr(ContextInfo, 'stock', '123171.SZ')

    print(f"\n🔍 开始数据预热测试")
    print(f"📊 股票代码: {stock_code}")

    # 测试1: 快速数据获取测试
    print(f"\n{'='*50}")
    print(f"📋 测试1: 快速数据获取")
    quick_result = quick_data_test(ContextInfo)

    # 测试2: 策略数据预热测试
    print(f"\n{'='*50}")
    print(f"📋 测试2: 策略数据预热")
    warmup_result = test_data_warmup_for_strategy(ContextInfo, 30)

    # 测试3: 不同周期数据测试
    print(f"\n{'='*50}")
    print(f"📋 测试3: 多周期数据测试")
    multi_period_test(ContextInfo)

    # 测试总结
    print(f"\n{'='*50}")
    print(f"📋 测试总结")

    success_count = 0
    if quick_result.get("success", False):
        success_count += 1
        print(f"✅ 快速数据获取: 成功")
    else:
        print(f"❌ 快速数据获取: 失败")

    if warmup_result.get("success", False) and warmup_result.get("data_ready", False):
        success_count += 1
        print(f"✅ 策略数据预热: 成功")
    else:
        print(f"❌ 策略数据预热: 失败")

    print(f"\n🎯 总体结果: {success_count}/3 项测试通过")

    if success_count >= 2:
        print(f"🎉 数据预热测试通过，策略可以正常运行")
    else:
        print(f"⚠️ 数据预热测试未完全通过，建议检查数据源")

def quick_data_validation(ContextInfo):
    """
    快速数据验证
    """
    try:
        stock_code = getattr(ContextInfo, 'stock', '123171.SZ')

        # 获取最近10根K线进行验证
        recent_data = ContextInfo.get_market_data_ex(
            ['close', 'volume'],  # 字段列表
            [stock_code],         # 证券代码列表
            period='1d',
            count=10,
            dividend_type='none'
        )

        if recent_data and stock_code in recent_data:
            data_list = recent_data[stock_code]
            if len(data_list) >= 5:
                # 简单的数据一致性检查
                prices = [float(item.get('close', 0)) for item in data_list[-5:]]
                volumes = [float(item.get('volume', 0)) for item in data_list[-5:]]

                valid_prices = sum(1 for p in prices if p > 0)
                valid_volumes = sum(1 for v in volumes if v > 0)

                print(f"   📊 最近5根K线验证: 有效价格{valid_prices}/5, 有效成交量{valid_volumes}/5")

                if valid_prices >= 4 and valid_volumes >= 3:
                    print(f"   ✅ 数据质量良好")
                else:
                    print(f"   ⚠️ 数据质量需要关注")

    except Exception as e:
        print(f"   ❌ 数据验证异常: {e}")

def multi_period_test(ContextInfo):
    """
    多周期数据测试
    """
    stock_code = getattr(ContextInfo, 'stock', '123171.SZ')
    periods = ['1d', '1h', '30m']

    for period in periods:
        try:
            print(f"\n🔍 测试周期: {period}")

            # 注意：这个函数需要在QMT策略环境中调用，需要ContextInfo对象
            print(f"⚠️ 此函数需要在QMT策略环境中调用")
            return False

            if test_data and stock_code in test_data:
                data_count = len(test_data[stock_code])
                print(f"   ✅ {period} 数据获取成功: {data_count}条")

                if data_count > 0:
                    latest = test_data[stock_code][-1]
                    price = latest.get('close', 0)
                    volume = latest.get('volume', 0)
                    print(f"   📊 最新数据: 价格={price:.3f}, 成交量={volume:.0f}")
            else:
                print(f"   ❌ {period} 数据获取失败")

        except Exception as e:
            print(f"   ❌ {period} 测试异常: {e}")

# ============================================================================
# 历史数据获取测试
# ============================================================================

def test_historical_data_fetch(stock_code, period_days=30, kline_type='1d'):
    """
    测试历史数据获取功能
    
    参数:
        stock_code: 股票代码 (如 '123171.SZ')
        period_days: 获取天数
        kline_type: K线类型 ('1d', '1h', '30m', '15m', '5m', '1m')
    
    返回:
        dict: 测试结果和数据
    """
    print(f"\n{'='*60}")
    print(f"🔍 历史数据获取测试")
    print(f"{'='*60}")
    print(f"📊 股票代码: {stock_code}")
    print(f"📅 获取天数: {period_days}天")
    print(f"⏰ K线类型: {kline_type}")
    
    try:
        # 计算开始和结束时间
        end_time = datetime.now()
        start_time = end_time - timedelta(days=period_days)
        
        start_str = start_time.strftime('%Y%m%d')
        end_str = end_time.strftime('%Y%m%d')
        
        print(f"🕐 时间范围: {start_str} ~ {end_str}")
        print(f"⏳ 正在获取历史数据...")
        
        # 尝试获取历史数据 - 使用QMT API
        try:
            # 方法1: 使用get_market_data_ex获取历史数据
            hist_data = get_market_data_ex(
                stock_list=[stock_code],
                period=kline_type,
                start_time=start_str,
                end_time=end_str,
                count=period_days * 10,  # 预留足够数量
                dividend_type='none',
                fill_data=True
            )
            
            if hist_data and len(hist_data) > 0:
                print(f"✅ 方法1成功: get_market_data_ex")
                return process_historical_data(hist_data, stock_code, "get_market_data_ex")
                
        except Exception as e:
            print(f"❌ 方法1失败: get_market_data_ex - {e}")
        
        # 方法2: 使用get_full_tick获取历史数据
        try:
            hist_data = get_full_tick([stock_code])
            if hist_data and len(hist_data) > 0:
                print(f"✅ 方法2成功: get_full_tick")
                return process_historical_data(hist_data, stock_code, "get_full_tick")
                
        except Exception as e:
            print(f"❌ 方法2失败: get_full_tick - {e}")
        
        # 方法3: 使用其他可能的API
        try:
            # 尝试其他历史数据获取方法
            print(f"🔄 尝试其他数据获取方法...")
            return {"success": False, "error": "所有历史数据获取方法都失败"}
            
        except Exception as e:
            print(f"❌ 所有方法都失败: {e}")
            return {"success": False, "error": str(e)}
            
    except Exception as e:
        print(f"❌ 历史数据获取测试异常: {e}")
        return {"success": False, "error": str(e)}

def process_historical_data(hist_data, stock_code, method_name):
    """
    处理和分析历史数据
    
    参数:
        hist_data: 历史数据
        stock_code: 股票代码
        method_name: 获取方法名称
    
    返回:
        dict: 处理结果
    """
    try:
        print(f"\n📊 数据处理和分析 (方法: {method_name})")
        print(f"-" * 50)
        
        # 数据基本信息
        data_count = len(hist_data) if isinstance(hist_data, list) else 1
        print(f"📈 数据条数: {data_count}")
        
        if data_count == 0:
            return {"success": False, "error": "获取到的数据为空"}
        
        # 提取OHLCV数据
        ohlcv_data = extract_ohlcv_data(hist_data, stock_code)
        
        if not ohlcv_data["success"]:
            return ohlcv_data
        
        # 数据质量检查
        quality_result = check_data_quality(ohlcv_data["data"])
        
        # 计算技术指标
        indicators = calculate_technical_indicators(ohlcv_data["data"])
        
        # 显示结果
        display_data_summary(ohlcv_data["data"], quality_result, indicators)
        
        return {
            "success": True,
            "method": method_name,
            "data_count": data_count,
            "ohlcv_data": ohlcv_data["data"],
            "quality": quality_result,
            "indicators": indicators
        }
        
    except Exception as e:
        print(f"❌ 数据处理异常: {e}")
        return {"success": False, "error": str(e)}

def extract_ohlcv_data(hist_data, stock_code):
    """
    从历史数据中提取OHLCV数据
    
    参数:
        hist_data: 原始历史数据
        stock_code: 股票代码
    
    返回:
        dict: 提取结果
    """
    try:
        opens = []
        highs = []
        lows = []
        closes = []
        volumes = []
        times = []
        
        # 根据数据格式提取OHLCV
        if isinstance(hist_data, dict):
            # 字典格式数据
            if stock_code in hist_data:
                stock_data = hist_data[stock_code]
                for item in stock_data:
                    opens.append(float(item.get('open', 0)))
                    highs.append(float(item.get('high', 0)))
                    lows.append(float(item.get('low', 0)))
                    closes.append(float(item.get('close', 0)))
                    volumes.append(float(item.get('volume', 0)))
                    times.append(item.get('time', ''))
        
        elif isinstance(hist_data, list):
            # 列表格式数据
            for item in hist_data:
                if hasattr(item, 'open'):
                    opens.append(float(item.open))
                    highs.append(float(item.high))
                    lows.append(float(item.low))
                    closes.append(float(item.close))
                    volumes.append(float(item.volume))
                    times.append(str(item.time) if hasattr(item, 'time') else '')
                elif isinstance(item, dict):
                    opens.append(float(item.get('open', 0)))
                    highs.append(float(item.get('high', 0)))
                    lows.append(float(item.get('low', 0)))
                    closes.append(float(item.get('close', 0)))
                    volumes.append(float(item.get('volume', 0)))
                    times.append(item.get('time', ''))
        
        if len(closes) == 0:
            return {"success": False, "error": "无法从数据中提取OHLCV信息"}
        
        return {
            "success": True,
            "data": {
                "opens": opens,
                "highs": highs,
                "lows": lows,
                "closes": closes,
                "volumes": volumes,
                "times": times,
                "count": len(closes)
            }
        }
        
    except Exception as e:
        return {"success": False, "error": f"OHLCV提取异常: {e}"}

def check_data_quality(ohlcv_data):
    """
    检查数据质量
    
    参数:
        ohlcv_data: OHLCV数据字典
    
    返回:
        dict: 质量检查结果
    """
    try:
        closes = ohlcv_data["closes"]
        highs = ohlcv_data["highs"]
        lows = ohlcv_data["lows"]
        volumes = ohlcv_data["volumes"]
        
        # 基本检查
        data_count = len(closes)
        zero_prices = sum(1 for c in closes if c <= 0)
        zero_volumes = sum(1 for v in volumes if v <= 0)
        
        # 价格合理性检查
        invalid_ohlc = 0
        for i in range(len(closes)):
            if highs[i] < lows[i] or closes[i] > highs[i] or closes[i] < lows[i]:
                invalid_ohlc += 1
        
        # 价格波动检查
        price_changes = []
        for i in range(1, len(closes)):
            change_pct = abs(closes[i] - closes[i-1]) / closes[i-1] * 100
            price_changes.append(change_pct)
        
        max_change = max(price_changes) if price_changes else 0
        avg_change = np.mean(price_changes) if price_changes else 0
        
        return {
            "data_count": data_count,
            "zero_prices": zero_prices,
            "zero_volumes": zero_volumes,
            "invalid_ohlc": invalid_ohlc,
            "max_price_change": max_change,
            "avg_price_change": avg_change,
            "quality_score": calculate_quality_score(data_count, zero_prices, zero_volumes, invalid_ohlc)
        }
        
    except Exception as e:
        return {"error": f"数据质量检查异常: {e}"}

def calculate_quality_score(data_count, zero_prices, zero_volumes, invalid_ohlc):
    """
    计算数据质量评分
    
    返回:
        int: 质量评分 (0-100)
    """
    score = 100
    
    # 数据量评分
    if data_count < 10:
        score -= 30
    elif data_count < 20:
        score -= 15
    
    # 零价格扣分
    if zero_prices > 0:
        score -= min(zero_prices * 10, 40)
    
    # 零成交量扣分
    if zero_volumes > 0:
        score -= min(zero_volumes * 5, 20)
    
    # 无效OHLC扣分
    if invalid_ohlc > 0:
        score -= min(invalid_ohlc * 15, 30)
    
    return max(0, score)

def calculate_technical_indicators(ohlcv_data):
    """
    计算技术指标
    
    参数:
        ohlcv_data: OHLCV数据字典
    
    返回:
        dict: 技术指标结果
    """
    try:
        closes = np.array(ohlcv_data["closes"])
        highs = np.array(ohlcv_data["highs"])
        lows = np.array(ohlcv_data["lows"])
        volumes = np.array(ohlcv_data["volumes"])
        
        indicators = {}
        
        # 计算ATR
        if len(closes) >= 15:
            atr_result = calculate_atr_simple(highs, lows, closes)
            indicators["atr"] = atr_result
        
        # 计算移动平均
        if len(closes) >= 5:
            indicators["ma5"] = np.mean(closes[-5:])
        if len(closes) >= 10:
            indicators["ma10"] = np.mean(closes[-10:])
        if len(closes) >= 20:
            indicators["ma20"] = np.mean(closes[-20:])
        
        # 计算价格统计
        indicators["current_price"] = closes[-1]
        indicators["price_range"] = np.max(closes) - np.min(closes)
        indicators["avg_volume"] = np.mean(volumes)
        
        return indicators
        
    except Exception as e:
        return {"error": f"技术指标计算异常: {e}"}

def calculate_atr_simple(highs, lows, closes, period=14):
    """
    简单ATR计算
    
    返回:
        dict: ATR计算结果
    """
    try:
        tr_list = []
        for i in range(1, len(highs)):
            tr1 = highs[i] - lows[i]
            tr2 = abs(highs[i] - closes[i-1])
            tr3 = abs(lows[i] - closes[i-1])
            tr = max(tr1, tr2, tr3)
            tr_list.append(tr)
        
        if len(tr_list) >= period:
            atr = np.mean(tr_list[-period:])
            return {
                "atr": round(atr, 4),
                "data_sufficient": True,
                "period": period,
                "tr_count": len(tr_list)
            }
        else:
            atr = np.mean(tr_list) if tr_list else 0
            return {
                "atr": round(atr, 4),
                "data_sufficient": False,
                "period": period,
                "tr_count": len(tr_list)
            }
            
    except Exception as e:
        return {"error": f"ATR计算异常: {e}"}

def display_data_summary(ohlcv_data, quality_result, indicators):
    """
    显示数据摘要
    """
    print(f"\n📊 数据摘要")
    print(f"-" * 30)
    print(f"📈 数据条数: {ohlcv_data['count']}")
    print(f"💰 当前价格: {ohlcv_data['closes'][-1]:.3f}")
    print(f"📊 价格区间: {min(ohlcv_data['closes']):.3f} ~ {max(ohlcv_data['closes']):.3f}")
    print(f"📦 平均成交量: {np.mean(ohlcv_data['volumes']):.0f}")
    
    print(f"\n🔍 数据质量")
    print(f"-" * 30)
    print(f"🎯 质量评分: {quality_result.get('quality_score', 0)}/100")
    print(f"❌ 零价格数: {quality_result.get('zero_prices', 0)}")
    print(f"❌ 零成交量数: {quality_result.get('zero_volumes', 0)}")
    print(f"❌ 无效OHLC数: {quality_result.get('invalid_ohlc', 0)}")
    
    print(f"\n📊 技术指标")
    print(f"-" * 30)
    if "atr" in indicators and "error" not in indicators["atr"]:
        atr_info = indicators["atr"]
        print(f"📈 ATR值: {atr_info['atr']:.4f}")
        print(f"📊 ATR数据充足: {'是' if atr_info['data_sufficient'] else '否'}")
        print(f"📋 TR数据点: {atr_info['tr_count']}")
    
    if "ma5" in indicators:
        print(f"📈 MA5: {indicators['ma5']:.3f}")
    if "ma10" in indicators:
        print(f"📈 MA10: {indicators['ma10']:.3f}")
    if "ma20" in indicators:
        print(f"📈 MA20: {indicators['ma20']:.3f}")

# ============================================================================
# 主测试函数
# ============================================================================

def run_historical_data_test(stock_code='123171.SZ', period_days=30):
    """
    运行历史数据测试
    
    参数:
        stock_code: 股票代码
        period_days: 获取天数
    """
    print(f"\n🚀 开始历史数据预热测试")
    print(f"📊 股票代码: {stock_code}")
    print(f"📅 测试周期: {period_days}天")
    
    # 测试不同K线周期
    test_periods = ['1d', '1h', '30m']
    
    results = {}
    
    for period in test_periods:
        print(f"\n{'='*60}")
        print(f"🔍 测试K线周期: {period}")
        
        result = test_historical_data_fetch(stock_code, period_days, period)
        results[period] = result
        
        if result["success"]:
            print(f"✅ {period} 数据获取成功")
        else:
            print(f"❌ {period} 数据获取失败: {result.get('error', '未知错误')}")
    
    # 显示测试总结
    print(f"\n{'='*60}")
    print(f"📋 测试总结")
    print(f"{'='*60}")
    
    success_count = sum(1 for r in results.values() if r["success"])
    total_count = len(results)
    
    print(f"✅ 成功: {success_count}/{total_count}")
    print(f"❌ 失败: {total_count - success_count}/{total_count}")
    
    # 推荐最佳数据源
    if success_count > 0:
        best_result = None
        best_score = 0
        
        for period, result in results.items():
            if result["success"]:
                score = result.get("quality", {}).get("quality_score", 0)
                if score > best_score:
                    best_score = score
                    best_result = (period, result)
        
        if best_result:
            period, result = best_result
            print(f"\n🏆 推荐数据源: {period} (质量评分: {best_score}/100)")
            print(f"📊 数据条数: {result.get('data_count', 0)}")
            print(f"🔧 获取方法: {result.get('method', '未知')}")
    
    return results

# ============================================================================
# 快速测试函数
# ============================================================================

def quick_data_test(ContextInfo):
    """
    快速数据获取测试 - 在QMT策略中调用

    参数:
        ContextInfo: QMT上下文对象
    """
    try:
        stock_code = getattr(ContextInfo, 'stock', '123171.SZ')
        print(f"\n🚀 快速数据获取测试")
        print(f"📊 股票代码: {stock_code}")

        # 测试当前数据获取
        try:
            # 注意：需要在QMT策略环境中调用
            print(f"⚠️ 此函数需要在QMT策略环境中调用，需要ContextInfo对象")
            return False

            if current_data and len(current_data) > 0:
                print(f"✅ 当前数据获取成功: {len(current_data)}条")

                # 简单数据分析
                if isinstance(current_data, dict) and stock_code in current_data:
                    stock_data = current_data[stock_code]
                    if len(stock_data) > 0:
                        latest = stock_data[-1]
                        print(f"📈 最新价格: {latest.get('close', 0):.3f}")
                        print(f"📊 最新成交量: {latest.get('volume', 0):.0f}")

                        # 计算简单ATR
                        if len(stock_data) >= 15:
                            closes = [float(item.get('close', 0)) for item in stock_data]
                            highs = [float(item.get('high', 0)) for item in stock_data]
                            lows = [float(item.get('low', 0)) for item in stock_data]

                            atr_result = calculate_atr_simple(highs, lows, closes)
                            if "error" not in atr_result:
                                print(f"📊 ATR值: {atr_result['atr']:.4f}")
                                print(f"🎯 数据充足: {'是' if atr_result['data_sufficient'] else '否'}")

                return {"success": True, "data_count": len(current_data)}
            else:
                print(f"❌ 数据获取失败: 返回数据为空")
                return {"success": False, "error": "数据为空"}

        except Exception as e:
            print(f"❌ 数据获取异常: {e}")
            return {"success": False, "error": str(e)}

    except Exception as e:
        print(f"❌ 快速测试异常: {e}")
        return {"success": False, "error": str(e)}

def test_data_warmup_for_strategy(ContextInfo, required_bars=30):
    """
    为策略提供数据预热测试

    参数:
        ContextInfo: QMT上下文对象
        required_bars: 需要的K线数量

    返回:
        dict: 预热数据结果
    """
    try:
        stock_code = getattr(ContextInfo, 'stock', '123171.SZ')
        print(f"\n🔥 策略数据预热测试")
        print(f"📊 股票代码: {stock_code}")
        print(f"📋 需要K线数: {required_bars}")

        # 获取历史数据
        # 注意：需要在QMT策略环境中调用
        print(f"⚠️ 此函数需要在QMT策略环境中调用，需要ContextInfo对象")
        return {"success": False, "error": "需要QMT环境"}

        if not hist_data or len(hist_data) == 0:
            return {
                "success": False,
                "error": "无法获取历史数据",
                "recommendation": "检查股票代码和网络连接"
            }

        # 处理数据
        if isinstance(hist_data, dict) and stock_code in hist_data:
            stock_data = hist_data[stock_code]
            data_count = len(stock_data)

            print(f"✅ 获取到 {data_count} 条历史数据")

            if data_count >= required_bars:
                print(f"🎯 数据充足，可以进行策略预热")

                # 提取OHLCV数据
                closes = [float(item.get('close', 0)) for item in stock_data]
                highs = [float(item.get('high', 0)) for item in stock_data]
                lows = [float(item.get('low', 0)) for item in stock_data]
                volumes = [float(item.get('volume', 0)) for item in stock_data]

                # 计算预热指标
                warmup_indicators = {
                    "data_count": data_count,
                    "current_price": closes[-1],
                    "price_range": max(closes) - min(closes),
                    "avg_volume": np.mean(volumes),
                    "atr_ready": False
                }

                # 计算ATR
                if data_count >= 15:
                    atr_result = calculate_atr_simple(highs, lows, closes)
                    if "error" not in atr_result:
                        warmup_indicators["atr"] = atr_result["atr"]
                        warmup_indicators["atr_ready"] = atr_result["data_sufficient"]

                print(f"📊 预热指标计算完成")
                print(f"   💰 当前价格: {warmup_indicators['current_price']:.3f}")
                print(f"   📈 ATR就绪: {'是' if warmup_indicators['atr_ready'] else '否'}")

                return {
                    "success": True,
                    "data_ready": True,
                    "indicators": warmup_indicators,
                    "ohlcv": {
                        "closes": closes,
                        "highs": highs,
                        "lows": lows,
                        "volumes": volumes
                    }
                }
            else:
                print(f"⚠️ 数据不足: 需要{required_bars}条，实际{data_count}条")
                return {
                    "success": True,
                    "data_ready": False,
                    "data_count": data_count,
                    "required": required_bars,
                    "recommendation": "等待更多数据或降低required_bars参数"
                }
        else:
            return {
                "success": False,
                "error": "数据格式异常",
                "recommendation": "检查股票代码格式"
            }

    except Exception as e:
        print(f"❌ 数据预热测试异常: {e}")
        return {
            "success": False,
            "error": str(e),
            "recommendation": "检查QMT API可用性"
        }

# ============================================================================
# 模块初始化
# ============================================================================

# ============================================================================
# 简化测试函数 - 专门用于QMT策略调用
# ============================================================================

def simple_data_test(C):
    """
    简化的数据测试函数 - 在QMT策略中调用

    参数:
        C: QMT上下文对象 (ContextInfo)
    """
    try:
        stock_code = getattr(C, 'stock', '123171.SZ')
        print(f"\n🔍 简化数据测试")
        print(f"� 股票代码: {stock_code}")

        # 测试基本数据获取
        try:
            # 使用QMT标准API获取数据（参考获取历史数据文件）
            current_data = C.get_market_data_ex(
                ['open', 'high', 'low', 'close', 'volume'],  # 字段列表
                [stock_code],                                 # 证券代码列表
                period='1d',
                count=20,
                dividend_type='none'
            )

            if current_data and stock_code in current_data:
                data_list = current_data[stock_code]
                data_count = len(data_list)

                print(f"✅ 数据获取成功: {data_count}条")
                print(f"📊 数据类型: {type(data_list)}")

                # 检查数据格式
                if data_count > 0:
                    print(f"📊 第一条数据类型: {type(data_list[0])}")
                    print(f"📊 第一条数据内容: {data_list[0]}")

                if data_count >= 15:
                    # 安全提取价格数据
                    try:
                        if hasattr(data_list, 'iloc'):
                            # DataFrame格式
                            closes = data_list['close'].values.tolist()
                            highs = data_list['high'].values.tolist()
                            lows = data_list['low'].values.tolist()
                        else:
                            # 列表格式，需要检查每个item的类型
                            closes = []
                            highs = []
                            lows = []

                            for item in data_list:
                                if hasattr(item, 'get'):
                                    # 字典格式
                                    closes.append(float(item.get('close', 0)))
                                    highs.append(float(item.get('high', 0)))
                                    lows.append(float(item.get('low', 0)))
                                elif hasattr(item, '__getitem__'):
                                    # 可索引对象
                                    closes.append(float(item['close']))
                                    highs.append(float(item['high']))
                                    lows.append(float(item['low']))
                                else:
                                    print(f"❌ 无法解析数据项: {type(item)}")
                                    return False

                        print(f"📊 提取到价格数据: {len(closes)}条")

                    except Exception as e:
                        print(f"❌ 价格数据提取异常: {e}")
                        return False

                    # 计算简单ATR
                    if len(closes) >= 15:
                        tr_list = []
                        for i in range(1, len(highs)):
                            tr1 = highs[i] - lows[i]
                            tr2 = abs(highs[i] - closes[i-1])
                            tr3 = abs(lows[i] - closes[i-1])
                            tr = max(tr1, tr2, tr3)
                            tr_list.append(tr)

                        if len(tr_list) >= 14:
                            atr = np.mean(tr_list[-14:])
                            print(f"� ATR计算成功: {atr:.4f}")
                            print(f"🎯 数据充足，可以进行策略运行")
                            return True
                        else:
                            print(f"⚠️ TR数据不足: {len(tr_list)}/14")
                    else:
                        print(f"⚠️ 价格数据不足: {len(closes)}/15")
                else:
                    print(f"⚠️ 历史数据不足: {data_count}/15")

            else:
                print(f"❌ 数据获取失败: 返回数据为空")

        except Exception as e:
            print(f"❌ 数据获取异常: {e}")

        return False

    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_current_market_data(C):
    """
    测试当前市场数据获取

    参数:
        C: QMT上下文对象
    """
    try:
        stock_code = getattr(C, 'stock', '123171.SZ')
        print(f"\n📊 当前市场数据测试")

        # 获取实时数据
        current_data = C.get_market_data_ex(
            ['open', 'high', 'low', 'close', 'volume'],  # 字段列表
            [stock_code],                                 # 证券代码列表
            period='1d',
            count=1,
            dividend_type='none'
        )

        if current_data and stock_code in current_data:
            latest_data = current_data[stock_code]
            print(f"📊 数据类型: {type(latest_data)}")

            if len(latest_data) > 0:
                latest = latest_data[0] if isinstance(latest_data, list) else latest_data.iloc[0]
                print(f"📊 最新数据类型: {type(latest)}")

                # 安全获取数据
                try:
                    if hasattr(latest, 'get'):
                        # 字典格式
                        price = latest.get('close', 0)
                        volume = latest.get('volume', 0)
                        high = latest.get('high', 0)
                        low = latest.get('low', 0)
                    elif hasattr(latest, '__getitem__'):
                        # 可索引对象
                        price = latest['close']
                        volume = latest['volume']
                        high = latest['high']
                        low = latest['low']
                    else:
                        print(f"❌ 无法解析数据格式: {type(latest)}")
                        return False

                    print(f"💰 当前价格: {price:.3f}")
                    print(f"📈 今日最高: {high:.3f}")
                    print(f"📉 今日最低: {low:.3f}")
                    print(f"📦 成交量: {volume:.0f}")

                    if price > 0 and volume >= 0:
                        print(f"✅ 当前数据正常")
                        return True
                    else:
                        print(f"⚠️ 当前数据异常")
                        return False

                except Exception as e:
                    print(f"❌ 数据解析异常: {e}")
                    return False

        print(f"❌ 无法获取当前数据")
        return False

    except Exception as e:
        print(f"❌ 当前数据测试异常: {e}")
        return False

# ============================================================================
# 策略入口点
# ============================================================================

if __name__ == "__main__":
    print("🔍 QMT历史数据预热测试策略")
    print("📋 这是一个QMT策略文件，请在QMT中加载运行")
    print("\n💡 主要功能:")
    print("1. 测试历史数据获取能力")
    print("2. 验证ATR计算所需数据")
    print("3. 检查数据质量和完整性")
    print("4. 为策略运行提供数据预热")
    print("\n🚀 使用方法:")
    print("1. 在QMT中加载此策略文件")
    print("2. 设置要测试的股票代码")
    print("3. 运行策略查看测试结果")
