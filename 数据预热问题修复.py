#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据预热问题修复

问题描述：
用户报告数据预热进度显示异常：3455/3，导致策略无法正常执行交易逻辑

问题原因：
1. 数据预热函数每次调用都会增加计数器
2. 在回测环境中，函数可能被多次调用导致计数异常

解决方案：
1. 使用ContextInfo.barpos作为当前K线数量，避免重复计数
2. 优化数据预热逻辑，支持数据缓冲区模式和传统模式
3. 添加更好的状态管理和错误处理

修复后的check_data_warmup函数：
"""

def check_data_warmup_fixed(ContextInfo):
    """
    修复后的数据预热检查函数
    
    参数:
        ContextInfo: QMT上下文对象
    
    返回:
        dict: 预热状态信息
    """
    try:
        # 获取策略配置
        STRATEGY_CONFIG = {
            'enable_data_buffer': True,
            'enable_data_warmup': True,
            'data_warmup_bars': 3,
            'min_trading_periods': 20
        }
        
        # 如果启用了数据缓冲区且已初始化，则跳过传统数据预热
        if (STRATEGY_CONFIG.get('enable_data_buffer', False) and 
            hasattr(ContextInfo, 'data_buffer') and 
            getattr(ContextInfo, 'buffer_initialized', False)):
            
            # 检查数据缓冲区是否有足够数据
            min_periods = STRATEGY_CONFIG.get('min_trading_periods', 20)
            if ContextInfo.data_buffer.is_ready_for_trading(min_periods):
                return {
                    'warmup_complete': True, 
                    'reason': f'数据缓冲区已就绪 ({ContextInfo.data_buffer.get_data_count()}根K线)'
                }
            else:
                return {
                    'warmup_complete': False,
                    'reason': f'数据缓冲区预热中 ({ContextInfo.data_buffer.get_data_count()}/{min_periods})'
                }
        
        # 传统数据预热模式
        if not STRATEGY_CONFIG['enable_data_warmup']:
            return {'warmup_complete': True, 'reason': '数据预热已禁用'}

        # 关键修复：使用barpos作为当前K线数量，避免重复计数
        current_bars = ContextInfo.barpos + 1  # barpos从0开始，所以+1
        required_bars = STRATEGY_CONFIG['data_warmup_bars']

        # 初始化预热状态（仅用于记录，不用于计数）
        if not hasattr(ContextInfo, 'warmup_state'):
            ContextInfo.warmup_state = {
                'start_time': str(ContextInfo.get_bar_timetag(ContextInfo.barpos)),
                'warmup_complete': False
            }
            print(f"📊 开始数据预热: {ContextInfo.warmup_state['start_time']}")

        print(f"📈 数据预热进度: {current_bars}/{required_bars}")

        # 检查是否完成预热
        if current_bars >= required_bars:
            if not ContextInfo.warmup_state['warmup_complete']:
                ContextInfo.warmup_state['warmup_complete'] = True
                print(f"✅ 数据预热完成: 已接收{current_bars}根K线")

            return {
                'warmup_complete': True,
                'reason': f'数据预热完成 ({current_bars}根K线)',
                'bar_count': current_bars
            }

        # 未完成预热
        return {
            'warmup_complete': False,
            'reason': f'数据预热中 ({current_bars}/{required_bars})',
            'bar_count': current_bars,
            'remaining': required_bars - current_bars
        }

    except Exception as e:
        print(f"❌ 数据预热检查异常: {e}")
        # 发生异常时，默认认为预热完成，避免阻塞策略执行
        return {'warmup_complete': True, 'reason': f'预热检查异常，跳过预热: {e}'}

def test_warmup_logic():
    """测试数据预热逻辑"""
    
    class MockContextInfo:
        def __init__(self):
            self.barpos = 0
            
        def get_bar_timetag(self, pos):
            return "20240101093000"
    
    print("🧪 测试数据预热逻辑")
    print("=" * 50)
    
    # 创建模拟对象
    C = MockContextInfo()
    
    # 测试前3根K线
    for i in range(5):
        C.barpos = i
        result = check_data_warmup_fixed(C)
        
        print(f"K线 {i+1}: {result['reason']}")
        print(f"   预热完成: {result['warmup_complete']}")
        
        if result['warmup_complete']:
            print("   ✅ 可以开始交易")
        else:
            print("   ⏳ 继续预热")
        print()

if __name__ == "__main__":
    test_warmup_logic()

"""
修复要点总结：

1. 问题根源：
   - 原代码：state['bar_count'] += 1  # 每次调用都增加
   - 修复后：current_bars = ContextInfo.barpos + 1  # 使用实际K线位置

2. 优化逻辑：
   - 支持数据缓冲区模式和传统模式
   - 更好的错误处理
   - 避免重复计数

3. 预期效果：
   - 数据预热进度正常显示：1/3, 2/3, 3/3
   - 第4根K线开始正常执行交易逻辑
   - 不再出现异常的大数字

4. 使用方法：
   - 将修复后的check_data_warmup函数替换到策略文件中
   - 重新运行策略，观察数据预热进度是否正常
"""
