#coding:gbk

"""
回测示例-简化版-集成止盈止损策略
====================================

策略概述：
基于原回测示例框架，集成简化版的止盈止损功能。
专注于解决数据获取和时间转换问题，确保策略能正常运行。

策略特点：
- 基础框架：双均线交叉系统（10日/20日）
- 止盈止损：ATR动态止损 + 固定止盈
- 数据处理：安全的数据获取和错误处理
- 适用环境：QMT回测环境

核心功能：
✅ 双均线交叉买入信号
✅ ATR动态止损
✅ 固定百分比止盈
✅ 安全的数据获取
✅ 错误处理和日志输出

技术指标：
- 快线：10日简单移动平均线
- 慢线：20日简单移动平均线
- ATR周期：14日
- 止损：3倍ATR或固定2%
- 止盈：固定5%

交易逻辑：
- 买入：快线上穿慢线
- 卖出：ATR止损 或 固定止盈 或 快线下穿慢线
"""

# 导入必要的库
import pandas as pd
import numpy as np
# QMT内置函数：timetag_to_datetime, get_trade_detail_data, passorder

# ============================================================================
# 策略配置参数
# ============================================================================

STRATEGY_CONFIG = {
    'atr_period': 14,                   # ATR计算周期
    'atr_stop_multiplier': 3.0,         # ATR止损倍数
    'fixed_stop_loss': 0.02,            # 固定止损2%
    'fixed_take_profit': 0.05,          # 固定止盈5%
    'max_position_ratio': 0.8,          # 最大仓位80%
    'min_cash_reserve': 1000,           # 最小资金保留
}

# ============================================================================
# 技术指标计算函数
# ============================================================================

def calculate_atr_simple(highs, lows, closes, period=14):
    """
    简化版ATR计算
    
    参数:
        highs: 最高价数组
        lows: 最低价数组
        closes: 收盘价数组
        period: ATR周期
    
    返回:
        float: ATR值
    """
    try:
        if len(closes) < period + 1:
            # 数据不足，使用简单波幅估算
            if len(closes) >= 2:
                price_range = max(closes) - min(closes)
                return price_range * 0.3  # 保守估算
            else:
                return closes[-1] * 0.02 if closes else 0.02  # 2%作为默认ATR
        
        # 计算真实波幅
        tr_list = []
        for i in range(1, len(closes)):
            high = highs[i] if i < len(highs) else closes[i]
            low = lows[i] if i < len(lows) else closes[i]
            prev_close = closes[i-1]
            
            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)
            tr = max(tr1, tr2, tr3)
            tr_list.append(tr)
        
        # 计算ATR（简单移动平均）
        if len(tr_list) >= period:
            atr = np.mean(tr_list[-period:])
        else:
            atr = np.mean(tr_list)
        
        return max(atr, closes[-1] * 0.005)  # 最小0.5%保护
    
    except Exception as e:
        print(f"❌ ATR计算失败: {e}")
        return closes[-1] * 0.02 if closes else 0.02

def safe_get_market_data(C, fields=['close'], count=None):
    """
    安全获取市场数据
    
    参数:
        C: ContextInfo对象
        fields: 数据字段列表
        count: 数据数量
    
    返回:
        dict: 包含数据的字典
    """
    try:
        # 获取时间
        bar_date = str(C.get_bar_timetag(C.barpos))
        
        # 设置默认数量
        if count is None:
            count = max(20, STRATEGY_CONFIG['atr_period'] + 5)
        
        # 获取数据
        local_data = C.get_market_data_ex(fields, [C.stock], end_time=bar_date, period=C.period, count=count, subscribe=False)
        
        if not local_data or C.stock not in local_data:
            print(f"⚠️ 无法获取{C.stock}的数据")
            return None
        
        stock_data = local_data[C.stock]
        print(f"📊 获取数据: {stock_data.shape}, 列数: {len(stock_data.columns)}")
        
        # 安全提取数据
        result = {}
        for i, field in enumerate(fields):
            if i < len(stock_data.columns):
                result[field] = list(stock_data.iloc[:, i])
            else:
                # 如果列数不足，使用第一列数据
                result[field] = list(stock_data.iloc[:, 0]) if len(stock_data.columns) > 0 else []
        
        return result
    
    except Exception as e:
        print(f"❌ 获取市场数据失败: {e}")
        return None

# ============================================================================
# 策略状态管理
# ============================================================================

def update_position_state(C, current_price):
    """
    更新持仓状态
    
    参数:
        C: ContextInfo对象
        current_price: 当前价格
    """
    try:
        # 初始化状态
        if not hasattr(C, 'position_state'):
            C.position_state = {
                'entry_price': 0,
                'entry_time': '',
                'highest_price': 0,
                'has_position': False
            }
        
        # 获取持仓信息
        try:
            holdings = get_trade_detail_data('test', 'stock', 'position')
            holdings_dict = {i.m_strInstrumentID + '.' + i.m_strExchangeID : i.m_nVolume for i in holdings}
            current_holding = holdings_dict.get(C.stock, 0)
        except:
            current_holding = 0
        
        state = C.position_state
        
        if current_holding > 0:
            if not state['has_position']:
                # 新建仓位
                state['entry_price'] = current_price
                state['entry_time'] = str(C.get_bar_timetag(C.barpos))
                state['highest_price'] = current_price
                state['has_position'] = True
                print(f"📊 新建仓位: 入场价={current_price:.3f}")
            else:
                # 更新最高价
                if current_price > state['highest_price']:
                    state['highest_price'] = current_price
        else:
            if state['has_position']:
                print(f"📊 清空仓位")
            state['has_position'] = False
            state['entry_price'] = 0
            state['highest_price'] = 0
    
    except Exception as e:
        print(f"❌ 状态更新失败: {e}")

# ============================================================================
# QMT策略框架函数
# ============================================================================

def init(C):
    """
    策略初始化函数
    """
    # 构建完整的股票代码
    C.stock = C.stockcode + '.' + C.market
    
    # 设置双均线参数
    C.line1 = 10   # 快线
    C.line2 = 20   # 慢线
    
    # 设置回测账户ID
    C.accountid = "testS"
    
    # 初始化状态
    C.position_state = {
        'entry_price': 0,
        'entry_time': '',
        'highest_price': 0,
        'has_position': False
    }
    
    print(f"🚀 策略初始化完成")
    print(f"📊 交易标的: {C.stock}")
    print(f"📈 快线: {C.line1}日, 慢线: {C.line2}日")
    print(f"🎯 ATR周期: {STRATEGY_CONFIG['atr_period']}日")

def handlebar(C):
    """
    K线处理函数（简化版集成止盈止损）
    """
    
    # === 第一步：获取时间和基础数据 ===
    
    bar_date = str(C.get_bar_timetag(C.barpos))
    
    # 获取收盘价数据
    close_data = safe_get_market_data(C, ['close'], max(C.line1, C.line2))
    if not close_data or not close_data['close']:
        print(f"{bar_date} 无法获取收盘价数据，跳过")
        return
    
    close_list = close_data['close']
    current_price = close_list[-1]
    
    print(f"\n📊 {bar_date} 当前价格: {current_price:.3f}")
    
    # === 第二步：检查数据充足性 ===
    
    if len(close_list) < max(C.line1, C.line2):
        print(f'{bar_date} 数据不足，跳过 (需要{max(C.line1, C.line2)}根，实际{len(close_list)}根)')
        return
    
    # === 第三步：计算技术指标 ===
    
    # 计算双均线
    line1_mean = round(np.mean(close_list[-C.line1:]), 2)
    line2_mean = round(np.mean(close_list[-C.line2:]), 2)
    
    print(f"📊 技术指标: 快线{line1_mean} 慢线{line2_mean}")
    
    # 计算ATR（获取更多数据用于ATR计算）
    extended_data = safe_get_market_data(C, ['close', 'high', 'low'], STRATEGY_CONFIG['atr_period'] + 10)
    if extended_data:
        atr = calculate_atr_simple(
            extended_data.get('high', extended_data['close']),
            extended_data.get('low', extended_data['close']),
            extended_data['close'],
            STRATEGY_CONFIG['atr_period']
        )
    else:
        atr = current_price * 0.02  # 默认2%
    
    print(f"🔧 ATR: {atr:.3f}")
    
    # === 第四步：获取账户信息 ===
    
    try:
        account = get_trade_detail_data('test', 'stock', 'account')
        if not account:
            print(f"{bar_date} 无法获取账户信息，跳过")
            return
        
        available_cash = int(account[0].m_dAvailable)
        
        holdings = get_trade_detail_data('test', 'stock', 'position')
        holdings_dict = {i.m_strInstrumentID + '.' + i.m_strExchangeID : i.m_nVolume for i in holdings}
        holding_vol = holdings_dict.get(C.stock, 0)
        
        print(f"💼 账户: 资金{available_cash:.0f}, 持仓{holding_vol}股")
    except Exception as e:
        print(f"❌ 获取账户信息失败: {e}")
        return
    
    # === 第五步：更新状态 ===
    
    update_position_state(C, current_price)
    state = C.position_state
    
    # === 第六步：交易逻辑 ===
    
    if holding_vol == 0:
        # 无持仓，检查买入条件
        if line1_mean > line2_mean:
            vol = int(available_cash * STRATEGY_CONFIG['max_position_ratio'] / current_price / 100) * 100
            
            if vol >= 100 and available_cash > STRATEGY_CONFIG['min_cash_reserve']:
                try:
                    passorder(23, 1101, C.accountid, C.stock, 5, -1, vol, C)
                    print(f"✅ {bar_date} 买入: {vol}股")
                    C.draw_text(1, 1, '买')
                except Exception as e:
                    print(f"❌ 买入失败: {e}")
            else:
                print(f"⚠️ 资金不足: 需要{vol}股")
    
    else:
        # 有持仓，检查卖出条件
        entry_price = state['entry_price']
        if entry_price > 0:
            profit_pct = (current_price - entry_price) / entry_price
            
            # 止损条件
            atr_stop = entry_price - atr * STRATEGY_CONFIG['atr_stop_multiplier']
            fixed_stop = entry_price * (1 - STRATEGY_CONFIG['fixed_stop_loss'])
            stop_price = max(atr_stop, fixed_stop)
            
            # 止盈条件
            take_profit_price = entry_price * (1 + STRATEGY_CONFIG['fixed_take_profit'])
            
            should_sell = False
            sell_reason = ""
            
            if current_price <= stop_price:
                should_sell = True
                sell_reason = f"止损 (ATR止损{atr_stop:.3f}, 固定止损{fixed_stop:.3f})"
            elif current_price >= take_profit_price:
                should_sell = True
                sell_reason = f"止盈 (目标价{take_profit_price:.3f})"
            elif line1_mean < line2_mean:
                should_sell = True
                sell_reason = "死叉信号"
            
            if should_sell:
                try:
                    passorder(24, 1101, C.accountid, C.stock, 5, -1, holding_vol, C)
                    print(f"🎯 {bar_date} 卖出: {sell_reason}")
                    print(f"   💰 盈亏: {profit_pct:.2%}")
                    C.draw_text(1, 1, '卖')
                except Exception as e:
                    print(f"❌ 卖出失败: {e}")
            else:
                print(f"📈 持仓中: 盈亏{profit_pct:.2%}, 止损价{stop_price:.3f}")
    
    print(f"📊 {bar_date} 处理完成\n" + "="*50)
