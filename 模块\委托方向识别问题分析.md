# QMT委托方向识别问题分析与解决方案

## 🔍 **问题现象**

根据用户提供的实际运行日志：

```
委托方向调试: m_nOffsetFlag=48, m_nDirection=48
发现1个未处理订单: 买卖未知(offset=48,dir=48) 10股@136.674 [已报待成交]
```

**问题**：
- 预期的 `m_nOffsetFlag` 值应该是 23（买入）或 24（卖出）
- 实际获取到的值是 48，导致无法正确识别买卖方向
- 显示为"买卖未知"，影响后续的委托管理逻辑

## 📋 **原因分析**

### 1. **文档与实际的差异**
- QMT官方文档显示 `m_nOffsetFlag` 的 EOffset_Flag_Type 枚举值为 23=买入, 24=卖出
- 但实际运行中获取到的是 48，可能是：
  - 不同版本的QMT使用不同的枚举值
  - 股票和期货使用不同的编码系统
  - 文档中的枚举值不完整

### 2. **字段含义的混淆**
- `m_nDirection`: 根据文档，股票的该字段始终是48
- `m_nOffsetFlag`: 应该用于区分买卖方向，但实际值与预期不符

## 🔧 **解决方案**

### **方案1：多重方向识别机制**

我们已经在代码中实现了多重识别机制：

```python
# 方法1：通过m_strOptName字段判断（中文描述）- 最可靠
if '买' in opt_name or '买入' in opt_name:
    op_type_desc = '买入'
elif '卖' in opt_name or '卖出' in opt_name:
    op_type_desc = '卖出'

# 方法2：通过标准的EOffset_Flag_Type枚举
elif order_op_type in [23, '23']:
    op_type_desc = '买入'
elif order_op_type in [24, '24']:
    op_type_desc = '卖出'

# 方法3：通过价格与市价比较推测
elif order_op_type == 48 and direction_field == 48:
    # 价格推测逻辑
    if price_diff_pct > 0.005:  # 高于市价0.5%以上
        op_type_desc = '卖出(价格推测)'
    elif price_diff_pct < -0.005:  # 低于市价0.5%以上
        op_type_desc = '买入(价格推测)'
```

### **方案2：调试工具**

创建了专门的调试模块：
- `QMT委托方向调试.py` - 详细分析委托对象的所有字段
- `debug_order_object()` 函数 - 显示委托对象的完整属性列表

## 🎯 **建议的测试步骤**

### 1. **运行调试模块**
```python
# 使用 QMT委托方向调试.py
# 查看委托对象的所有字段和值
```

### 2. **关键字段检查**
重点检查以下字段：
- `m_strOptName` - 中文描述（最可靠）
- `m_eEntrustType` - 委托类别
- `m_eFutureTradeType` - 成交类型
- 其他可能的方向字段

### 3. **价格推测验证**
- 比较委托价格与当前市价
- 验证价格推测的准确性

## 📊 **预期结果**

运行调试模块后，应该能够：

1. **找到正确的方向字段**：
   ```
   🔍 方向相关字段:
   m_strOptName: '买入' → 买入
   m_eEntrustType: 1 → 买入
   ```

2. **确认价格推测的有效性**：
   ```
   方法4 (价格推测): 委托136.674 vs 市价136.941 → 可能是买入
   ```

3. **获得完整的字段列表**：
   ```
   📋 所有属性:
   1. m_strOrderSysID: 12345678
   2. m_strOptName: 买入
   3. m_eEntrustType: 1
   ...
   ```

## ⚠️ **临时解决方案**

在找到正确的识别方法之前，可以：

1. **手动指定方向**：
   ```python
   # 在委托管理中临时硬编码
   if order_op_type == 48:
       # 根据具体情况手动判断
       op_type_desc = '买入'  # 或 '卖出'
   ```

2. **跳过方向检查**：
   ```python
   # 暂时处理所有未成交委托，不区分买卖方向
   if order['is_pending']:
       # 统一撤单处理
   ```

## 🔄 **后续优化**

1. **建立字段映射表**：
   ```python
   QMT_DIRECTION_MAPPING = {
       48: '需要进一步判断',
       23: '买入',
       24: '卖出'
   }
   ```

2. **版本兼容性处理**：
   ```python
   def get_order_direction(order_obj, qmt_version='auto'):
       # 根据QMT版本使用不同的识别逻辑
   ```

3. **机器学习辅助**：
   ```python
   # 基于历史数据训练方向识别模型
   # 使用价格、时间、数量等特征
   ```

## 📞 **需要用户配合**

请运行 `QMT委托方向调试.py` 并提供：

1. **完整的字段列表**
2. **m_strOptName 字段的值**
3. **m_eEntrustType 字段的值**
4. **任何包含"买"或"卖"字样的字段**

这将帮助我们找到正确的方向识别方法。
