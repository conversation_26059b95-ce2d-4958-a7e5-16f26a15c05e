# coding: gbk
"""
QMT下单、委托查询、撤单功能测试模块

基于QMT官方文档和实际运行经验编写
测试功能：
1. 下单功能测试
2. 委托查询功能测试  
3. 撤单功能测试
4. 委托方向识别测试

使用方法：
1. 在QMT中加载此策略
2. 设置为tick周期运行
3. 观察测试结果和日志输出
"""

import time
import datetime

# 测试配置
TEST_CONFIG = {
    # 基本配置
    'test_stock': '127094.SZ',          # 测试股票代码（请修改为您要测试的股票）
    'test_volume': 10,                  # 测试数量（建议使用小数量）
    'test_offset_ratio': 0.01,          # 测试偏移比例（1%，确保不会立即成交）

    # 安全配置
    'enable_real_trading': False,       # ⚠️ 是否启用真实交易（False=仅测试查询，True=真实下单撤单）
    'max_test_orders': 3,               # 最大测试订单数量（防止过度下单）
    'safety_check': True,               # 安全检查（防止误操作）

    # 调试配置
    'debug_mode': True,                 # 调试模式（显示详细信息）
    'debug_order_fields': False,        # 是否显示订单对象的所有字段
    'test_interval': 30,                # 测试执行间隔（秒）- tick模式下建议30秒执行一次测试操作

    # 高级配置
    'auto_cycle': True,                 # 是否自动循环测试
    'test_account_info': True,          # 是否测试账户信息查询

    # 查询过滤配置
    'filter_cancelled_orders': True,    # 是否过滤已撤销的委托（只显示有效委托）
    'show_only_pending': False,         # 是否只显示未处理的委托

    # 下单参数配置（简化版）
    'buy_offset_ratio': -0.01,          # 买入偏移比例（负值=低于市价买入）

    # 触发条件配置（简化版）
    'enable_trigger_condition': True,   # 是否启用触发条件
    'trigger_data_count': 3,            # 触发所需的数据点数量（只需要3次数据）
}

def init(ContextInfo):
    """初始化函数"""
    print("=" * 60)
    print("🧪 QMT下单撤单测试模块启动")
    print("=" * 60)
    
    # 设置账户信息
    try:
        ContextInfo.acct = account
        ContextInfo.acct_type = accountType
        print(f"📊 使用账户: {ContextInfo.acct} (类型: {ContextInfo.acct_type})")
    except NameError:
        ContextInfo.acct = 'test_account'
        ContextInfo.acct_type = 'STOCK'
        print(f"🧪 使用测试账户: {ContextInfo.acct}")
    
    # 初始化测试状态
    ContextInfo.test_phase = 'query'  # 测试阶段：query -> order -> wait -> cancel
    ContextInfo.last_test_time = 0
    ContextInfo.test_order_id = None
    ContextInfo.test_order_count = 0  # 测试订单计数

    # 初始化数据缓冲区（用于触发条件判断）
    from collections import deque
    ContextInfo.price_buffer = deque(maxlen=TEST_CONFIG['trigger_data_count'])
    ContextInfo.volume_buffer = deque(maxlen=TEST_CONFIG['trigger_data_count'])
    ContextInfo.time_buffer = deque(maxlen=TEST_CONFIG['trigger_data_count'])
    ContextInfo.trigger_conditions_met = False

    # 安全检查
    if TEST_CONFIG['enable_real_trading'] and TEST_CONFIG['safety_check']:
        print("⚠️ 真实交易模式已启用！")
        print("⚠️ 请确认以下设置:")
        print(f"   测试股票: {TEST_CONFIG['test_stock']}")
        print(f"   测试数量: {TEST_CONFIG['test_volume']}股")
        print(f"   偏移比例: {TEST_CONFIG['test_offset_ratio']*100:.1f}%")
        print(f"   最大测试订单: {TEST_CONFIG['max_test_orders']}个")
        print("⚠️ 如需禁用真实交易，请设置 enable_real_trading=False")

    print(f"🎯 测试股票: {TEST_CONFIG['test_stock']}")
    print(f"🔧 真实交易: {'✅ 启用' if TEST_CONFIG['enable_real_trading'] else '❌ 仅查询测试'}")
    print("=" * 60)

    # 检查QMT API可用性
    get_qmt_api_status()

def handlebar(ContextInfo):
    """
    主测试逻辑 - 与主策略模块保持一致的处理方式

    参考：QMT止盈止损下单模块.py 的 handlebar 函数
    """
    try:
        # 🔒 首先进行安全检查 - 只在最新K线执行逻辑（与主策略模块一致）
        if not ContextInfo.is_last_bar():
            return

        current_time = time.time()

        # 显示当前状态
        print(f"📊 数据接收 {datetime.datetime.now().strftime('%H:%M:%S')} | "
              f"缓冲:{len(ContextInfo.price_buffer)}/{TEST_CONFIG['trigger_data_count']} | "
              f"阶段:{ContextInfo.test_phase}")

        # 1. 收集市场数据（使用与主策略模块相同的方法）
        collect_market_data(ContextInfo)

        # 2. 检查触发条件
        check_trigger_conditions(ContextInfo)

        # 3. 控制测试执行频率
        if current_time - ContextInfo.last_test_time < TEST_CONFIG['test_interval']:
            # 显示当前状态但不执行测试操作
            if ContextInfo.trigger_conditions_met:
                print(f"   🎯 触发条件已满足，等待执行...")
            return

        # 执行测试操作的时间到了
        ContextInfo.last_test_time = current_time

        print(f"\n{'='*50}")
        print(f"🧪 执行测试操作 - {datetime.datetime.now().strftime('%H:%M:%S')}")
        print(f"🔄 当前阶段: {ContextInfo.test_phase}")
        print(f"📊 数据状态: 缓冲区{len(ContextInfo.price_buffer)}个数据点")
        print(f"🎯 触发条件: {'✅满足' if ContextInfo.trigger_conditions_met else '❌未满足'}")
        print(f"{'='*50}")

        # 4. 查询委托状态
        test_query_orders(ContextInfo)

        # 5. 根据阶段执行不同测试
        if ContextInfo.test_phase == 'query':
            test_account_info(ContextInfo)
            if TEST_CONFIG['enable_real_trading'] and ContextInfo.trigger_conditions_met:
                ContextInfo.test_phase = 'order'
                print("🎯 触发条件满足，准备下单测试")
        elif ContextInfo.test_phase == 'order':
            test_place_order_with_offset(ContextInfo)
            ContextInfo.test_phase = 'wait'
        elif ContextInfo.test_phase == 'wait':
            # 等待让订单生效
            print("⏳ 等待订单生效...")
            ContextInfo.test_phase = 'cancel'
        elif ContextInfo.test_phase == 'cancel':
            test_cancel_order(ContextInfo)
            ContextInfo.test_phase = 'query'
            ContextInfo.trigger_conditions_met = False  # 重置触发条件

        # 显示测试总结
        print_test_summary(ContextInfo)

    except Exception as e:
        print(f"❌ handlebar执行异常: {str(e)}")
        import traceback
        traceback.print_exc()

def collect_market_data(ContextInfo):
    """收集市场数据用于触发条件判断"""
    try:
        stock_code = TEST_CONFIG['test_stock']

        # 使用与主策略模块相同的方法获取tick数据
        current_data = ContextInfo.get_market_data_ex(
            [],  # 获取所有字段（QMT会自动处理tick支持的字段）
            [stock_code],
            period='tick',
            count=1,
            subscribe=False
        )

        if not current_data or stock_code not in current_data:
            return

        # 解析数据
        data_df = current_data[stock_code]
        if len(data_df) == 0:
            return

        latest_data = data_df.iloc[-1]

        # 尝试多种可能的字段名（与主策略模块保持一致）
        current_price = 0
        for price_field in ['lastPrice', 'last_price', 'price', 'close', 'last']:
            if price_field in latest_data and latest_data[price_field] is not None:
                current_price = float(latest_data[price_field])
                break

        current_volume = 0
        for volume_field in ['volume', 'lastVolume', 'last_volume']:
            if volume_field in latest_data and latest_data[volume_field] is not None:
                current_volume = float(latest_data[volume_field])
                break

        current_time = time.time()

        # 添加到缓冲区
        ContextInfo.price_buffer.append(current_price)
        ContextInfo.volume_buffer.append(current_volume)
        ContextInfo.time_buffer.append(current_time)

        if TEST_CONFIG.get('debug_mode', True):
            print(f"📊 数据收集: 价格={current_price:.3f}, 成交量={current_volume}, 缓冲区={len(ContextInfo.price_buffer)}/{TEST_CONFIG['trigger_data_count']}")

    except Exception as e:
        print(f"❌ 数据收集失败: {e}")

def check_trigger_conditions(ContextInfo):
    """检查触发条件 - 简化版：只需要3次数据就触发"""
    if not TEST_CONFIG.get('enable_trigger_condition', True):
        ContextInfo.trigger_conditions_met = True  # 如果禁用触发条件，总是满足
        return

    # 只检查数据点数量：达到3个就触发
    required_count = TEST_CONFIG['trigger_data_count']
    data_count = len(ContextInfo.price_buffer)
    ContextInfo.trigger_conditions_met = data_count >= required_count

    if TEST_CONFIG.get('debug_mode', True):
        print(f"🎯 触发条件检查（简化版）:")
        print(f"   数据点数量: {data_count}/{required_count} → {'✅' if ContextInfo.trigger_conditions_met else '❌'}")
        print(f"   触发状态: {'✅ 满足，准备下单' if ContextInfo.trigger_conditions_met else '❌ 继续收集数据'}")

        if ContextInfo.trigger_conditions_met and data_count > 0:
            try:
                prices = list(ContextInfo.price_buffer)
                print(f"   价格序列: {[f'{p:.3f}' for p in prices]}")
                if len(prices) >= 2:
                    trend = '上涨' if prices[-1] > prices[0] else '下跌' if prices[-1] < prices[0] else '平稳'
                    print(f"   趋势判断: {trend} (首价:{prices[0]:.3f} → 末价:{prices[-1]:.3f})")
            except Exception as e:
                print(f"   价格分析失败: {e}")

def test_query_orders(ContextInfo):
    """测试委托查询功能"""
    print("\n🔍 测试1: 委托查询功能")
    print("-" * 30)
    
    try:
        # 获取委托信息
        orders = get_trade_detail_data(ContextInfo.acct, ContextInfo.acct_type.lower(), 'order')
        
        if not orders:
            print("📋 当前无委托信息")
            return

        # 过滤委托（根据配置）
        filtered_orders = []
        total_orders = len(orders)

        for order_obj in orders:
            order_status = getattr(order_obj, 'm_nOrderStatus', -1)

            # 状态判断
            is_cancelled = order_status in [53, 54]  # 已撤销、部分撤销
            is_pending = order_status in [48, 49, 50, 51]  # 未处理状态
            is_completed = order_status == 52  # 全部成交

            # 根据配置过滤
            should_include = True

            if TEST_CONFIG.get('filter_cancelled_orders', True) and is_cancelled:
                should_include = False  # 过滤已撤销的委托

            if TEST_CONFIG.get('show_only_pending', False) and not is_pending:
                should_include = False  # 只显示未处理的委托

            if should_include:
                filtered_orders.append(order_obj)

        print(f"📋 总委托数: {total_orders}, 显示: {len(filtered_orders)} 个")
        if TEST_CONFIG.get('filter_cancelled_orders', True):
            print("🔍 已过滤已撤销的委托")
        if TEST_CONFIG.get('show_only_pending', False):
            print("🔍 只显示未处理的委托")

        if not filtered_orders:
            print("📋 没有符合条件的委托")
            return

        for i, order_obj in enumerate(filtered_orders):
            print(f"\n📋 委托 #{i+1}:")
            
            # 基本信息
            order_id = getattr(order_obj, 'm_strOrderSysID', '未知')
            stock_code = getattr(order_obj, 'm_strInstrumentID', '未知')
            stock_name = getattr(order_obj, 'm_strInstrumentName', '未知')
            price = getattr(order_obj, 'm_dPrice', 0) or getattr(order_obj, 'm_dLimitPrice', 0)
            volume = getattr(order_obj, 'm_nVolume', 0) or getattr(order_obj, 'm_nVolumeTotalOriginal', 0)
            traded_volume = getattr(order_obj, 'm_nVolumeTraded', 0)
            order_status = getattr(order_obj, 'm_nOrderStatus', -1)
            order_time = getattr(order_obj, 'm_strInsertTime', '')
            
            # 方向识别
            opt_name = getattr(order_obj, 'm_strOptName', '')
            offset_flag = getattr(order_obj, 'm_nOffsetFlag', -1)
            direction = getattr(order_obj, 'm_nDirection', -1)
            entrust_type = getattr(order_obj, 'm_eEntrustType', -1)
            
            print(f"   委托号: {order_id}")
            print(f"   股票: {stock_code} ({stock_name})")
            print(f"   价格: {price:.3f}")
            print(f"   数量: {volume} (已成交: {traded_volume})")
            print(f"   时间: {order_time}")
            print(f"   状态: {order_status}")
            
            # 方向识别测试
            print(f"   🔍 方向字段:")
            print(f"      OptName: '{opt_name}'")
            print(f"      OffsetFlag: {offset_flag}")
            print(f"      Direction: {direction}")
            print(f"      EntrustType: {entrust_type}")
            
            # 方向判断
            if '买' in opt_name or '买入' in opt_name:
                direction_result = '买入'
            elif '卖' in opt_name or '卖出' in opt_name:
                direction_result = '卖出'
            else:
                direction_result = f'未知(offset={offset_flag})'
            
            print(f"   ✅ 识别结果: {direction_result}")
            
            # 状态判断
            status_mapping = {
                -1: '状态待确认',
                48: '未报',
                49: '待报', 
                50: '已报待成交',
                51: '部分成交',
                52: '全部成交',
                53: '已撤销',
                54: '部分撤销'
            }
            status_desc = status_mapping.get(order_status, f'未知状态({order_status})')
            is_pending = order_status in [48, 49, 50, 51]
            
            print(f"   📊 状态: {status_desc} (未处理: {is_pending})")
            
            # 保存最新的委托ID用于撤单测试
            if is_pending and direction_result == '买入':
                ContextInfo.test_order_id = order_id
                print(f"   🎯 标记为撤单测试目标")
            
    except Exception as e:
        print(f"❌ 委托查询测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_place_order_with_offset(ContextInfo):
    """测试带偏移的下单功能"""
    print("\n📤 测试2: 偏移下单功能")
    print("-" * 30)

    if not TEST_CONFIG['enable_real_trading']:
        print("⚠️ 真实交易未启用，跳过下单测试")
        return

    # 安全检查：限制测试订单数量
    if ContextInfo.test_order_count >= TEST_CONFIG['max_test_orders']:
        print(f"⚠️ 已达到最大测试订单数量 ({TEST_CONFIG['max_test_orders']})，跳过下单")
        return

    try:
        stock_code = TEST_CONFIG['test_stock']
        volume = TEST_CONFIG['test_volume']

        # 获取当前价格（使用与主策略模块相同的方法）
        current_data = ContextInfo.get_market_data_ex(
            [],  # 获取所有字段
            [stock_code],
            period='tick',
            count=1,
            subscribe=False
        )

        if not current_data or stock_code not in current_data:
            print(f"❌ 无法获取 {stock_code} 的当前价格")
            return

        # 解析数据
        data_df = current_data[stock_code]
        if len(data_df) == 0:
            print(f"❌ {stock_code} 的数据为空")
            return

        latest_data = data_df.iloc[-1]

        # 尝试多种可能的字段名
        current_price = 0
        for price_field in ['lastPrice', 'last_price', 'price', 'close', 'last']:
            if price_field in latest_data and latest_data[price_field] is not None:
                current_price = float(latest_data[price_field])
                break

        if current_price <= 0:
            print(f"❌ 无法获取有效的价格数据")
            print(f"📊 可用字段: {list(latest_data.keys())}")
            print(f"📊 字段值: {dict(latest_data)}")
            return

        # 简化逻辑：只下买单
        offset_ratio = TEST_CONFIG['buy_offset_ratio']
        hang_price = current_price * (1 + offset_ratio)
        order_type = "买入"
        qmt_direction = 23  # QMT买入代码

        print(f"📊 简化下单逻辑: 收集到{len(ContextInfo.price_buffer)}次数据，执行买入操作")

        print(f"📊 下单参数:")
        print(f"   股票: {stock_code}")
        print(f"   当前价格: {current_price:.3f}")
        print(f"   操作方向: {order_type}")
        print(f"   偏移比例: {offset_ratio*100:.2f}%")
        print(f"   挂单价格: {hang_price:.3f}")
        print(f"   数据点数: {len(ContextInfo.price_buffer)}")
        print(f"   数量: {volume}股")

        # 执行下单
        print(f"📤 执行{order_type}下单...")
        order_result = passorder(
            qmt_direction,              # 买入/卖出代码
            1101,                       # 委托类型
            ContextInfo.acct,           # 账户ID
            stock_code,                 # 股票代码
            hang_price,                 # 价格
            volume,                     # 数量
            ContextInfo                 # 上下文
        )

        print(f"📋 下单返回值: {order_result} (类型: {type(order_result)})")

        # 根据QMT文档，passorder返回None，需要通过get_last_order_id获取订单ID
        if order_result is None:
            print(f"✅ {order_type}请求已提交到QMT")
            ContextInfo.test_order_count += 1  # 增加测试订单计数

            # 等待订单处理
            time.sleep(1)

            # 获取最新订单ID
            try:
                latest_order_id = get_last_order_id(ContextInfo.acct, 'stock', 'order', f'测试{order_type}')
                if latest_order_id and latest_order_id != '-1':
                    print(f"✅ 获取到订单ID: {latest_order_id}")
                    ContextInfo.test_order_id = latest_order_id
                else:
                    print(f"⚠️ 暂未获取到订单ID，但订单可能已提交")
            except Exception as e:
                print(f"⚠️ 获取订单ID失败: {e}")
        else:
            print(f"❌ {order_type}可能失败，返回值: {order_result}")

        print(f"📊 测试订单统计: {ContextInfo.test_order_count}/{TEST_CONFIG['max_test_orders']}")

    except Exception as e:
        print(f"❌ 偏移下单测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_place_order(ContextInfo):
    """测试下单功能"""
    print("\n📤 测试2: 下单功能")
    print("-" * 30)

    if not TEST_CONFIG['enable_real_trading']:
        print("⚠️ 真实交易未启用，跳过下单测试")
        return

    # 安全检查：限制测试订单数量
    if ContextInfo.test_order_count >= TEST_CONFIG['max_test_orders']:
        print(f"⚠️ 已达到最大测试订单数量 ({TEST_CONFIG['max_test_orders']})，跳过下单")
        return

    try:
        stock_code = TEST_CONFIG['test_stock']
        volume = TEST_CONFIG['test_volume']
        
        # 获取当前价格
        current_data = ContextInfo.get_market_data(['close'], [stock_code])
        if not current_data or stock_code not in current_data:
            print(f"❌ 无法获取 {stock_code} 的当前价格")
            return
        
        current_price = current_data[stock_code]['close']
        offset_ratio = TEST_CONFIG['test_offset_ratio']
        hang_price = current_price * (1 - offset_ratio)  # 低于市价买入
        
        print(f"📊 下单参数:")
        print(f"   股票: {stock_code}")
        print(f"   当前价格: {current_price:.3f}")
        print(f"   挂单价格: {hang_price:.3f} (偏移: {-offset_ratio*100:.1f}%)")
        print(f"   数量: {volume}股")
        
        # 执行下单
        print(f"📤 执行买入下单...")
        order_result = passorder(
            23,                     # 买入代码
            1101,                   # 委托类型
            ContextInfo.acct,       # 账户ID
            stock_code,             # 股票代码
            hang_price,             # 价格
            volume,                 # 数量
            ContextInfo             # 上下文
        )
        
        print(f"📋 下单返回值: {order_result} (类型: {type(order_result)})")
        
        # 根据QMT文档，passorder返回None，需要通过get_last_order_id获取订单ID
        if order_result is None:
            print(f"✅ 下单请求已提交到QMT")
            ContextInfo.test_order_count += 1  # 增加测试订单计数

            # 等待订单处理
            time.sleep(1)

            # 获取最新订单ID
            try:
                latest_order_id = get_last_order_id(ContextInfo.acct, 'stock', 'order', '测试下单')
                if latest_order_id and latest_order_id != '-1':
                    print(f"✅ 获取到订单ID: {latest_order_id}")
                    ContextInfo.test_order_id = latest_order_id
                else:
                    print(f"⚠️ 暂未获取到订单ID，但订单可能已提交")
            except Exception as e:
                print(f"⚠️ 获取订单ID失败: {e}")
        else:
            print(f"❌ 下单可能失败，返回值: {order_result}")

        print(f"📊 测试订单统计: {ContextInfo.test_order_count}/{TEST_CONFIG['max_test_orders']}")
            
    except Exception as e:
        print(f"❌ 下单测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_cancel_order(ContextInfo):
    """测试撤单功能"""
    print("\n🔄 测试3: 撤单功能")
    print("-" * 30)
    
    if not TEST_CONFIG['enable_real_trading']:
        print("⚠️ 真实交易未启用，跳过撤单测试")
        return
    
    if not ContextInfo.test_order_id:
        print("⚠️ 没有可撤销的测试订单")
        return
    
    try:
        order_id = ContextInfo.test_order_id
        print(f"🎯 撤销订单: {order_id}")
        
        # 执行撤单
        cancel_result = cancel(order_id, ContextInfo.acct, 'STOCK', ContextInfo)
        
        print(f"📋 撤单返回值: {cancel_result} (类型: {type(cancel_result)})")
        
        if cancel_result:
            print(f"✅ 撤单成功")
        else:
            print(f"❌ 撤单失败或订单已处理")
        
        # 清除测试订单ID
        ContextInfo.test_order_id = None
        
    except Exception as e:
        print(f"❌ 撤单测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_account_info(ContextInfo):
    """测试账户信息查询"""
    print("\n💰 测试4: 账户信息查询")
    print("-" * 30)
    
    try:
        # 获取账户信息
        account_info = get_trade_detail_data(ContextInfo.acct, ContextInfo.acct_type.lower(), 'account')
        
        if account_info and len(account_info) > 0:
            acc = account_info[0]
            available_cash = getattr(acc, 'm_dAvailable', 0)
            total_asset = getattr(acc, 'm_dTotalAsset', 0)
            market_value = getattr(acc, 'm_dMarketValue', 0)
            
            print(f"💰 账户信息:")
            print(f"   可用资金: {available_cash:.2f}")
            print(f"   总资产: {total_asset:.2f}")
            print(f"   市值: {market_value:.2f}")
        else:
            print("❌ 无法获取账户信息")
            
    except Exception as e:
        print(f"❌ 账户信息查询失败: {e}")

def print_test_summary(ContextInfo):
    """打印测试总结"""
    print(f"\n📊 测试总结:")
    print(f"   当前阶段: {ContextInfo.test_phase}")
    print(f"   测试订单ID: {ContextInfo.test_order_id or '无'}")
    print(f"   订单计数: {ContextInfo.test_order_count}/{TEST_CONFIG['max_test_orders']}")
    print(f"   数据缓冲: {len(ContextInfo.price_buffer)}/{TEST_CONFIG['trigger_data_count']}")
    print(f"   触发条件: {'✅ 满足' if ContextInfo.trigger_conditions_met else '❌ 不满足'}")
    print(f"   下次测试: {TEST_CONFIG['test_interval']}秒后")

    # 显示当前配置
    if TEST_CONFIG.get('debug_mode', True):
        print(f"\n⚙️ 当前配置（简化版）:")
        print(f"   买入偏移: {TEST_CONFIG['buy_offset_ratio']*100:.2f}%")
        print(f"   数据点数量: {TEST_CONFIG['trigger_data_count']}")
        print(f"   触发条件: {'启用' if TEST_CONFIG['enable_trigger_condition'] else '禁用'}")
        print(f"   真实交易: {'启用' if TEST_CONFIG['enable_real_trading'] else '禁用'}")

def test_all_functions(ContextInfo):
    """一次性测试所有功能（手动调用）"""
    print("\n🚀 执行完整功能测试")
    print("=" * 60)

    # 1. 查询测试
    test_query_orders(ContextInfo)

    # 2. 账户信息测试
    test_account_info(ContextInfo)

    if TEST_CONFIG['enable_real_trading']:
        # 3. 下单测试
        test_place_order(ContextInfo)

        # 等待
        print("\n⏳ 等待3秒让订单生效...")
        time.sleep(3)

        # 4. 再次查询确认订单
        print("\n🔍 确认订单状态:")
        test_query_orders(ContextInfo)

        # 5. 撤单测试
        if ContextInfo.test_order_id:
            test_cancel_order(ContextInfo)

            # 等待
            print("\n⏳ 等待3秒让撤单生效...")
            time.sleep(3)

            # 6. 最终查询
            print("\n🔍 最终订单状态:")
            test_query_orders(ContextInfo)

    print("\n✅ 完整功能测试完成")

def debug_order_fields(order_obj):
    """调试订单对象的所有字段"""
    print("\n🔍 订单对象字段调试:")
    print("-" * 40)

    # 获取所有属性
    attrs = [attr for attr in dir(order_obj) if not attr.startswith('_')]

    for attr in sorted(attrs):
        try:
            value = getattr(order_obj, attr)
            if not callable(value):  # 跳过方法
                print(f"   {attr}: {value} ({type(value).__name__})")
        except:
            print(f"   {attr}: <无法访问>")

def get_qmt_api_status():
    """检查QMT API可用性"""
    print("\n🔧 QMT API可用性检查:")
    print("-" * 30)

    api_functions = [
        ('get_trade_detail_data', '委托查询'),
        ('passorder', '下单'),
        ('cancel', '撤单'),
        ('get_last_order_id', '获取订单ID'),
    ]

    for func_name, desc in api_functions:
        try:
            func = globals().get(func_name)
            if func and callable(func):
                print(f"   ✅ {desc} ({func_name}): 可用")
            else:
                print(f"   ❌ {desc} ({func_name}): 不可用")
        except:
            print(f"   ❌ {desc} ({func_name}): 检查失败")

if __name__ == "__main__":
    print("🧪 QMT下单撤单测试模块 - 与主策略模块一致版")
    print("请在QMT环境中运行此脚本")
    print("⚠️ 设置为tick周期运行，使用is_last_bar()安全检查")
    print("\n📋 使用说明:")
    print("1. 修改TEST_CONFIG中的参数")
    print("2. 设置enable_real_trading=True启用真实交易测试")
    print("3. 配置触发条件参数（价格阈值、成交量阈值等）")
    print("4. 在QMT中设置为tick周期并运行策略")
    print("5. 观察控制台输出的测试结果")
    print("\n⚙️ QMT处理模式（与主策略模块一致）:")
    print("✅ 使用is_last_bar()进行安全检查")
    print("✅ 只在最新数据时执行逻辑")
    print("✅ 使用get_market_data_ex()获取tick数据")
    print("✅ 空列表[]参数让QMT自动处理字段")
    print("✅ 测试操作按间隔执行（默认30秒一次）")
    print("\n⚙️ 主要功能（简化版）:")
    print("✅ 简单触发条件（收集到3个数据点就下单）")
    print("✅ 固定买入下单（偏移市价可配置）")
    print("✅ 委托状态过滤（只显示有效委托）")
    print("✅ 安全限制（最大测试订单数量）")
    print("✅ 详细调试信息（可配置开关）")
    print("\n🎯 触发条件（简化版）:")
    print("1. 收集到3个数据点")
    print("2. 无其他复杂条件")
    print("3. 立即触发下单")
    print("\n📊 下单逻辑（简化版）:")
    print("• 收集到3次数据 → 执行买入")
    print("• 固定买入方向，偏移比例可配置")
    print("• 无趋势判断，无成交量检查")
    print("\n📈 数据处理方法（参考主策略模块）:")
    print("• 使用get_market_data_ex()获取tick数据")
    print("• 空列表[]参数让QMT自动处理字段")
    print("• 智能字段提取（支持多种字段名）")
    print("• is_last_bar()安全检查确保数据有效性")
