#coding:gbk
"""
QMT Tick历史数据测试策略
专门测试tick数据获取和处理

重要特性:
1. Tick数据成交量是增量成交量，需要自己处理
2. 只能使用lastPrice最新价，OHLC都是当日价格
3. 测试历史tick数据的获取和处理方法

作者: QMT策略开发
版本: 1.0.0
日期: 2024-12-19
"""

import numpy as np
from datetime import datetime, timedelta
import time

# ============================================================================
# QMT策略标准接口
# ============================================================================

def init(ContextInfo):
    """
    策略初始化函数 - 下载tick历史数据
    """
    print("🚀 QMT Tick历史数据测试策略启动")
    print("="*60)
    
    # 获取股票代码
    ContextInfo.stock = ContextInfo.stockcode + '.' + ContextInfo.market
    print(f"📊 测试股票: {ContextInfo.stock}")
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 下载tick历史数据
    print(f"\n📥 开始下载tick历史数据...")
    
    try:
        # 下载最近几天的tick数据
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=3)).strftime('%Y%m%d')
        
        print(f"📅 数据范围: {start_date} ~ {end_date}")
        
        # 下载tick数据
        download_history_data(ContextInfo.stock, "tick", start_date, end_date)
        print(f"✅ Tick数据下载完成")
        
    except Exception as e:
        print(f"⚠️ Tick历史数据下载异常: {e}")
        print(f"💡 提示: 请检查网络连接和股票代码")
    
    # 等待下载完成
    print(f"\n⏳ 等待tick数据下载完成...")
    time.sleep(15)
    
    # 初始化计数器和数据缓存
    ContextInfo.tick_counter = 0
    ContextInfo.tick_cache = []
    ContextInfo.volume_cache = []
    ContextInfo.last_cumulative_volume = 0
    
    print(f"\n💡 Tick数据下载完成，等待handlebar()函数进行数据测试...")
    print("="*60)

def handlebar(ContextInfo):
    """
    K线数据处理函数 - 获取并打印过去100个tick数据
    """
    ContextInfo.tick_counter = getattr(ContextInfo, 'tick_counter', 0) + 1

    # 每次K线都执行tick数据获取
    print(f"\n🔍 获取过去100个Tick数据 (第{ContextInfo.tick_counter}根K线)")
    print("-" * 80)

    # 获取并打印100个tick数据
    success = get_and_print_100_ticks(ContextInfo)

    if success:
        print(f"🎉 成功获取并打印了过去100个tick数据！")
    else:
        print(f"❌ 获取tick数据失败")

    print("-" * 80)

# ============================================================================
# Tick数据获取函数
# ============================================================================

def get_and_print_100_ticks(ContextInfo):
    """
    获取并打印过去100个tick数据
    """
    print(f"📦 开始获取过去100个Tick数据: {ContextInfo.stock}")

    try:
        # 计算时间范围 - 获取过去1小时的数据
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)

        start_time_str = start_time.strftime('%Y%m%d%H%M%S')
        end_time_str = end_time.strftime('%Y%m%d%H%M%S')

        print(f"  📅 时间范围: {start_time_str} ~ {end_time_str}")

        # 获取历史tick数据
        hist_tick_data = ContextInfo.get_market_data_ex(
            [],
            [ContextInfo.stock],
            period='tick',
            start_time=start_time_str,
            end_time=end_time_str,
            count=100,  # 限制获取100条
            dividend_type='none'
        )

        if hist_tick_data is None:
            print(f"  ❌ 获取tick数据失败: 返回None")
            return False

        if ContextInfo.stock not in hist_tick_data:
            print(f"  ❌ 获取tick数据失败: 股票代码不在返回数据中")
            print(f"  📊 返回的股票代码: {list(hist_tick_data.keys())}")
            return False

        data_df = hist_tick_data[ContextInfo.stock]
        tick_count = len(data_df)

        print(f"  ✅ 成功获取 {tick_count} 笔tick数据")
        print(f"  📊 数据类型: {type(data_df)}")
        print(f"  📊 数据结构: {data_df.columns.tolist() if hasattr(data_df, 'columns') else 'N/A'}")

        if tick_count == 0:
            print(f"  ⚠️ 获取到的tick数据为空")
            return False

        # 打印所有tick数据
        print(f"\n  📋 所有Tick数据详情:")
        print(f"  {'序号':<4} {'时间':<20} {'价格':<10} {'成交量':<12} {'增量':<12}")
        print(f"  {'-'*4} {'-'*20} {'-'*10} {'-'*12} {'-'*12}")

        prev_volume = 0
        total_incremental = 0

        for i, (_, tick) in enumerate(data_df.iterrows()):
            tick_time = tick.get('time', '')
            price = tick.get('lastPrice', 0)
            volume = tick.get('volume', 0)

            # 计算增量成交量
            if i == 0:
                incremental = volume
            else:
                incremental = volume - prev_volume

            total_incremental += incremental if incremental > 0 else 0
            prev_volume = volume

            # 时间格式转换
            if isinstance(tick_time, (int, float)) and tick_time > 1000000000:
                try:
                    if tick_time > 1000000000000:  # 毫秒时间戳
                        time_readable = datetime.fromtimestamp(tick_time/1000).strftime('%H:%M:%S.%f')[:-3]
                    else:  # 秒时间戳
                        time_readable = datetime.fromtimestamp(tick_time).strftime('%H:%M:%S')
                except:
                    time_readable = str(tick_time)
            else:
                time_readable = str(tick_time)

            print(f"  {i+1:<4} {time_readable:<20} {price:<10.3f} {volume:<12.0f} {incremental:<12.0f}")

        # 统计信息
        prices = [tick.get('lastPrice', 0) for _, tick in data_df.iterrows()]
        volumes = [tick.get('volume', 0) for _, tick in data_df.iterrows()]

        valid_prices = [p for p in prices if p > 0]
        if valid_prices:
            min_price = min(valid_prices)
            max_price = max(valid_prices)
            avg_price = sum(valid_prices) / len(valid_prices)

            print(f"\n  📊 数据统计:")
            print(f"    总tick数: {tick_count}")
            print(f"    价格范围: {min_price:.3f} ~ {max_price:.3f}")
            print(f"    平均价格: {avg_price:.3f}")
            print(f"    成交量范围: {min(volumes)} ~ {max(volumes)}")
            print(f"    总增量成交量: {total_incremental}")

        print(f"  ✅ 100个Tick数据打印完成")
        return True

    except Exception as e:
        print(f"  ❌ 获取tick数据异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

# ============================================================================
# 策略信息
# ============================================================================

print("📄 QMT Tick历史数据测试策略已加载")
print("🔧 专门获取并打印过去100个tick数据")
print("📅 版本: 2.0.0 (2024-12-19)")
        print(f"⚡ 测试当前Tick数据: {ContextInfo.stock}")
        
        # 获取最新tick数据
        tick_data = ContextInfo.get_market_data_ex(
            ['time', 'lastPrice', 'volume'],  # 只获取关键字段
            [ContextInfo.stock],
            period='tick',
            count=10,  # 获取最新10笔
            dividend_type='none'
        )
        
        if tick_data is not None and ContextInfo.stock in tick_data:
            data_df = tick_data[ContextInfo.stock]
            tick_count = len(data_df)

            print(f"  ✅ 获取到 {tick_count} 笔当前tick数据")
            print(f"  📊 数据类型: {type(data_df)}")
            print(f"  📊 数据结构: {data_df.columns.tolist() if hasattr(data_df, 'columns') else 'N/A'}")

            if tick_count > 0:
                print(f"\n  📋 所有当前tick数据:")
                print(f"  {'序号':<4} {'时间':<20} {'价格':<10} {'成交量':<12}")
                print(f"  {'-'*4} {'-'*20} {'-'*10} {'-'*12}")

                for i, (_, tick) in enumerate(data_df.iterrows()):
                    tick_time = tick.get('time', '')
                    price = tick.get('lastPrice', 0)
                    volume = tick.get('volume', 0)

                    # 时间格式转换
                    if isinstance(tick_time, (int, float)) and tick_time > 1000000000:
                        try:
                            if tick_time > 1000000000000:  # 毫秒时间戳
                                time_readable = datetime.fromtimestamp(tick_time/1000).strftime('%H:%M:%S.%f')[:-3]
                            else:  # 秒时间戳
                                time_readable = datetime.fromtimestamp(tick_time).strftime('%H:%M:%S')
                        except:
                            time_readable = str(tick_time)
                    else:
                        time_readable = str(tick_time)

                    print(f"  {i+1:<4} {time_readable:<20} {price:<10.3f} {volume:<12.0f}")

                # 显示最新tick数据
                latest_tick = data_df.iloc[-1]
                latest_price = latest_tick.get('lastPrice', 0)
                latest_volume = latest_tick.get('volume', 0)
                latest_time = latest_tick.get('time', '')

                print(f"\n  📊 最新tick: 时间={latest_time}, 价格={latest_price:.3f}, 量={latest_volume}")

                # 检查数据合理性
                if latest_price > 0:
                    print(f"  ✅ 当前tick数据正常")
                    return True
                else:
                    print(f"  ❌ 当前tick价格异常: {latest_price}")
                    return False
            else:
                print(f"  ❌ 当前tick数据为空")
                return False
        else:
            print(f"  ❌ 无法获取当前tick数据")
            return False
            
    except Exception as e:
        print(f"  ❌ 当前tick数据测试异常: {e}")
        return False

def test_historical_tick_data(ContextInfo):
    """
    测试历史tick数据获取
    """
    try:
        print(f"📈 测试历史Tick数据: {ContextInfo.stock}")
        
        # 获取更多历史tick数据
        hist_tick_data = ContextInfo.get_market_data_ex(
            ['time', 'lastPrice', 'volume'],
            [ContextInfo.stock],
            period='tick',
            count=100,  # 获取更多历史数据
            dividend_type='none'
        )
        
        if hist_tick_data is not None and ContextInfo.stock in hist_tick_data:
            data_df = hist_tick_data[ContextInfo.stock]
            tick_count = len(data_df)

            print(f"  ✅ 获取到 {tick_count} 笔历史tick数据")
            print(f"  📊 数据类型: {type(data_df)}")
            print(f"  📊 数据结构: {data_df.columns.tolist() if hasattr(data_df, 'columns') else 'N/A'}")

            if tick_count > 0:
                # 分析数据时间跨度
                first_time = data_df.iloc[0].get('time', '')
                last_time = data_df.iloc[-1].get('time', '')
                print(f"  📅 数据时间跨度: {first_time} ~ {last_time}")

                print(f"\n  📋 所有历史tick数据:")
                print(f"  {'序号':<4} {'时间':<20} {'价格':<10} {'成交量':<12}")
                print(f"  {'-'*4} {'-'*20} {'-'*10} {'-'*12}")

                # 打印所有历史tick数据
                for i, (_, tick) in enumerate(data_df.iterrows()):
                    tick_time = tick.get('time', '')
                    price = tick.get('lastPrice', 0)
                    volume = tick.get('volume', 0)

                    # 时间格式转换
                    if isinstance(tick_time, (int, float)) and tick_time > 1000000000:
                        try:
                            if tick_time > 1000000000000:  # 毫秒时间戳
                                time_readable = datetime.fromtimestamp(tick_time/1000).strftime('%H:%M:%S.%f')[:-3]
                            else:  # 秒时间戳
                                time_readable = datetime.fromtimestamp(tick_time).strftime('%H:%M:%S')
                        except:
                            time_readable = str(tick_time)
                    else:
                        time_readable = str(tick_time)

                    print(f"  {i+1:<4} {time_readable:<20} {price:<10.3f} {volume:<12.0f}")

                # 分析价格范围
                prices = [tick.get('lastPrice', 0) for _, tick in data_df.iterrows()]
                valid_prices = [p for p in prices if p > 0]

                if len(valid_prices) > 0:
                    min_price = min(valid_prices)
                    max_price = max(valid_prices)
                    avg_price = np.mean(valid_prices)

                    print(f"\n  📊 价格统计: 最低={min_price:.3f}, 最高={max_price:.3f}, 均价={avg_price:.3f}")
                    print(f"  ✅ 历史tick数据充足")
                    return True
                else:
                    print(f"  ❌ 历史tick价格数据异常")
                    return False
            else:
                print(f"  ⚠️ 历史tick数据为空")
                return False
        else:
            print(f"  ❌ 无法获取历史tick数据")
            return False
            
    except Exception as e:
        print(f"  ❌ 历史tick数据测试异常: {e}")
        return False

def test_tick_volume_processing(ContextInfo):
    """
    测试tick成交量处理 - 增量成交量转换
    """
    try:
        print(f"📦 测试Tick成交量处理: {ContextInfo.stock}")
        
        # 获取tick数据进行成交量分析
        tick_data = ContextInfo.get_market_data_ex(
            ['time', 'lastPrice', 'volume'],
            [ContextInfo.stock],
            period='tick',
            count=20,
            dividend_type='none'
        )
        
        if tick_data is not None and ContextInfo.stock in tick_data:
            data_df = tick_data[ContextInfo.stock]
            
            if len(data_df) >= 10:
                print(f"  ✅ 获取到 {len(data_df)} 笔tick数据进行成交量分析")

                # 转换为列表格式便于处理
                tick_list = []
                for _, tick in data_df.iterrows():
                    tick_list.append({
                        'time': tick.get('time', ''),
                        'lastPrice': tick.get('lastPrice', 0),
                        'volume': tick.get('volume', 0)
                    })

                print(f"  📊 开始分析增量成交量...")

                # 使用专门的增量成交量计算函数
                incremental_volumes, analysis = calculate_incremental_volume(tick_list)

                # 显示所有tick数据的详细信息
                print(f"\n  📋 所有tick数据及增量成交量(纯差值计算):")
                print(f"  {'序号':<4} {'时间':<20} {'价格':<10} {'累计量':<12} {'增量':<12} {'计算方式':<15}")
                print(f"  {'-'*4} {'-'*20} {'-'*10} {'-'*12} {'-'*12} {'-'*15}")

                for i in range(len(tick_list)):
                    tick = tick_list[i]
                    tick_time = tick['time']
                    price = tick['lastPrice']
                    current_volume = tick['volume']
                    incremental_volume = incremental_volumes[i]

                    # 时间格式转换（如果是时间戳）
                    if isinstance(tick_time, (int, float)) and tick_time > 1000000000:
                        try:
                            # 转换时间戳为可读格式
                            if tick_time > 1000000000000:  # 毫秒时间戳
                                tick_time_readable = datetime.fromtimestamp(tick_time/1000).strftime('%H:%M:%S.%f')[:-3]
                            else:  # 秒时间戳
                                tick_time_readable = datetime.fromtimestamp(tick_time).strftime('%H:%M:%S')
                        except:
                            tick_time_readable = str(tick_time)
                    else:
                        tick_time_readable = str(tick_time)

                    # 显示计算方式
                    if i == 0:
                        calculation = f"={current_volume}"
                    else:
                        prev_volume = tick_list[i-1]['volume']
                        calculation = f"={current_volume}-{prev_volume}"

                    print(f"  {i+1:<4} {tick_time_readable:<20} {price:<10.3f} {current_volume:<12.0f} {incremental_volume:<12.0f} {calculation:<15}")

                print(f"\n  📊 数据总览: 共{len(tick_list)}笔tick数据")
                
                # 使用分析结果进行统计
                print(f"  📊 增量成交量统计(纯差值计算):")
                print(f"    总tick数: {analysis['total_ticks']}")
                print(f"    正增量笔数: {analysis['positive_increments']}")
                print(f"    零增量笔数: {analysis['zero_increments']}")
                print(f"    负增量笔数: {analysis['negative_increments']}")
                print(f"    总正增量: {analysis['total_positive_volume']}")

                # 计算统计指标
                positive_increments = [v for v in incremental_volumes if v > 0]
                negative_increments = [v for v in incremental_volumes if v < 0]

                if positive_increments:
                    avg_positive = np.mean(positive_increments)
                    max_positive = max(positive_increments)
                    min_positive = min(positive_increments)
                    print(f"    正增量统计: 平均={avg_positive:.1f}, 最大={max_positive}, 最小={min_positive}")

                if negative_increments:
                    avg_negative = np.mean(negative_increments)
                    max_negative = max(negative_increments)  # 最大的负数（最接近0）
                    min_negative = min(negative_increments)  # 最小的负数（绝对值最大）
                    print(f"    负增量统计: 平均={avg_negative:.1f}, 最大={max_negative}, 最小={min_negative}")

                # 计算成交活跃度
                activity_rate = analysis['positive_increments'] / analysis['total_ticks'] * 100
                print(f"    成交活跃度: {activity_rate:.1f}%")

                # 显示总的增量和（应该等于最后的累计量减去第一笔的累计量）
                total_increment = sum(incremental_volumes[1:])  # 排除第一笔
                first_volume = tick_list[0]['volume']
                last_volume = tick_list[-1]['volume']
                expected_total = last_volume - first_volume
                print(f"    增量总和: {total_increment} (期望: {expected_total})")

                print(f"  ✅ 增量成交量计算完成（纯差值方法）")
                return True
            else:
                print(f"  ⚠️ tick数据不足进行成交量分析")
                return False
        else:
            print(f"  ❌ 无法获取tick数据进行成交量分析")
            return False
            
    except Exception as e:
        print(f"  ❌ tick成交量处理测试异常: {e}")
        return False

def test_tick_price_analysis(ContextInfo):
    """
    测试tick价格分析 - 只使用lastPrice
    """
    try:
        print(f"💰 测试Tick价格分析: {ContextInfo.stock}")
        
        # 获取tick数据进行价格分析
        tick_data = ContextInfo.get_market_data_ex(
            ['time', 'lastPrice'],  # 只获取时间和最新价
            [ContextInfo.stock],
            period='tick',
            count=30,
            dividend_type='none'
        )
        
        if tick_data is not None and ContextInfo.stock in tick_data:
            data_df = tick_data[ContextInfo.stock]
            
            if len(data_df) > 0:
                print(f"  ✅ 获取到 {len(data_df)} 笔tick数据进行价格分析")

                # 提取所有lastPrice
                prices = []
                price_changes = []

                print(f"\n  📋 所有tick价格数据:")
                print(f"  {'序号':<4} {'时间':<20} {'价格':<10} {'变化':<10} {'方向':<6}")
                print(f"  {'-'*4} {'-'*20} {'-'*10} {'-'*10} {'-'*6}")

                for i, (_, tick) in enumerate(data_df.iterrows()):
                    price = tick.get('lastPrice', 0)
                    tick_time = tick.get('time', '')

                    # 时间格式转换
                    if isinstance(tick_time, (int, float)) and tick_time > 1000000000:
                        try:
                            if tick_time > 1000000000000:  # 毫秒时间戳
                                tick_time_readable = datetime.fromtimestamp(tick_time/1000).strftime('%H:%M:%S.%f')[:-3]
                            else:  # 秒时间戳
                                tick_time_readable = datetime.fromtimestamp(tick_time).strftime('%H:%M:%S')
                        except:
                            tick_time_readable = str(tick_time)
                    else:
                        tick_time_readable = str(tick_time)

                    if price > 0:
                        prices.append(price)

                        if i > 0 and len(prices) > 1:
                            price_change = price - prices[-2]
                            price_changes.append(price_change)

                            # 判断方向
                            if price_change > 0:
                                direction = "↗上涨"
                            elif price_change < 0:
                                direction = "↘下跌"
                            else:
                                direction = "→平盘"

                            print(f"  {i+1:<4} {tick_time_readable:<20} {price:<10.3f} {price_change:+<10.3f} {direction:<6}")
                        else:
                            print(f"  {i+1:<4} {tick_time_readable:<20} {price:<10.3f} {'--':<10} {'基准':<6}")
                    else:
                        print(f"  {i+1:<4} {tick_time_readable:<20} {'无效':<10} {'--':<10} {'异常':<6}")

                print(f"\n  📊 价格数据总览: 共{len(data_df)}笔，有效{len(prices)}笔")
                
                if len(prices) > 0:
                    # 价格统计分析
                    min_price = min(prices)
                    max_price = max(prices)
                    avg_price = np.mean(prices)
                    price_volatility = max_price - min_price

                    print(f"\n  📊 价格统计分析:")
                    print(f"    最低价格: {min_price:.3f}")
                    print(f"    最高价格: {max_price:.3f}")
                    print(f"    平均价格: {avg_price:.3f}")
                    print(f"    价格波动: {price_volatility:.3f} ({price_volatility/avg_price*100:.2f}%)")

                    # 价格变化分析
                    if len(price_changes) > 0:
                        up_ticks = len([c for c in price_changes if c > 0])
                        down_ticks = len([c for c in price_changes if c < 0])
                        flat_ticks = len([c for c in price_changes if c == 0])

                        total_changes = len(price_changes)
                        up_rate = up_ticks / total_changes * 100
                        down_rate = down_ticks / total_changes * 100
                        flat_rate = flat_ticks / total_changes * 100

                        print(f"    价格方向统计:")
                        print(f"      上涨: {up_ticks}笔 ({up_rate:.1f}%)")
                        print(f"      下跌: {down_ticks}笔 ({down_rate:.1f}%)")
                        print(f"      平盘: {flat_ticks}笔 ({flat_rate:.1f}%)")

                        # 计算平均涨跌幅
                        if up_ticks > 0:
                            avg_up = np.mean([c for c in price_changes if c > 0])
                            print(f"      平均上涨: {avg_up:.3f}")
                        if down_ticks > 0:
                            avg_down = np.mean([c for c in price_changes if c < 0])
                            print(f"      平均下跌: {avg_down:.3f}")

                    print(f"  ✅ 价格分析完成")
                    return True
                else:
                    print(f"  ❌ 无有效价格数据")
                    return False
            else:
                print(f"  ⚠️ tick数据不足进行价格分析")
                return False
        else:
            print(f"  ❌ 无法获取tick数据进行价格分析")
            return False
            
    except Exception as e:
        print(f"  ❌ tick价格分析测试异常: {e}")
        return False

def calculate_incremental_volume(tick_data_list):
    """
    纯差值计算增量成交量

    参数:
        tick_data_list: tick数据列表

    返回:
        incremental_volumes: 增量成交量列表
        analysis: 分析结果字典
    """
    incremental_volumes = []
    analysis = {
        'total_ticks': len(tick_data_list),
        'positive_increments': 0,
        'zero_increments': 0,
        'negative_increments': 0,
        'total_positive_volume': 0
    }

    for i, tick in enumerate(tick_data_list):
        current_volume = tick.get('volume', 0)

        if i == 0:
            # 第一笔数据，增量就是当前成交量
            incremental = current_volume
        else:
            # 纯差值计算：当前累计量 - 前一笔累计量
            prev_volume = tick_data_list[i-1].get('volume', 0)
            incremental = current_volume - prev_volume

        incremental_volumes.append(incremental)

        # 统计分析
        if incremental > 0:
            analysis['positive_increments'] += 1
            analysis['total_positive_volume'] += incremental
        elif incremental == 0:
            analysis['zero_increments'] += 1
        else:
            analysis['negative_increments'] += 1

    return incremental_volumes, analysis

# ============================================================================
# 策略信息
# ============================================================================

print("📄 QMT Tick历史数据测试策略已加载")
print("🔧 专门测试tick数据获取和增量成交量处理")
print("📅 版本: 1.0.0 (2024-12-19)")
