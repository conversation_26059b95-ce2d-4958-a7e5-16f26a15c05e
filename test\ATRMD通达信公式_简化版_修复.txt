{通达信ATRMD指标 - 简化版（语法完全修复）}
{作者：量化交易策略}
{版本：1.6 - 语法修复版}
{说明：保留核心ATRMD功能，确保通达信语法完全兼容}

{=== 参数设置 ===}
N:14;        {ATR计算周期}
M:20;        {相对强度计算周期}
S:5;         {信号线平滑周期}

{=== 核心计算 ===}
{真实波动范围}
TR1:=MAX(MAX(H-L,ABS(REF(C,1)-H)),ABS(REF(C,1)-L));

{平均真实波动范围}
ATR1:=MA(TR1,N);

{ATR相对于价格的比率}
ATRATIO:=ATR1/C;

{ATRMD相对强度（原始值，百分比形式）}
ATRMD_RAW:=ATRATIO/SUM(ATRATIO,M)*100;

{=== 主要指标线 ===}
{ATRMD主线}
ATRMD:ATRMD_RAW,COLORWHITE,LINETHICK2;

{ATRMD信号线（平滑）}
SIGNAL:MA(ATRMD_RAW,S),COLORYELLOW,LINETHICK1;

{ATRMD均线}
AVGLINE:MA(ATRMD_RAW,M),COLORBLUE,LINETHICK1;

{=== 参考线 ===}
{上轨线（均值+标准差）}
UPPER:MA(ATRMD_RAW,M)+STD(ATRMD_RAW,M),COLORRED,LINETHICK1;

{下轨线（均值-标准差）}
LOWER:MA(ATRMD_RAW,M)-STD(ATRMD_RAW,M),COLORGREEN,LINETHICK1;

{=== 交易信号 ===}
{金叉信号：ATRMD上穿信号线}
GOLDCROSS:=CROSS(ATRMD_RAW,SIGNAL);

{死叉信号：ATRMD下穿信号线}
DEADCROSS:=CROSS(SIGNAL,ATRMD_RAW);

{突破上轨}
BREAKUP:=CROSS(ATRMD_RAW,UPPER);

{跌破下轨}
BREAKDOWN:=CROSS(LOWER,ATRMD_RAW);

{=== 信号标记 ===}
{买入信号标记}
STICKLINE(GOLDCROSS AND ATRMD_RAW<AVGLINE,0,ATRMD_RAW,2,0),COLORGREEN;

{卖出信号标记}
STICKLINE(DEADCROSS AND ATRMD_RAW>AVGLINE,0,ATRMD_RAW,2,0),COLORRED;

{高波动警示}
STICKLINE(BREAKUP,UPPER,ATRMD_RAW,1,0),COLORRED;

{低波动提示}
STICKLINE(BREAKDOWN,LOWER,ATRMD_RAW,1,0),COLORGREEN;

{=== 信号点显示 ===}
{买入信号点}
BUYSIGNAL:IF(GOLDCROSS AND ATRMD_RAW<AVGLINE,ATRMD_RAW,DRAWNULL),COLORGREEN,POINTDOT,LINETHICK3;

{卖出信号点}
SELLSIGNAL:IF(DEADCROSS AND ATRMD_RAW>AVGLINE,ATRMD_RAW,DRAWNULL),COLORRED,POINTDOT,LINETHICK3;

{高波动警示点}
HIGHVOL:IF(BREAKUP,ATRMD_RAW,DRAWNULL),COLORRED,CIRCLEDOT,LINETHICK2;

{低波动提示点}
LOWVOL:IF(BREAKDOWN,ATRMD_RAW,DRAWNULL),COLORGREEN,CIRCLEDOT,LINETHICK2;

{=== 趋势箭头 ===}
{上升趋势箭头}
DRAWICON(GOLDCROSS AND ATRMD_RAW<AVGLINE,ATRMD_RAW*0.8,1);

{下降趋势箭头}
DRAWICON(DEADCROSS AND ATRMD_RAW>AVGLINE,ATRMD_RAW*1.2,2);

{高波动警告}
DRAWICON(BREAKUP,ATRMD_RAW*1.1,7);

{低波动提示}
DRAWICON(BREAKDOWN,ATRMD_RAW*0.9,8);

{=== 数值显示 ===}
{当前数值显示（简化版）}
DRAWTEXT_FIX(1,0.95,0,0,'ATRMD'),COLORWHITE;
DRAWTEXT_FIX(1,0.90,0,0,ATRMD_RAW),COLORWHITE;

{=== 状态判断 ===}
{当前状态}
STATUS:=IF(ATRMD_RAW>UPPER,3,IF(ATRMD_RAW<LOWER,1,2));

{趋势状态}
TREND:=IF(ATRMD_RAW>SIGNAL,1,-1);

{=== 辅助输出（用于选股等）===}
{输出当前ATRMD值}
ATRMDVALUE:ATRMD_RAW,NODRAW;

{输出信号状态}
SIGNALSTATUS:IF(GOLDCROSS,1,IF(DEADCROSS,-1,0)),NODRAW;

{输出波动状态}
VOLSTATUS:IF(BREAKUP,2,IF(BREAKDOWN,-2,STATUS)),NODRAW;

{=== 使用说明 ===}
{
【指标说明】
1. ATRMD：显示原始的平均真实波动率相对强度数值
2. SIGNAL：ATRMD的平滑信号线
3. AVGLINE：ATRMD的移动平均线
4. UPPER/LOWER：动态上下轨

【信号含义】
1. 绿点（BUYSIGNAL）：买入信号
2. 红点（SELLSIGNAL）：卖出信号  
3. 红圈（HIGHVOL）：高波动警示
4. 绿圈（LOWVOL）：低波动提示

【交易策略】
1. 买入时机：
   - ATRMD金叉信号线且位于均线下方
   - ATRMD从低波动区域回升

2. 卖出时机：
   - ATRMD死叉信号线且位于均线上方
   - ATRMD进入高波动区域

3. 风险提示：
   - 突破上轨：波动率异常高，注意风险
   - 跌破下轨：波动率异常低，可能酝酿大行情

【参数说明】
N：ATR计算周期（默认14）
M：相对强度计算周期（默认20）
S：信号线平滑周期（默认5）

【参数建议】
- 短线：N=5, M=10, S=3
- 中线：N=14, M=20, S=5（默认）
- 长线：N=30, M=50, S=10

【数值含义】
ATRMD数值越大 = 当前波动率相对越强
ATRMD数值越小 = 当前波动率相对越弱
原始数值便于设定具体交易阈值

【注意事项】
1. 结合价格走势确认信号
2. 高波动时期谨慎操作
3. 低波动时期关注突破机会
4. 建议与其他指标配合使用
}

{=== 版本信息 ===}
{ATRMD简化版 v1.6 - 语法修复版}
{特点：语法完全兼容，原始数值显示，核心功能保留}
{修复：TR变量名冲突，中文变量名，字符串连接问题}
