#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试KeyError: resistances修复
"""

def test_print_strategy_status():
    """测试print_strategy_status函数是否修复了KeyError"""
    try:
        print("=== 测试KeyError: resistances修复 ===")
        
        import importlib.util
        
        # 加载策略文件
        spec = importlib.util.spec_from_file_location("strategy", "框架/6sk线.py")
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        
        # 模拟上下文
        class MockContext:
            def __init__(self):
                self.stockcode = "000001"
                self.market = "SZ"
                self.acct = "test_account"
                self.period = "1m"
                self.position = 0
                self.merged_klines = [
                    {'open': 10.0, 'high': 10.5, 'low': 9.8, 'close': 10.2, 'volume': 1000},
                    {'open': 10.2, 'high': 10.8, 'low': 10.0, 'close': 10.5, 'volume': 1200},
                    {'open': 10.5, 'high': 10.9, 'low': 10.3, 'close': 10.7, 'volume': 800}
                ]
                self.ATRLength = 14
                self.VolLen = 20
                self.use_dynamic_mode = False
        
        # 模拟indicators字典（CMF+BIAS策略版本）
        indicators = {
            'opens': [10.0, 10.2, 10.5],
            'highs': [10.5, 10.8, 10.9],
            'lows': [9.8, 10.0, 10.3],
            'closes': [10.2, 10.5, 10.7],
            'volumes': [1000, 1200, 800],
            'data_quality': '标准模式',
            'data_count': 3,
            'current_price': 10.7,
            'divergence': {
                'status': 'success',
                'buy_signal': False,
                'sell_signal': False,
                'conditions': {
                    'SKDJ超卖确认': False,
                    '双重背离': False,
                    '强趋势确认': False,
                    '突破确认': False
                }
            }
        }
        
        # 测试print_strategy_status函数
        print("📊 测试print_strategy_status函数...")
        C = MockContext()
        available_cash = 50000.0
        current_position = 0
        bar_time = "2024-01-15 14:30:00"
        
        # 这里应该不会抛出KeyError: resistances
        strategy_module.print_strategy_status(C, indicators, available_cash, current_position, bar_time)
        
        print("✅ print_strategy_status函数测试通过 - 没有KeyError")
        return True
        
    except KeyError as e:
        print(f"❌ 仍然存在KeyError: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_buy_signal_check():
    """测试买入信号检查函数"""
    try:
        print("\n=== 测试买入信号检查函数 ===")
        
        import importlib.util
        
        # 加载策略文件
        spec = importlib.util.spec_from_file_location("strategy", "框架/6sk线.py")
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        
        # 模拟indicators字典
        indicators = {
            'current_price': 10.7,
            'divergence': {
                'status': 'success',
                'buy_signal': True,
                'conditions': {
                    'SKDJ超卖确认': True,
                    '双重背离': True,
                    '强趋势确认': True,
                    '突破确认': True
                }
            }
        }
        
        # 测试check_buy_signal函数
        print("📊 测试check_buy_signal函数...")
        result = strategy_module.check_buy_signal(indicators)
        print(f"   买入信号检查结果: {result}")
        
        print("✅ check_buy_signal函数测试通过")
        return True
        
    except Exception as e:
        print(f"❌ check_buy_signal测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 开始KeyError修复测试...")
    
    success_count = 0
    total_tests = 2
    
    if test_print_strategy_status():
        success_count += 1
    
    if test_buy_signal_check():
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！KeyError: resistances问题已修复")
    else:
        print("❌ 部分测试失败，需要进一步检查")
