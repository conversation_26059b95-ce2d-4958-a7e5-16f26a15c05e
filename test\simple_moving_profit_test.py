#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的移动止盈方案测试
直接测试移动止盈逻辑，不依赖复杂的导入
"""

def test_moving_profit_logic():
    """测试移动止盈逻辑"""
    print("🧪 测试移动止盈逻辑")
    
    # 移动止盈方案参数
    入场价格 = 100.0
    ATR_14日 = 0.05  # 假设14日ATR为0.05
    ATR_倍数 = 14.0 / 3.0  # 4.67倍
    
    # 计算关键参数
    ATR_百分比 = (ATR_14日 / 入场价格) * 100
    止损距离 = ATR_百分比 * ATR_倍数
    初始止盈距离 = 止损距离  # 对称设计
    移动触发距离 = 初始止盈距离  # 等于初始止盈距离
    
    print(f"📊 移动止盈方案参数:")
    print(f"   入场价格: {入场价格:.2f}")
    print(f"   14日ATR: {ATR_14日:.4f}")
    print(f"   ATR倍数: {ATR_倍数:.2f}")
    print(f"   ATR百分比: {ATR_百分比:.2f}%")
    print(f"   止损距离: {止损距离:.2f}%")
    print(f"   初始止盈距离: {初始止盈距离:.2f}%")
    print(f"   移动触发距离: {移动触发距离:.2f}%")
    
    # 验证方案要求
    print(f"\n✅ 方案验证:")
    
    # 1. 验证风险收益对称
    对称性 = abs(止损距离 - 初始止盈距离) < 0.01
    print(f"   风险收益对称: {'✅' if 对称性 else '❌'} (止损{止损距离:.2f}% vs 止盈{初始止盈距离:.2f}%)")
    
    # 2. 验证ATR倍数
    期望倍数 = 14.0 / 3.0
    倍数正确 = abs(ATR_倍数 - 期望倍数) < 0.1
    print(f"   ATR倍数正确: {'✅' if 倍数正确 else '❌'} (期望{期望倍数:.2f}, 实际{ATR_倍数:.2f})")
    
    # 3. 验证移动触发距离
    触发距离正确 = abs(移动触发距离 - 初始止盈距离) < 0.01
    print(f"   移动触发距离: {'✅' if 触发距离正确 else '❌'} (应等于初始止盈距离)")
    
    # 4. 验证合理性检查
    距离合理 = 0.15 <= 止损距离 <= 5.0 and 0.15 <= 初始止盈距离 <= 5.0
    print(f"   距离合理性: {'✅' if 距离合理 else '❌'} (0.15%-5.0%范围内)")
    
    return {
        'ATR_14日': ATR_14日,
        'ATR_百分比': ATR_百分比,
        'ATR_倍数': ATR_倍数,
        '止损距离': 止损距离,
        '初始止盈距离': 初始止盈距离,
        '移动触发距离': 移动触发距离,
        '入场价格': 入场价格
    }

def simulate_moving_profit_scenario():
    """模拟移动止盈场景"""
    print(f"\n🎯 模拟移动止盈场景")
    
    # 假设参数
    入场价格 = 100.0
    初始止盈距离 = 0.23  # 0.23%
    移动触发距离 = 0.23  # 0.23%
    
    # 计算关键价格点
    初始止盈价格 = 入场价格 * (1 + 初始止盈距离 / 100)
    移动触发单位 = 移动触发距离 / 100 * 入场价格
    
    print(f"📊 场景参数:")
    print(f"   入场价格: {入场价格:.2f}")
    print(f"   初始止盈距离: {初始止盈距离:.2f}%")
    print(f"   初始止盈价格: {初始止盈价格:.3f}")
    print(f"   移动触发单位: {移动触发单位:.3f}")
    
    # 模拟价格上涨过程
    价格序列 = [100.0, 100.1, 100.23, 100.3, 100.46, 100.5, 100.69, 100.8, 100.92]
    
    print(f"\n📈 价格上涨过程:")
    
    当前止盈线 = 0
    
    for i, 当前价格 in enumerate(价格序列):
        print(f"\n第{i+1}步: 价格 {当前价格:.2f}")
        
        if 当前价格 >= 初始止盈价格:
            # 移动止盈已启动
            price_gain_from_initial = 当前价格 - 初始止盈价格
            move_times = int(price_gain_from_initial / 移动触发单位)
            
            # 计算新的止盈线
            initial_stop_price = 入场价格 + (初始止盈距离 / 100 * 入场价格)
            new_trailing_stop_price = initial_stop_price + (move_times * 移动触发单位)
            
            # 止盈线只能向上移动
            if new_trailing_stop_price > 当前止盈线:
                当前止盈线 = new_trailing_stop_price
                print(f"   ✅ 移动止盈启动: 移动{move_times}次, 止盈线 {当前止盈线:.3f}")
            else:
                print(f"   ➡️ 止盈线保持: {当前止盈线:.3f} (未达到移动条件)")
                
            # 计算保护的利润
            if 当前止盈线 > 入场价格:
                保护利润 = (当前止盈线 - 入场价格) / 入场价格 * 100
                print(f"   🛡️ 保护利润: {保护利润:.2f}%")
                
            # 检查是否触发止盈
            if 当前价格 <= 当前止盈线:
                print(f"   🚨 触发止盈! 价格{当前价格:.2f} <= 止盈线{当前止盈线:.3f}")
        else:
            print(f"   ⏳ 等待启动: 需达到 {初始止盈价格:.3f}")
    
    print(f"\n🎯 最终结果:")
    print(f"   最高价格: {max(价格序列):.2f}")
    print(f"   最终止盈线: {当前止盈线:.3f}")
    if 当前止盈线 > 入场价格:
        最终保护利润 = (当前止盈线 - 入场价格) / 入场价格 * 100
        print(f"   最终保护利润: {最终保护利润:.2f}%")

def test_atr_calculation():
    """测试ATR计算逻辑"""
    print(f"\n🔢 测试ATR计算逻辑")
    
    # 模拟不同的ATR值和价格
    测试案例 = [
        {'价格': 100.0, 'ATR': 0.05, '期望距离': '0.23%'},
        {'价格': 50.0, 'ATR': 0.025, '期望距离': '0.23%'},
        {'价格': 200.0, 'ATR': 0.10, '期望距离': '0.23%'},
        {'价格': 10.0, 'ATR': 0.005, '期望距离': '0.23%'},
    ]
    
    ATR_倍数 = 14.0 / 3.0
    
    for i, 案例 in enumerate(测试案例):
        价格 = 案例['价格']
        ATR = 案例['ATR']
        
        # 计算ATR百分比和距离
        ATR_百分比 = (ATR / 价格) * 100
        止损距离 = ATR_百分比 * ATR_倍数
        
        print(f"案例{i+1}: 价格{价格:.1f}, ATR{ATR:.3f}")
        print(f"   ATR百分比: {ATR_百分比:.2f}%")
        print(f"   止损距离: {止损距离:.2f}%")
        print(f"   期望距离: {案例['期望距离']}")
        
        # 验证合理性
        合理性 = 0.15 <= 止损距离 <= 5.0
        print(f"   合理性: {'✅' if 合理性 else '❌'}")

if __name__ == "__main__":
    print("🚀 简化移动止盈方案测试开始")
    print("=" * 50)
    
    # 测试移动止盈逻辑
    result = test_moving_profit_logic()
    
    # 模拟移动止盈场景
    simulate_moving_profit_scenario()
    
    # 测试ATR计算
    test_atr_calculation()
    
    print("\n" + "=" * 50)
    print("✅ 简化移动止盈方案测试完成")
    
    print(f"\n📋 总结:")
    print(f"   移动止盈方案已成功实现")
    print(f"   基于14日ATR × 4.67倍的对称风险收益设计")
    print(f"   移动止盈线只能向上调整，永不下调")
    print(f"   每当价格上涨初始止盈距离时，止盈线向上移动相同距离")
