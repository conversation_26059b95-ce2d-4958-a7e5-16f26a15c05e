# QMT配置参数错误修复

## 🚨 **错误信息**

```
handlebar执行异常:trigger_price_threshold
```

## 🔍 **问题原因**

在简化测试模块时，我们移除了复杂的触发条件配置参数，但代码中的某些地方仍在引用这些已删除的参数。

## ✅ **已修复的问题**

### **1. print_test_summary函数中的引用**：

**修复前（❌ 错误）**：
```python
print(f"   价格阈值: {TEST_CONFIG['trigger_price_threshold']*100:.2f}%")
print(f"   成交量阈值: {TEST_CONFIG['trigger_volume_threshold']}")
print(f"   卖出偏移: {TEST_CONFIG['sell_offset_ratio']*100:.2f}%")
```

**修复后（✅ 正确）**：
```python
print(f"\n⚙️ 当前配置（简化版）:")
print(f"   买入偏移: {TEST_CONFIG['buy_offset_ratio']*100:.2f}%")
print(f"   数据点数量: {TEST_CONFIG['trigger_data_count']}")
print(f"   触发条件: {'启用' if TEST_CONFIG['enable_trigger_condition'] else '禁用'}")
print(f"   真实交易: {'启用' if TEST_CONFIG['enable_real_trading'] else '禁用'}")
```

### **2. 配置参数清理**：

**修复前（❌ 冗余）**：
```python
TEST_CONFIG = {
    'buy_offset_ratio': -0.01,
    'sell_offset_ratio': 0.01,          # ❌ 已不需要
    'min_price_change': 0.001,          # ❌ 已不需要
    'trigger_price_threshold': 0.005,   # ❌ 已删除但仍被引用
    'trigger_volume_threshold': 1000,   # ❌ 已删除但仍被引用
}
```

**修复后（✅ 简化）**：
```python
TEST_CONFIG = {
    'trigger_data_count': 3,            # 只需要3次数据
    'buy_offset_ratio': -0.01,          # 买入偏移比例
    'enable_trigger_condition': True,   # 是否启用触发条件
    'enable_real_trading': False,       # 是否启用真实交易
}
```

## 🔧 **完整的简化配置**

```python
TEST_CONFIG = {
    # 基本配置
    'test_stock': '000001.SZ',          # 测试股票代码
    'test_quantity': 100,               # 测试数量（股）
    
    # 触发条件配置（简化版）
    'enable_trigger_condition': True,   # 是否启用触发条件
    'trigger_data_count': 3,            # 触发所需的数据点数量（只需要3次数据）
    
    # 交易控制配置
    'enable_real_trading': False,       # 是否启用真实交易（测试时建议False）
    'max_test_orders': 3,               # 最大测试订单数量（防止过度下单）
    'safety_check': True,               # 安全检查（防止误操作）
    
    # 调试配置
    'debug_mode': True,                 # 调试模式（显示详细信息）
    'debug_order_fields': False,        # 是否显示订单对象的所有字段
    'test_interval': 30,                # 测试执行间隔（秒）
    
    # 高级配置
    'auto_cycle': True,                 # 是否自动循环测试
    'test_account_info': True,          # 是否测试账户信息查询
    
    # 查询过滤配置
    'filter_cancelled_orders': True,    # 是否过滤已撤销的委托
    'show_only_pending': False,         # 是否只显示未处理的委托
    
    # 下单参数配置（简化版）
    'buy_offset_ratio': -0.01,          # 买入偏移比例（负值=低于市价买入）
}
```

## 📊 **简化后的运行效果**

现在您应该看到：

```
📊 数据接收 14:23:21 | 缓冲:3/3 | 阶段:query

🎯 触发条件检查（简化版）:
   数据点数量: 3/3 → ✅
   触发状态: ✅ 满足，准备下单
   价格序列: ['137.220', '137.225', '137.230']
   趋势判断: 上涨 (首价:137.220 → 末价:137.230)

📊 简化下单逻辑: 收集到3次数据，执行买入操作
📊 下单参数:
   股票: 000001.SZ
   当前价格: 137.230
   操作方向: 买入
   偏移比例: -1.00%
   挂单价格: 135.858
   数据点数: 3

⚙️ 当前配置（简化版）:
   买入偏移: -1.00%
   数据点数量: 3
   触发条件: 启用
   真实交易: 禁用
```

## 🎯 **核心修复要点**

### **1. 移除所有已删除参数的引用**：
- ❌ `trigger_price_threshold`
- ❌ `trigger_volume_threshold`  
- ❌ `sell_offset_ratio`
- ❌ `min_price_change`

### **2. 保留简化后的核心参数**：
- ✅ `trigger_data_count` - 数据点数量
- ✅ `buy_offset_ratio` - 买入偏移
- ✅ `enable_trigger_condition` - 触发条件开关
- ✅ `enable_real_trading` - 真实交易开关

### **3. 更新所有引用这些参数的代码**：
- ✅ `print_test_summary()` 函数
- ✅ `check_trigger_conditions()` 函数
- ✅ `test_place_order_with_offset()` 函数

## 💡 **验证修复**

运行测试后，您应该：

1. **不再看到** `trigger_price_threshold` 错误
2. **看到简化的** 触发条件检查输出
3. **看到简化的** 配置信息显示
4. **正常执行** 3次数据触发下单逻辑

**现在测试模块应该能正常运行，只需要3次数据就触发买入下单！** 🚀
