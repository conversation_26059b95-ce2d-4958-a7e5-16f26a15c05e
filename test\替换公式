{通达信公式 - ATRMD指标}
{简称: ATRMD}
{名称: 平均真实波动率相对强度指标}
{类别: 技术指标}
{说明: 基于ATR的相对强度指标，用于衡量市场波动性}

{参数设置}
{N:=11; M:=27; P:=35}



{计算真实波动范围}
TR1:=MAX(MAX(H-L,ABS(REF(C,1)-H)),ABS(REF(C,1)-L));
{计算平均真实波动范围}
ATR1:=MA(TR1,N);
{计算ATRMD相对强度}
ATRMD:=(ATR1/C)/SUM(ATR1/C,M);
{转换为百分比}
ATRPCT:=ATRMD*100;

{计算P周期内的最高值和最低值}
MAXATR:=HHV(ATRPCT,P);
MINATR:=LLV(ATRPCT,P);

{0-100标准化}
ATRN:(ATRPCT-MINATR)/(MAXATR-MINATR)*100,COLORWHITE,LINETHICK1;

{标准化参考线}
超买线:80,COLORRED,LINETHICK1;
中线:50,COLORYELLOW,LINETHICK1;
超卖线:20,COLORGREEN,LINETHICK1;

==========================================================================================================

{CMF标准化指标 - CHAIKIN MONEY FLOW 0-100标准化版本}
{N:27;
M:18;
P:35;}

{计算收盘位置值 (CLV)}
CLV:=(CLOSE-LOW-HIGH+CLOSE)/(HIGH-LOW);

{计算资金流量}
MF:=CLV*VOL;

{计算原始CMF}
CMF原始:=SUM(MF,N)/SUM(VOL,N);

{计算P周期内CMF的最高值和最低值}
MAXCMF:=HHV(CMF原始,P);
MINCMF:=LLV(CMF原始,P);

{0-100标准化CMF}
CMF:(CMF原始-MINCMF)/(MAXCMF-MINCMF)*100,COLORWHITE,LINETHICK1;

{价格高低点判断}
HH:=HIGH>=HHV(HIGH,M);
LL:=LOW<=LLV(LOW,M);

{标准化CMF高低点判断}
CMFHH:=CMF>=HHV(CMF,M);
CMFLL:=CMF<=LLV(CMF,M);

{背离信号计算 - 基于标准化后的CMF}
{顶背离：价格创新高但CMF未创新高，且CMF在50以上}
顶背离:=HH AND CMFHH=0 AND CMF>50;

{底背离：价格创新低但CMF未创新低，且CMF在50以下}
底背离:=LL AND CMFLL=0 AND CMF<50;

{标准化参考线}
超买线:80,COLORRED,LINETHICK1;
中线:50,COLORYELLOW,LINETHICK1;
超卖线:20,COLORGREEN,LINETHICK1;

{背离信号显示}
STICKLINE(顶背离,50,CMF,3,0),COLORRED;
STICKLINE(底背离,50,CMF,3,0),COLORGREEN;

{背离信号文字提示}
DRAWTEXT(顶背离,CMF+5,'顶背离'),COLORRED;
DRAWTEXT(底背离,CMF-5,'底背离'),COLORGREEN;

{使用说明}
{1. CMF值在0-100之间波动，50为中性线}
{2. CMF>80：资金流入强劲，超买区域}
{3. CMF<20：资金流出严重，超卖区域}
{4. 顶背离：价格新高但CMF未新高，看跌信号}
{5. 底背离：价格新低但CMF未新低，看涨信号}
{6. 标准化后更便于设置交易策略阈值};


==========================================================================================================


{BIAS标准化指标 - 乖离率0-100标准化版本 单周期}
{N：18:   P:35}

{计算原始乖离率}
BIAS原始:=(CLOSE-MA(CLOSE,N))/MA(CLOSE,N)*100;

{计算P周期内乖离率的最高值和最低值}
HHVBIAS:=HHV(BIAS原始,P);
LLVBIAS:=LLV(BIAS原始,P);

{0-100标准化乖离率}
BIAS:(BIAS原始-LLVBIAS)/(HHVBIAS-LLVBIAS)*100,COLORWHITE,LINETHICK1;

{标准化参考线}
超买线:80,COLORRED,LINETHICK1;
强势线:70,COLOR00FFFF,LINETHICK1;
中线:50,COLORBLUE,LINETHICK1;
弱势线:30,COLOR00FFFF,LINETHICK1;
超卖线:20,COLORGREEN,LINETHICK1;

{单周期信号分析}
{超买超卖信号}
超买信号:=BIAS>80;
超卖信号:=BIAS<20;

{背离分析}
{价格创新高但BIAS未创新高}
顶背离:=HIGH>=HHV(HIGH,10) AND BIAS<HHV(BIAS,10) AND BIAS>70;
{价格创新低但BIAS未创新低}
底背离:=LOW<=LLV(LOW,10) AND BIAS>LLV(BIAS,10) AND BIAS<30;

{趋势信号}
强势:=BIAS>70;
弱势:=BIAS<30;

{信号显示}



{背离信号标记}

DRAWICON(底背离,BIAS-3,2),COLORGREEN;

{文字提示}


{使用说明}
{1. BIAS值在0-100之间波动，50为中性线}
{2. >80：超买区域，考虑减仓}
{3. 70-80：强势区域，持股观望}
{4. 30-70：正常波动区域}
{5. 20-30：弱势区域，关注反弹}
{6. <20：超卖区域，考虑建仓}
{7. 结合背离分析提高准确性}
{8. 单周期更简洁清晰};
