#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MACD背离策略测试脚本

用于验证策略的基本功能和参数设置
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

# 添加框架路径
sys.path.append(os.path.join(os.path.dirname(__file__), '框架'))

# 模拟QMT环境变量
account = "test_account"
accountType = "STOCK"

# 导入策略
try:
    from 六sk线 import init, handlebar, DivergenceDetector
    print("✅ 策略模块导入成功")
except ImportError as e:
    print(f"❌ 策略模块导入失败: {e}")
    sys.exit(1)

class MockContext:
    """模拟QMT策略上下文"""
    def __init__(self):
        self.stockcode = "000001"
        self.market = "SZ"
        
def test_divergence_detector():
    """测试背离检测器"""
    print("\n" + "="*60)
    print("🧪 测试背离检测器")
    print("="*60)
    
    # 创建测试数据
    np.random.seed(42)
    n_bars = 100
    
    # 生成模拟K线数据
    base_price = 10.0
    prices = []
    for i in range(n_bars):
        # 模拟价格走势：先下跌再上涨
        if i < 30:
            trend = -0.02  # 下跌趋势
        elif i < 70:
            trend = 0.01   # 上涨趋势
        else:
            trend = -0.01  # 再次下跌
            
        noise = np.random.normal(0, 0.005)
        base_price *= (1 + trend + noise)
        prices.append(base_price)
    
    # 构建K线数据
    klines = []
    for i, price in enumerate(prices):
        high = price * (1 + abs(np.random.normal(0, 0.01)))
        low = price * (1 - abs(np.random.normal(0, 0.01)))
        volume = np.random.randint(1000, 10000)
        
        kline = {
            'open': prices[i-1] if i > 0 else price,
            'high': high,
            'low': low,
            'close': price,
            'volume': volume,
            'time': f"2024-01-{i+1:02d} 09:30:00"
        }
        klines.append(kline)
    
    # 测试背离检测器
    detector = DivergenceDetector(
        fast_period=12,
        slow_period=26,
        signal_period=9,
        peak_valley_window=5,
        min_divergence_bars=8,
        max_divergence_bars=25
    )
    
    print(f"📊 生成测试数据: {len(klines)}根K线")
    print(f"📈 价格范围: {min(prices):.3f} - {max(prices):.3f}")
    
    # 获取背离信号
    result = detector.get_divergence_signals(klines)
    
    print(f"\n🔍 背离检测结果:")
    print(f"   状态: {result['status']}")
    
    if result['status'] == 'success':
        bullish = result['bullish_divergence']
        bearish = result['bearish_divergence']
        
        if bullish:
            print(f"   📈 底背离: 强度={bullish['strength']}, 跨度={bullish['bars_span']}根K线")
        else:
            print(f"   📈 底背离: 无")
            
        if bearish:
            print(f"   📉 顶背离: 强度={bearish['strength']}, 跨度={bearish['bars_span']}根K线")
        else:
            print(f"   📉 顶背离: 无")
            
        data_quality = result['data_quality']
        print(f"   📊 数据质量: {data_quality['total_bars']}根K线, 覆盖率{data_quality['data_coverage']:.1%}")
    else:
        print(f"   ❌ 检测失败: {result.get('error_message', 'unknown')}")

def test_strategy_initialization():
    """测试策略初始化"""
    print("\n" + "="*60)
    print("🧪 测试策略初始化")
    print("="*60)
    
    # 创建模拟上下文
    C = MockContext()
    
    try:
        # 初始化策略
        init(C)
        
        print("✅ 策略初始化成功")
        print(f"📊 MACD参数: 快线={C.macd_fast_period}, 慢线={C.macd_slow_period}, 信号线={C.macd_signal_period}")
        print(f"🔍 背离检测: 峰谷窗口={C.peak_valley_window}, 最小强度={C.min_signal_strength}")
        print(f"🛡️ 风险控制: 止损{C.stop_loss_pct*100}%, 止盈{C.take_profit_pct*100}%, 最大持仓{C.max_hold_bars}根K线")
        print(f"📈 成交量过滤: {'启用' if C.enable_volume_filter else '禁用'}")
        
        # 检查关键属性
        required_attrs = [
            'macd_fast_period', 'macd_slow_period', 'macd_signal_period',
            'peak_valley_window', 'min_divergence_bars', 'max_divergence_bars',
            'min_signal_strength', 'stop_loss_pct', 'take_profit_pct', 'max_hold_bars',
            'enable_volume_filter', 'volume_ma_period', 'volume_ratio',
            'divergence_detector', 'merged_klines', 'position'
        ]
        
        missing_attrs = []
        for attr in required_attrs:
            if not hasattr(C, attr):
                missing_attrs.append(attr)
        
        if missing_attrs:
            print(f"⚠️ 缺少属性: {missing_attrs}")
        else:
            print("✅ 所有必需属性已设置")
            
    except Exception as e:
        print(f"❌ 策略初始化失败: {e}")
        import traceback
        traceback.print_exc()

def test_parameter_validation():
    """测试参数验证"""
    print("\n" + "="*60)
    print("🧪 测试参数验证")
    print("="*60)
    
    C = MockContext()
    init(C)
    
    # 验证MACD参数
    assert C.macd_fast_period < C.macd_slow_period, "MACD快线周期应小于慢线周期"
    assert C.macd_signal_period > 0, "MACD信号线周期应大于0"
    
    # 验证背离检测参数
    assert C.peak_valley_window > 0, "峰谷窗口应大于0"
    assert C.min_divergence_bars < C.max_divergence_bars, "最小背离跨度应小于最大跨度"
    assert C.min_signal_strength in ['weak', 'medium', 'strong'], "信号强度设置无效"
    
    # 验证风险控制参数
    assert 0 < C.stop_loss_pct < 1, "止损百分比应在0-1之间"
    assert 0 < C.take_profit_pct < 1, "止盈百分比应在0-1之间"
    assert C.max_hold_bars > 0, "最大持仓时间应大于0"
    
    # 验证成交量参数
    assert C.volume_ma_period > 0, "成交量均线周期应大于0"
    assert C.volume_ratio > 0, "成交量比例应大于0"
    
    print("✅ 所有参数验证通过")

def main():
    """主测试函数"""
    print("🚀 MACD背离策略测试开始")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 运行测试
        test_strategy_initialization()
        test_parameter_validation()
        test_divergence_detector()
        
        print("\n" + "="*60)
        print("✅ 所有测试完成")
        print("="*60)
        print("📋 测试总结:")
        print("   ✅ 策略初始化正常")
        print("   ✅ 参数设置有效")
        print("   ✅ 背离检测器工作正常")
        print("   ✅ 策略已准备就绪")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
