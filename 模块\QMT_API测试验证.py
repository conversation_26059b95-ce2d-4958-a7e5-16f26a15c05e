# coding: gbk
"""
QMT API测试验证模块
基于官方文档验证正确的API用法

主要测试：
1. passorder下单的正确用法和返回值处理
2. get_last_order_id获取订单ID
3. get_trade_detail_data获取委托信息
4. cancel撤单功能
5. 委托方向代码的正确识别
"""

def init(ContextInfo):
    """初始化函数"""
    print("=" * 60)
    print("🧪 QMT API测试验证模块启动")
    print("=" * 60)
    
    # 设置测试账户（QMT运行时会自动传递真实账户）
    try:
        ContextInfo.acct = account          # QMT自动传递的账户ID
        ContextInfo.acct_type = accountType # QMT自动传递的账户类型
        print(f"📊 使用真实账户: {ContextInfo.acct} (类型: {ContextInfo.acct_type})")
    except NameError:
        # 测试环境使用模拟账户
        ContextInfo.acct = 'test_account'
        ContextInfo.acct_type = 'STOCK'
        print(f"🧪 使用测试账户: {ContextInfo.acct}")
    
    # 测试股票
    ContextInfo.test_stock = '000001.SZ'
    print(f"🎯 测试股票: {ContextInfo.test_stock}")

def handlebar(ContextInfo):
    """主要测试逻辑"""
    if not ContextInfo.is_last_bar():
        return
    
    print("\n" + "=" * 50)
    print("🔍 开始API测试")
    print("=" * 50)
    
    # 测试1: 获取账户信息
    test_get_account_info(ContextInfo)
    
    # 测试2: 获取委托信息
    test_get_orders(ContextInfo)
    
    # 测试3: 测试下单（小量测试）
    # test_place_order(ContextInfo)  # 注释掉避免意外下单

def test_get_account_info(ContextInfo):
    """测试获取账户信息"""
    print("\n📊 测试1: 获取账户信息")
    print("-" * 30)
    
    try:
        # 使用QMT内置函数获取账户信息
        account_info = get_trade_detail_data(ContextInfo.acct, ContextInfo.acct_type.lower(), 'account')
        
        if account_info:
            print(f"✅ 成功获取账户信息，数量: {len(account_info)}")
            for i, info in enumerate(account_info):
                print(f"   账户{i+1}: {type(info)}")
                # 显示账户对象的所有属性
                attrs = [attr for attr in dir(info) if not attr.startswith('_')]
                print(f"   可用属性: {attrs[:5]}...")  # 只显示前5个属性
        else:
            print("❌ 未获取到账户信息")
            
    except Exception as e:
        print(f"❌ 获取账户信息失败: {e}")

def test_get_orders(ContextInfo):
    """测试获取委托信息"""
    print("\n📋 测试2: 获取委托信息")
    print("-" * 30)
    
    try:
        # 获取委托信息
        orders = get_trade_detail_data(ContextInfo.acct, ContextInfo.acct_type.lower(), 'order')
        
        if orders:
            print(f"✅ 成功获取委托信息，数量: {len(orders)}")
            
            for i, order in enumerate(orders[:3]):  # 只显示前3个委托
                print(f"\n   委托{i+1}:")
                print(f"   类型: {type(order)}")
                
                # 获取关键字段
                order_id = getattr(order, 'm_strOrderSysID', '未知')
                stock_code = getattr(order, 'm_strInstrumentID', '未知')
                stock_name = getattr(order, 'm_strInstrumentName', '未知')
                
                # 获取方向字段（重点测试）
                direction = getattr(order, 'm_nDirection', -1)
                offset_flag = getattr(order, 'm_nOffsetFlag', -1)
                
                print(f"   委托号: {order_id}")
                print(f"   股票: {stock_code} ({stock_name})")
                print(f"   方向字段: m_nDirection={direction}, m_nOffsetFlag={offset_flag}")
                
                # 根据官方文档解析方向
                if offset_flag == 23:
                    direction_desc = "买入"
                elif offset_flag == 24:
                    direction_desc = "卖出"
                else:
                    direction_desc = f"未知({offset_flag})"
                
                print(f"   解析方向: {direction_desc}")
                
                # 显示所有可用属性
                attrs = [attr for attr in dir(order) if not attr.startswith('_') and not callable(getattr(order, attr))]
                print(f"   所有属性: {attrs}")
                
        else:
            print("ℹ️  当前无委托信息")
            
    except Exception as e:
        print(f"❌ 获取委托信息失败: {e}")

def test_place_order(ContextInfo):
    """测试下单功能（谨慎使用）"""
    print("\n💰 测试3: 下单功能")
    print("-" * 30)
    print("⚠️  注意：这是真实下单测试，请确认要执行")
    
    try:
        # 获取当前价格
        current_price = ContextInfo.get_market_data(['close'], [ContextInfo.test_stock])
        if not current_price or len(current_price) == 0:
            print("❌ 无法获取当前价格")
            return
        
        price = current_price[ContextInfo.test_stock]['close']
        test_volume = 100  # 最小交易单位
        
        print(f"📈 当前价格: {price:.3f}")
        print(f"🎯 测试下单: 买入{test_volume}股@{price:.3f}")
        
        # 执行下单（根据官方文档）
        order_result = passorder(
            23,                      # opType: 买入
            1101,                    # orderType: 限价单
            ContextInfo.acct,        # accountID: 账户ID
            ContextInfo.test_stock,  # orderCode: 股票代码
            11,                      # prType: 限价
            price,                   # price: 价格
            test_volume,             # volume: 数量
            'API测试',               # strategyName: 策略名称
            1,                       # quickTrade: 快速下单
            'API测试订单',           # userOrderId: 用户订单ID
            ContextInfo              # ContextInfo: 上下文
        )
        
        print(f"📋 下单返回值: {order_result} (类型: {type(order_result)})")
        
        # 根据官方文档，passorder返回None，需要通过get_last_order_id获取订单ID
        import time
        time.sleep(1)  # 等待订单处理
        
        # 获取最新订单ID
        latest_order_id = get_last_order_id(ContextInfo.acct, ContextInfo.acct_type.lower(), 'order', 'API测试')
        print(f"📋 最新订单ID: {latest_order_id}")
        
        if latest_order_id and latest_order_id != '-1':
            print("✅ 下单成功，已获取订单ID")
            
            # 测试撤单
            print("🔄 测试撤单...")
            cancel_result = cancel(latest_order_id, ContextInfo.acct, ContextInfo.acct_type, ContextInfo)
            print(f"📋 撤单结果: {cancel_result}")
            
        else:
            print("⚠️  未获取到订单ID")
            
    except Exception as e:
        print(f"❌ 下单测试失败: {e}")

def test_get_last_order_id(ContextInfo):
    """测试获取最新订单ID"""
    print("\n🆔 测试4: 获取最新订单ID")
    print("-" * 30)
    
    try:
        # 获取最新的订单ID
        latest_order_id = get_last_order_id(ContextInfo.acct, ContextInfo.acct_type.lower(), 'order')
        print(f"📋 最新订单ID: {latest_order_id}")
        
        if latest_order_id and latest_order_id != '-1':
            # 获取该订单的详细信息
            order_detail = get_value_by_order_id(latest_order_id, ContextInfo.acct, ContextInfo.acct_type, 'order')
            if order_detail:
                print(f"✅ 获取到订单详情")
                print(f"   股票: {getattr(order_detail, 'm_strInstrumentID', '未知')}")
                print(f"   方向: {getattr(order_detail, 'm_nOffsetFlag', '未知')}")
            else:
                print("❌ 无法获取订单详情")
        else:
            print("ℹ️  无最新订单")
            
    except Exception as e:
        print(f"❌ 获取最新订单ID失败: {e}")

if __name__ == "__main__":
    print("🧪 QMT API测试验证模块")
    print("请在QMT环境中运行此脚本")
