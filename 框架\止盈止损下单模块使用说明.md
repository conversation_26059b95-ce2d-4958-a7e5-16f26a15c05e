# 止盈止损和下单模块使用说明

## 概述

本模块从原始的6sk线.py策略文件中提取了核心的止盈止损和下单功能，可以独立使用和测试。

## 文件说明

### 1. `止盈止损下单模块测试.py` - 完整版本
- **功能**: 包含完整的止盈止损和下单逻辑
- **依赖**: numpy, pandas (用于技术分析计算)
- **特点**: 
  - 完整的移动止盈风控计算
  - 基于ATR的动态止盈止损
  - 完整的订单管理和撤单功能
  - 模拟QMT交易环境

### 2. `简化测试.py` - 简化版本
- **功能**: 简化的止盈止损和下单逻辑
- **依赖**: 无外部依赖，纯Python实现
- **特点**:
  - 简化的移动止盈逻辑
  - 基本的止盈止损检查
  - 模拟交易环境
  - 可直接运行测试

## 核心功能模块

### 1. 止盈止损检查模块

#### `check_exit_conditions(C, current_price, 移动止盈_info=None)`
检查是否应该平仓的核心函数。

**参数:**
- `C`: 策略上下文对象
- `current_price`: 当前价格
- `移动止盈_info`: 移动止盈风控信息（可选）

**返回:**
```python
{
    'should_exit': True/False,  # 是否应该平仓
    'reason': '平仓原因',        # 平仓原因说明
    'price': 当前价格           # 建议平仓价格
}
```

**平仓条件:**
1. **固定止损**: 亏损超过0.5%
2. **市场动态止损**: 基于ATR计算的动态止损
3. **移动止盈**: 保护已实现利润的移动止盈线
4. **市场动态止盈**: 基于ATR计算的动态止盈

### 2. 移动止盈风控计算

#### `calculate_moving_profit_control(highs, lows, closes)`
计算基于ATR的优化移动止盈风控参数。

**参数:**
- `highs`: 最高价数组
- `lows`: 最低价数组
- `closes`: 收盘价数组

**返回:**
```python
{
    'ATR_周期': 21,              # ATR计算周期
    'ATR值': 0.05,              # ATR绝对值
    'ATR_百分比': 0.23,         # ATR百分比
    '止损距离': 0.23,           # 止损距离百分比
    '初始止盈距离': 0.23,       # 初始止盈距离百分比
    '标准移动触发距离': 0.23,   # 标准移动触发距离
    '加速移动触发距离': 0.16,   # 加速移动触发距离
    '最小移动幅度': 0.1,        # 最小移动幅度
    '当前价格': 10.0,           # 当前价格
    '市场模式': '统一21日ATR',   # 市场模式
    '波动率区间': '正常波动区',  # 波动率区间
    '计算模式': '优化移动止盈方案' # 计算模式
}
```

### 3. 订单执行模块

#### `execute_buy_order(C, current_price, volume=None)`
执行买入订单，带挂单偏移策略。

**特点:**
- 自动撤销之前的买入订单
- 挂单价格略低于当前价格（减少滑点）
- 自动计算买入数量（默认使用30%资金）
- 完整的风险检查

#### `execute_sell_order(C, current_price, reason="手动卖出")`
执行卖出订单，带挂单偏移策略。

**特点:**
- 自动撤销之前的卖出订单
- 挂单价格略高于当前价格（减少滑点）
- 自动获取持仓数量
- 计算盈亏统计

### 4. 订单管理模块

#### `check_has_pending_orders(C, order_type='ALL')`
检查是否有未处理的订单。

#### `cancel_pending_orders(C, order_type='ALL')`
撤销未成交的挂单。

## 使用示例

### 基本使用流程

```python
from 止盈止损下单模块测试 import *

# 1. 创建策略上下文
C = MockContext('000001.SZ')

# 2. 模拟市场数据
market_data = create_test_data(50)

# 3. 计算移动止盈风控
移动止盈_info = calculate_moving_profit_control(
    market_data['highs'],
    market_data['lows'], 
    market_data['closes']
)

# 4. 执行买入
current_price = 10.0
buy_result = execute_buy_order(C, current_price, 1000)

if buy_result['success']:
    print(f"买入成功: {buy_result}")
    
    # 5. 检查止盈止损
    new_price = 10.5
    exit_result = check_exit_conditions(C, new_price, 移动止盈_info)
    
    if exit_result['should_exit']:
        # 6. 执行卖出
        sell_result = execute_sell_order(C, new_price, exit_result['reason'])
        print(f"卖出结果: {sell_result}")
```

### 完整交易循环示例

```python
def trading_loop():
    C = MockContext('000001.SZ')
    
    # 模拟价格序列
    prices = [10.0, 10.2, 10.5, 11.0, 10.8, 11.2, 11.5, 11.0]
    
    for i, price in enumerate(prices):
        print(f"第{i+1}天，价格: {price}")
        
        if C.position == 0:
            # 无持仓时考虑买入
            if i == 2:  # 第3天买入
                buy_result = execute_buy_order(C, price, 1000)
                
        else:
            # 有持仓时检查止盈止损
            exit_result = check_exit_conditions(C, price)
            
            if exit_result['should_exit']:
                sell_result = execute_sell_order(C, price, exit_result['reason'])
                break
```

## 配置参数

### 策略上下文参数

```python
class MockContext:
    # 基本信息
    stock = '000001.SZ'          # 股票代码
    acct = 'TEST_ACCOUNT'        # 账户
    
    # 持仓状态
    position = 0                 # 持仓状态 (0=空仓, 1=多头)
    entry_price = 0              # 入场价格
    
    # 移动止盈参数
    use_trailing_stop = True     # 是否使用移动止盈
    trailing_stop_ratio = 0.02   # 移动止盈比例
    
    # 风险控制参数
    stop_loss_pct = 0.05         # 止损比例
    take_profit_pct = 0.10       # 止盈比例
    固定止损 = 0.5               # 固定止损百分比
    
    # 挂单偏移参数
    buy_hang_offset_ratio = 0.002   # 买入挂单偏移比例
    sell_hang_offset_ratio = 0.002  # 卖出挂单偏移比例
```

## 测试运行

### 运行完整测试
```bash
python 框架/止盈止损下单模块测试.py
```

### 运行简化测试
```bash
python 框架/简化测试.py
```

## 注意事项

1. **模拟环境**: 当前版本使用模拟的QMT环境，实际使用时需要连接真实的QMT平台
2. **数据依赖**: 完整版本需要numpy和pandas库
3. **风险控制**: 实盘使用前请充分测试和验证
4. **参数调整**: 根据实际市场情况调整止盈止损参数

## 扩展功能

可以根据需要扩展以下功能：
- 更复杂的技术指标计算
- 多股票组合管理
- 更精细的风险控制
- 实时数据接口
- 回测框架集成

## 技术支持

如有问题，请检查：
1. Python环境是否正确安装
2. 依赖库是否完整
3. 参数配置是否合理
4. 模拟数据是否有效
