# -*- coding: utf-8 -*-
"""
QMT数据预热测试策略 - 修正版
基于QMT官方文档的正确API使用方式

主要功能:
1. 测试历史数据下载和获取
2. 验证实时数据接收
3. 检查ATR计算所需的数据完整性

使用方法:
1. 在QMT中新建策略
2. 复制此代码
3. 运行策略查看测试结果

作者: QMT策略开发
版本: 1.0.0
日期: 2024-12-19
"""

import datetime
import numpy as np

# ============================================================================
# QMT策略标准接口
# ============================================================================

def init(ContextInfo):
    """
    策略初始化函数 - 下载历史数据
    注意: 根据QMT官方文档，在init()中只能下载数据，不能获取数据
    """
    print("🚀 QMT数据预热测试策略启动 (修正版)")
    print("="*60)
    
    # 获取股票代码
    stock_code = getattr(ContextInfo, 'stock', '123171.SZ')
    print(f"📊 测试股票: {stock_code}")
    print(f"🕐 测试时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 下载历史数据（QMT要求在init中完成）
    print(f"\n📥 开始下载历史数据...")
    
    try:
        # 方法1: 使用内置函数下载历史数据
        download_history_data(stock_code, "1d", "20230101", "")
        print(f"✅ 日线数据下载完成")
        
        # 下载分钟数据用于更精确的分析
        download_history_data(stock_code, "1m", "20241201", "")
        print(f"✅ 分钟数据下载完成")
        
    except Exception as e:
        print(f"⚠️ 历史数据下载异常: {e}")
        print(f"💡 提示: 请检查网络连接和股票代码")
    
    print(f"\n💡 数据下载完成，等待handlebar()函数进行数据测试...")
    print("="*60)

def handlebar(ContextInfo):
    """
    K线数据处理函数 - 执行数据测试
    注意: 根据QMT官方文档，数据获取应在handlebar()中进行
    """
    # 获取当前K线位置，避免重复测试
    current_bar = getattr(ContextInfo, 'barpos', 0)
    last_test_bar = getattr(ContextInfo, 'last_test_bar', -1)
    
    # 每10根K线执行一次完整测试
    if current_bar - last_test_bar >= 10:
        ContextInfo.last_test_bar = current_bar
        
        print(f"\n🔍 执行数据预热测试 (K线位置: {current_bar})")
        print("-" * 50)
        
        # 执行测试
        test1_result = test_historical_data(ContextInfo)
        test2_result = test_atr_calculation(ContextInfo)
        test3_result = test_realtime_data(ContextInfo)
        
        # 测试结果总结
        print(f"\n📋 测试结果总结:")
        print(f"✅ 历史数据测试: {'通过' if test1_result else '失败'}")
        print(f"✅ ATR计算测试: {'通过' if test2_result else '失败'}")
        print(f"✅ 实时数据测试: {'通过' if test3_result else '失败'}")
        
        if all([test1_result, test2_result, test3_result]):
            print(f"🎉 所有测试通过，数据预热成功！")
        else:
            print(f"⚠️ 部分测试失败，请检查数据连接")
        
        print("-" * 50)

# ============================================================================
# 测试函数
# ============================================================================

def test_historical_data(ContextInfo):
    """
    测试历史数据获取
    """
    try:
        stock_code = getattr(ContextInfo, 'stock', '123171.SZ')
        print(f"📈 测试历史数据获取: {stock_code}")
        
        # 使用QMT官方API获取历史数据
        hist_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume'],
            stock_list=[stock_code],
            period='1d',
            count=30,
            dividend_type='none',
            fill_data=True
        )
        
        if hist_data and stock_code in hist_data:
            data_df = hist_data[stock_code]
            data_count = len(data_df)
            
            if data_count >= 20:
                # 获取最新数据
                latest_close = data_df['close'].iloc[-1]
                latest_volume = data_df['volume'].iloc[-1]
                
                print(f"  ✅ 获取到 {data_count} 根K线数据")
                print(f"  📊 最新价格: {latest_close:.3f}")
                print(f"  📊 最新成交量: {latest_volume:.0f}")
                return True
            else:
                print(f"  ❌ 数据不足: 仅获取到 {data_count} 根K线")
                return False
        else:
            print(f"  ❌ 未获取到数据")
            return False
            
    except Exception as e:
        print(f"  ❌ 历史数据测试异常: {e}")
        return False

def test_atr_calculation(ContextInfo):
    """
    测试ATR计算所需数据
    """
    try:
        stock_code = getattr(ContextInfo, 'stock', '123171.SZ')
        print(f"📊 测试ATR计算数据: {stock_code}")
        
        # 获取ATR计算所需的数据（至少14根K线）
        atr_data = ContextInfo.get_market_data_ex(
            fields=['high', 'low', 'close'],
            stock_list=[stock_code],
            period='1d',
            count=20,
            dividend_type='none',
            fill_data=True
        )
        
        if atr_data and stock_code in atr_data:
            data_df = atr_data[stock_code]
            
            if len(data_df) >= 14:
                # 计算ATR
                highs = data_df['high'].values
                lows = data_df['low'].values
                closes = data_df['close'].values
                
                # 计算真实波幅
                tr_list = []
                for i in range(1, len(highs)):
                    tr1 = highs[i] - lows[i]
                    tr2 = abs(highs[i] - closes[i-1])
                    tr3 = abs(lows[i] - closes[i-1])
                    tr_list.append(max(tr1, tr2, tr3))
                
                if len(tr_list) >= 14:
                    atr = np.mean(tr_list[-14:])
                    trigger_threshold = atr * 4.0
                    stop_loss_threshold = atr * 3.0
                    
                    print(f"  ✅ ATR计算成功")
                    print(f"  📊 ATR值: {atr:.4f}")
                    print(f"  📊 触发阈值: {trigger_threshold:.4f}")
                    print(f"  📊 止损阈值: {stop_loss_threshold:.4f}")
                    return True
                else:
                    print(f"  ❌ TR数据不足: {len(tr_list)} < 14")
                    return False
            else:
                print(f"  ❌ K线数据不足: {len(data_df)} < 14")
                return False
        else:
            print(f"  ❌ 未获取到ATR数据")
            return False
            
    except Exception as e:
        print(f"  ❌ ATR计算测试异常: {e}")
        return False

def test_realtime_data(ContextInfo):
    """
    测试实时数据获取
    """
    try:
        stock_code = getattr(ContextInfo, 'stock', '123171.SZ')
        print(f"📡 测试实时数据获取: {stock_code}")
        
        # 获取最新的实时数据
        realtime_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume'],
            stock_list=[stock_code],
            period='1d',
            count=1,
            dividend_type='none',
            fill_data=True
        )
        
        if realtime_data and stock_code in realtime_data:
            data_df = realtime_data[stock_code]
            
            if len(data_df) > 0:
                latest_data = data_df.iloc[-1]
                current_price = latest_data['close']
                current_volume = latest_data['volume']
                
                print(f"  ✅ 实时数据获取成功")
                print(f"  📊 当前价格: {current_price:.3f}")
                print(f"  📊 当前成交量: {current_volume:.0f}")
                
                # 检查数据合理性
                if current_price > 0 and current_volume >= 0:
                    return True
                else:
                    print(f"  ❌ 数据异常: 价格={current_price}, 成交量={current_volume}")
                    return False
            else:
                print(f"  ❌ 实时数据为空")
                return False
        else:
            print(f"  ❌ 未获取到实时数据")
            return False
            
    except Exception as e:
        print(f"  ❌ 实时数据测试异常: {e}")
        return False

# ============================================================================
# 策略信息
# ============================================================================

print("📄 QMT数据预热测试策略已加载")
print("🔧 基于QMT官方文档API规范")
print("📅 版本: 1.0.0 (2024-12-19)")
