# QMT止盈止损下单模块 - 时间风控功能说明

## 功能概述

为QMT止盈止损下单模块添加了时间风控功能，实现以下规则：
- **每日14:55之后不开仓**
- **每日14:56卖出手里所有仓位**

## 配置参数

在 `STRATEGY_CONFIG` 中新增以下配置项：

```python
# 时间风控配置
'enable_time_risk_control': True,  # 是否启用时间风控
'no_open_after_time': '14:55:00',  # 此时间后不开仓 (格式: HH:MM:SS)
'force_close_time': '14:56:00',    # 强制平仓时间 (格式: HH:MM:SS)
'time_zone_offset': 0,             # 时区偏移（小时），0表示使用系统时间
```

## 核心函数

### 1. check_time_risk_control(ContextInfo)
检查时间风控条件，返回当前时间状态：
- `allow_open`: 是否允许开仓
- `force_close`: 是否强制平仓
- `current_time`: 当前时间
- `reason`: 原因说明

### 2. force_close_all_positions(ContextInfo, reason)
强制平仓所有持仓：
- 自动获取当前所有持仓
- 逐个执行卖出操作
- 返回平仓执行结果

## 集成位置

### 1. 主策略函数 (handlebar)
```python
def handlebar(C):
    # 0. 时间风控检查
    time_control = check_time_risk_control(C)
    
    # 强制平仓检查
    if time_control['force_close']:
        print(f"🚨 时间风控触发强制平仓: {time_control['reason']}")
        force_close_result = force_close_all_positions(C, "时间风控强制平仓")
        return  # 强制平仓后直接返回
```

### 2. 买入订单函数 (execute_buy_order)
```python
def execute_buy_order(ContextInfo, stock_code, current_price, ...):
    # 1. 时间风控检查
    time_control = check_time_risk_control(ContextInfo)
    
    if time_control['force_close']:
        return {'success': False, 'reason': f"时间风控禁止: {time_control['reason']}"}
    
    if not time_control['allow_open']:
        return {'success': False, 'reason': f"时间风控禁止开仓: {time_control['reason']}"}
```

### 3. 买入信号检查 (check_buy_signal_three_bars)
```python
def check_buy_signal_three_bars(ContextInfo, stock_code, market_data=None):
    # 时间风控检查 - 如果不允许开仓，直接返回
    time_control = check_time_risk_control(ContextInfo)
    if not time_control['allow_open']:
        return {
            'buy_signal': False,
            'reason': f'时间风控禁止开仓: {time_control["reason"]}',
            'data_count': 0
        }
```

## 工作流程

### 时间段划分
1. **正常交易时间** (开盘 - 14:55:00)
   - 允许开仓和平仓
   - 正常执行所有交易逻辑

2. **禁止开仓时间** (14:55:00 - 14:56:00)
   - 禁止新开仓位
   - 允许平仓操作
   - 买入信号被自动忽略

3. **强制平仓时间** (14:56:00 - 收盘)
   - 自动平仓所有持仓
   - 禁止所有开仓操作
   - 策略进入保护模式

### 执行逻辑
```
当前时间 < 14:55:00  →  正常交易
当前时间 >= 14:55:00 且 < 14:56:00  →  禁止开仓，允许平仓
当前时间 >= 14:56:00  →  强制平仓所有持仓
```

## 使用方法

### 1. 启用时间风控
```python
STRATEGY_CONFIG['enable_time_risk_control'] = True
```

### 2. 自定义时间点
```python
STRATEGY_CONFIG['no_open_after_time'] = '14:50:00'  # 提前到14:50
STRATEGY_CONFIG['force_close_time'] = '14:55:00'    # 提前到14:55
```

### 3. 禁用时间风控
```python
STRATEGY_CONFIG['enable_time_risk_control'] = False
```

## 日志输出

### 正常时间
```
✅ 时间风控: 14:30:15 - 时间风控正常，允许交易
```

### 禁止开仓时间
```
⏰ 时间风控: 14:55:30 - 已过开仓截止时间(14:55:00)，禁止开仓
```

### 强制平仓时间
```
🚨 时间风控: 14:56:10 - 已到强制平仓时间(14:56:00)，必须清仓
🚨 时间风控触发强制平仓: 已到强制平仓时间(14:56:00)，必须清仓
🔴 强制平仓: 000001.SZ 1000股
✅ 000001.SZ 强制平仓委托已提交
📊 强制平仓结果: 强制平仓完成: 成功1个, 失败0个
```

## 注意事项

1. **时间格式**: 必须使用 'HH:MM:SS' 格式（24小时制）
2. **系统时间**: 使用系统本地时间，确保时间准确
3. **强制平仓**: 14:56后会立即执行强制平仓，无法撤销
4. **委托处理**: 强制平仓使用市价单，确保快速成交
5. **异常处理**: 如果平仓失败，会记录日志并继续尝试

## 风险提示

1. **时间准确性**: 确保系统时间准确，避免误触发
2. **网络延迟**: 考虑网络延迟对时间判断的影响
3. **市场流动性**: 强制平仓时可能面临流动性不足的风险
4. **价格冲击**: 集中平仓可能对价格造成冲击

## 测试建议

1. **模拟环境**: 先在模拟环境中测试时间风控功能
2. **时间调整**: 可以临时调整时间点进行测试
3. **日志监控**: 密切关注时间风控相关的日志输出
4. **异常处理**: 测试各种异常情况下的处理逻辑
