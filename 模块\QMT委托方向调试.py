# coding: gbk
"""
QMT委托方向调试模块
专门用于调试和修复委托方向识别问题

根据用户反馈：
- m_nOffsetFlag=48, m_nDirection=48
- 显示为"买卖未知(offset=48,dir=48)"
- 需要找到正确的买卖方向识别方法
"""

def init(ContextInfo):
    """初始化函数"""
    print("=" * 60)
    print("🔍 QMT委托方向调试模块启动")
    print("=" * 60)
    
    # 设置测试账户
    try:
        ContextInfo.acct = account
        ContextInfo.acct_type = accountType
        print(f"📊 使用账户: {ContextInfo.acct} (类型: {ContextInfo.acct_type})")
    except NameError:
        ContextInfo.acct = 'test_account'
        ContextInfo.acct_type = 'STOCK'
        print(f"🧪 使用测试账户: {ContextInfo.acct}")

def handlebar(ContextInfo):
    """主要调试逻辑"""
    if not ContextInfo.is_last_bar():
        return
    
    print("\n" + "=" * 50)
    print("🔍 开始委托方向调试")
    print("=" * 50)
    
    debug_order_direction(ContextInfo)

def debug_order_direction(ContextInfo):
    """调试委托方向识别"""
    print("\n🔍 委托方向调试分析")
    print("-" * 40)
    
    try:
        # 获取委托信息
        orders = get_trade_detail_data(ContextInfo.acct, ContextInfo.acct_type.lower(), 'order')
        
        if not orders:
            print("📋 当前无委托信息")
            return
        
        print(f"📋 找到 {len(orders)} 个委托")
        
        for i, order_obj in enumerate(orders):
            print(f"\n📋 委托 #{i+1} 详细分析:")
            print("=" * 30)
            
            # 显示所有可能的方向相关字段
            direction_fields = [
                'm_nDirection',      # 买卖方向
                'm_nOffsetFlag',     # 操作类型
                'm_eEntrustType',    # 委托类别
                'm_strOptName',      # 买卖标记（中文）
                'm_eOrderStatus',    # 订单状态
                'm_eFutureTradeType' # 成交类型
            ]
            
            print("🔍 方向相关字段:")
            field_values = {}
            for field in direction_fields:
                if hasattr(order_obj, field):
                    value = getattr(order_obj, field)
                    field_values[field] = value
                    print(f"   {field}: {value} ({type(value).__name__})")
                else:
                    print(f"   {field}: [不存在]")
            
            # 基本信息
            order_id = getattr(order_obj, 'm_strOrderSysID', '未知')
            stock_code = getattr(order_obj, 'm_strInstrumentID', '未知')
            stock_name = getattr(order_obj, 'm_strInstrumentName', '未知')
            price = getattr(order_obj, 'm_dPrice', 0) or getattr(order_obj, 'm_dLimitPrice', 0)
            volume = getattr(order_obj, 'm_nVolume', 0) or getattr(order_obj, 'm_nVolumeTotalOriginal', 0)
            
            print(f"\n📊 基本信息:")
            print(f"   委托号: {order_id}")
            print(f"   股票: {stock_code} ({stock_name})")
            print(f"   价格: {price}")
            print(f"   数量: {volume}")
            
            # 尝试多种方向识别方法
            print(f"\n🎯 方向识别尝试:")
            
            # 方法1：通过m_strOptName（中文描述）
            opt_name = field_values.get('m_strOptName', '')
            if opt_name:
                if '买' in opt_name or '买入' in opt_name:
                    direction1 = '买入'
                elif '卖' in opt_name or '卖出' in opt_name:
                    direction1 = '卖出'
                else:
                    direction1 = f'未知({opt_name})'
                print(f"   方法1 (OptName): '{opt_name}' → {direction1}")
            else:
                print(f"   方法1 (OptName): 无数据")
            
            # 方法2：通过m_nOffsetFlag标准枚举
            offset_flag = field_values.get('m_nOffsetFlag', -1)
            if offset_flag == 23:
                direction2 = '买入'
            elif offset_flag == 24:
                direction2 = '卖出'
            else:
                direction2 = f'未知({offset_flag})'
            print(f"   方法2 (OffsetFlag): {offset_flag} → {direction2}")
            
            # 方法3：通过m_eEntrustType
            entrust_type = field_values.get('m_eEntrustType', -1)
            if entrust_type == 1:
                direction3 = '买入'
            elif entrust_type == 2:
                direction3 = '卖出'
            else:
                direction3 = f'未知({entrust_type})'
            print(f"   方法3 (EntrustType): {entrust_type} → {direction3}")
            
            # 方法4：通过价格和市价比较（推测）
            try:
                current_data = ContextInfo.get_market_data(['close'], [stock_code + '.SZ'])
                if current_data and stock_code + '.SZ' in current_data:
                    current_price = current_data[stock_code + '.SZ']['close']
                    if price > current_price * 1.01:  # 高于市价1%以上
                        direction4 = '可能是卖出'
                    elif price < current_price * 0.99:  # 低于市价1%以上
                        direction4 = '可能是买入'
                    else:
                        direction4 = '接近市价'
                    print(f"   方法4 (价格推测): 委托{price:.3f} vs 市价{current_price:.3f} → {direction4}")
                else:
                    print(f"   方法4 (价格推测): 无法获取市价")
            except:
                print(f"   方法4 (价格推测): 获取失败")
            
            # 显示所有属性（用于发现新的可用字段）
            print(f"\n📋 所有属性 (前20个):")
            all_attrs = [attr for attr in dir(order_obj) if not attr.startswith('_') and not callable(getattr(order_obj, attr))]
            for j, attr in enumerate(all_attrs[:20]):
                try:
                    value = getattr(order_obj, attr)
                    print(f"   {j+1:2d}. {attr}: {value}")
                except:
                    print(f"   {j+1:2d}. {attr}: [获取失败]")
            
            if len(all_attrs) > 20:
                print(f"   ... 还有 {len(all_attrs)-20} 个属性")
            
            print("-" * 30)
            
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()

def analyze_direction_pattern():
    """分析方向识别模式"""
    print("\n📊 QMT委托方向识别分析")
    print("=" * 40)
    
    print("🔍 已知信息:")
    print("   - m_nOffsetFlag = 48")
    print("   - m_nDirection = 48") 
    print("   - 显示为'买卖未知'")
    print("   - 委托状态为'已报待成交'")
    
    print("\n🎯 可能的解决方案:")
    print("   1. 检查 m_strOptName 字段（中文描述）")
    print("   2. 检查 m_eEntrustType 字段（委托类别）")
    print("   3. 通过价格与市价比较推测")
    print("   4. 检查其他未知字段")
    
    print("\n⚠️  注意事项:")
    print("   - QMT不同版本字段可能不同")
    print("   - 股票和期货的字段含义可能不同")
    print("   - 需要实际运行数据来确认")

if __name__ == "__main__":
    print("🔍 QMT委托方向调试模块")
    print("请在QMT环境中运行此脚本")
    analyze_direction_pattern()
