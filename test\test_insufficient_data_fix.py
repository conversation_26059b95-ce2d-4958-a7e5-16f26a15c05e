#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试insufficient_data问题修复
"""

def test_dynamic_mode_with_insufficient_data():
    """测试动态模式处理数据不足的情况"""
    try:
        print("=== 测试动态模式处理数据不足 ===")
        
        import importlib.util
        
        # 加载策略文件
        spec = importlib.util.spec_from_file_location("strategy", "框架/6sk线.py")
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        
        # 创建CMF+BIAS检测器
        detector = strategy_module.CMFBIASDivergenceDetector()
        print(f"📊 检测器最小数据需求: {detector.min_data_length}根K线")
        
        # 测试不同数据量的情况
        test_cases = [
            {"data_count": 5, "description": "严重不足"},
            {"data_count": 15, "description": "可尝试动态模式"},
            {"data_count": 25, "description": "动态模式良好"},
            {"data_count": 35, "description": "动态模式优秀"},
            {"data_count": 55, "description": "标准模式"}
        ]
        
        for case in test_cases:
            print(f"\n📊 测试{case['description']}数据: {case['data_count']}根K线")
            
            # 生成模拟K线数据
            merged_klines = []
            for i in range(case['data_count']):
                kline = {
                    'open': 10.0 + i * 0.1,
                    'high': 10.5 + i * 0.1,
                    'low': 9.8 + i * 0.1,
                    'close': 10.2 + i * 0.1,
                    'volume': 1000 + i * 50
                }
                merged_klines.append(kline)
            
            # 测试综合信号检测
            result = detector.get_comprehensive_signals(merged_klines)
            
            print(f"   状态: {result['status']}")
            if result['status'] == 'success':
                print(f"   买入信号: {result['buy_signal']}")
                data_quality = result.get('data_quality', {})
                print(f"   数据覆盖率: {data_quality.get('data_coverage', 0):.2%}")
                print(f"   动态模式: {data_quality.get('dynamic_mode', False)}")
            elif result['status'] == 'insufficient_data':
                print(f"   错误信息: {result['message']}")
            else:
                print(f"   错误: {result.get('error_message', '未知错误')}")
        
        print("✅ 动态模式数据不足处理测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parameter_recovery():
    """测试参数恢复机制"""
    try:
        print("\n=== 测试参数恢复机制 ===")
        
        import importlib.util
        
        # 加载策略文件
        spec = importlib.util.spec_from_file_location("strategy", "框架/6sk线.py")
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        
        # 创建检测器
        detector = strategy_module.CMFBIASDivergenceDetector()
        
        # 记录原始参数
        original_cmf_n = detector.CMF_N
        original_bias_n = detector.BIAS_N
        original_adx_n = detector.ADX_N
        original_vae_period = detector.VAE_周期
        
        print(f"📊 原始参数: CMF_N={original_cmf_n}, BIAS_N={original_bias_n}, ADX_N={original_adx_n}, VAE_周期={original_vae_period}")
        
        # 生成少量数据触发动态模式
        merged_klines = []
        for i in range(20):  # 20根K线，少于标准需求50根
            kline = {
                'open': 10.0 + i * 0.1,
                'high': 10.5 + i * 0.1,
                'low': 9.8 + i * 0.1,
                'close': 10.2 + i * 0.1,
                'volume': 1000 + i * 50
            }
            merged_klines.append(kline)
        
        # 执行检测（应该触发动态模式）
        result = detector.get_comprehensive_signals(merged_klines)
        
        # 检查参数是否恢复
        print(f"📊 执行后参数: CMF_N={detector.CMF_N}, BIAS_N={detector.BIAS_N}, ADX_N={detector.ADX_N}, VAE_周期={detector.VAE_周期}")
        
        # 验证参数恢复
        if (detector.CMF_N == original_cmf_n and 
            detector.BIAS_N == original_bias_n and 
            detector.ADX_N == original_adx_n and 
            detector.VAE_周期 == original_vae_period):
            print("✅ 参数恢复成功")
            return True
        else:
            print("❌ 参数恢复失败")
            return False
        
    except Exception as e:
        print(f"❌ 参数恢复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 开始insufficient_data修复测试...")
    
    success_count = 0
    total_tests = 2
    
    if test_dynamic_mode_with_insufficient_data():
        success_count += 1
    
    if test_parameter_recovery():
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！insufficient_data问题已修复")
        print("\n📋 修复总结:")
        print("1. ✅ 实现了智能动态模式，数据不足时自动调整参数")
        print("2. ✅ 最小数据需求从50根降低到10根K线")
        print("3. ✅ 参数恢复机制确保不影响后续计算")
        print("4. ✅ 详细的日志输出帮助调试")
    else:
        print("❌ 部分测试失败，需要进一步检查")
