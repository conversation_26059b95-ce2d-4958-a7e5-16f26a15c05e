# QMT Tick数据字段修复说明

## 🚨 **问题描述**

在QMT获取分笔（tick）数据时，出现错误：
```
ERROR获取分笔数据的时候,不支持'close'数据字段
```

## ✅ **解决方案**

### **正确的字段名称**：

| 数据类型 | ❌ 错误字段 | ✅ 正确字段 | 说明 |
|----------|-------------|-------------|------|
| **价格** | `close` | `lastPrice` | 最新成交价 |
| **成交量** | `volume` | `lastVolume` | 最新成交量 |
| **时间** | `time` | `timetag` | 时间戳 |

### **修复前后对比**：

#### **修复前（错误）**：
```python
# ❌ 这会导致ERROR错误
current_data = ContextInfo.get_market_data(['close', 'volume'], [stock_code])
current_price = current_data[stock_code]['close']
current_volume = current_data[stock_code]['volume']
```

#### **修复后（正确）**：
```python
# ✅ 正确的tick数据获取方式
current_data = ContextInfo.get_market_data(['lastPrice', 'lastVolume'], [stock_code])
current_price = current_data[stock_code]['lastPrice']
current_volume = current_data[stock_code]['lastVolume']
```

## 🔧 **已修复的文件**

### **1. 测试文件修复**：
- **文件**：`模块/QMT下单撤单测试.py`
- **修复位置**：
  - `collect_market_data()` 函数
  - `test_place_order_with_offset()` 函数

### **2. 主策略模块修复**：
- **文件**：`模块/QMT止盈止损下单模块.py`
- **修复位置**：
  - `handlebar()` 函数中的市场数据获取
  - 委托方向判断中的价格比较

## 📊 **QMT数据字段完整对照表**

### **Tick数据字段**：
```python
# ✅ QMT Tick数据支持的字段
tick_fields = [
    'lastPrice',    # 最新成交价（必须）
    'lastVolume',   # 最新成交量
    'timetag',      # 时间戳
    'last',         # 最新价（别名）
    'volume',       # 累计成交量
]

# ❌ QMT Tick数据不支持的字段
unsupported_fields = [
    'close',        # 收盘价（K线字段）
    'open',         # 开盘价（K线字段）
    'high',         # 最高价（K线字段）
    'low',          # 最低价（K线字段）
]
```

### **K线数据字段**：
```python
# ✅ QMT K线数据支持的字段
kline_fields = [
    'close',        # 收盘价
    'open',         # 开盘价
    'high',         # 最高价
    'low',          # 最低价
    'volume',       # 成交量
    'time',         # 时间
]
```

## 🎯 **智能字段适配**

为了同时支持tick和K线数据，我们实现了智能字段适配：

```python
def get_price_data(ContextInfo, stock_code):
    """智能获取价格数据（自动适配tick/K线模式）"""
    
    if STRATEGY_CONFIG.get('enable_data_buffer', False):
        # Tick模式：使用lastPrice字段
        market_data = ContextInfo.get_market_data(['lastPrice', 'lastVolume'], [stock_code])
        if market_data and len(market_data) > 0:
            data = market_data[0]
            current_price = data.get('lastPrice', 0)
            volume = data.get('lastVolume', 0)
    else:
        # K线模式：使用传统字段
        market_data = ContextInfo.get_market_data(['close', 'volume'], [stock_code])
        if market_data and len(market_data) > 0:
            data = market_data[0]
            current_price = data.get('close', 0)
            volume = data.get('volume', 0)
    
    return current_price, volume
```

## 🔍 **字段兼容性处理**

在历史数据处理中，我们使用了多重字段尝试：

```python
# 尝试多种可能的字段名（按优先级排序）
tick_price = 0
for price_field in ['lastPrice', 'last_price', 'price', 'close', 'last']:
    if price_field in row and row[price_field] is not None:
        tick_price = float(row[price_field])
        break
```

## ⚠️ **注意事项**

1. **周期设置**：
   - Tick数据：必须设置为tick周期
   - K线数据：可以设置为1分钟、5分钟等

2. **数据频率**：
   - Tick数据：QMT默认3秒一个tick
   - K线数据：根据周期设置

3. **字段检查**：
   - 使用前检查字段是否存在
   - 提供默认值防止程序崩溃

4. **错误处理**：
   - 捕获字段不存在的异常
   - 记录详细的错误信息

## 🎉 **修复结果**

修复后，系统将能够：
- ✅ 正确获取tick数据而不报错
- ✅ 智能适配tick和K线两种模式
- ✅ 提供详细的调试信息
- ✅ 保持向后兼容性

**现在您的策略可以正常运行在tick周期下，不会再出现字段错误！** 🚀
