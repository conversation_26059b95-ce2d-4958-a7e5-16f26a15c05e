#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一21日ATR测试程序
验证移除趋势和震荡市场判断，统一使用21日ATR计算
"""

import numpy as np

def simple_atr_calculation(highs, lows, closes, period=21):
    """简化的ATR计算"""
    if len(closes) < period + 1:
        return 0.05
    
    # 计算True Range
    tr_list = []
    for i in range(1, len(closes)):
        high_low = highs[i] - lows[i]
        high_close = abs(highs[i] - closes[i-1])
        low_close = abs(lows[i] - closes[i-1])
        tr = max(high_low, high_close, low_close)
        tr_list.append(tr)
    
    # 计算ATR (简单移动平均)
    if len(tr_list) >= period:
        atr = np.mean(tr_list[-period:])
        return atr
    else:
        return np.mean(tr_list) if tr_list else 0.05

def test_unified_21day_atr():
    """测试统一21日ATR计算"""
    print("=" * 80)
    print("🎯 统一21日ATR计算测试")
    print("=" * 80)
    
    # 创建不同类型的测试数据
    test_scenarios = [
        {
            "name": "低波动数据",
            "volatility": 0.005,  # 0.5%日波动
            "trend": 0.001,       # 0.1%日趋势
            "days": 30
        },
        {
            "name": "中等波动数据", 
            "volatility": 0.015,  # 1.5%日波动
            "trend": 0.003,       # 0.3%日趋势
            "days": 30
        },
        {
            "name": "高波动数据",
            "volatility": 0.025,  # 2.5%日波动
            "trend": 0.005,       # 0.5%日趋势
            "days": 30
        },
        {
            "name": "震荡市场数据",
            "volatility": 0.020,  # 2.0%日波动
            "trend": 0.0,         # 无趋势
            "days": 30
        },
        {
            "name": "强趋势数据",
            "volatility": 0.015,  # 1.5%日波动
            "trend": 0.008,       # 0.8%日趋势
            "days": 30
        }
    ]
    
    for scenario in test_scenarios:
        print(f"🔍 测试场景: {scenario['name']}")
        print("-" * 60)
        
        # 生成测试数据
        np.random.seed(42)  # 固定随机种子确保可重复
        base_price = 100.0
        days = scenario['days']
        volatility = scenario['volatility']
        trend = scenario['trend']
        
        # 生成价格序列
        trend_factor = np.linspace(0, trend * days, days)
        noise = np.random.normal(0, volatility, days)
        closes = base_price * (1 + trend_factor + noise)
        highs = closes * (1 + np.random.uniform(0.002, 0.008, days))
        lows = closes * (1 - np.random.uniform(0.002, 0.008, days))
        
        print(f"   📊 数据特征:")
        print(f"     起始价格: {closes[0]:.3f}")
        print(f"     结束价格: {closes[-1]:.3f}")
        print(f"     总变化: {(closes[-1] / closes[0] - 1) * 100:.2f}%")
        print(f"     设定波动率: {volatility * 100:.1f}%")
        print(f"     设定趋势: {trend * 100:.1f}%/日")
        print()
        
        # 测试统一21日ATR计算
        print(f"   🎯 统一21日ATR计算结果:")
        
        # 固定参数
        ATR_PERIOD = 21
        base_multiplier = 2.0
        market_mode = "统一21日ATR"
        
        print(f"     ATR周期: {ATR_PERIOD}日")
        print(f"     基础倍数: {base_multiplier:.1f}")
        print(f"     市场模式: {market_mode}")
        
        # 计算ATR
        current_atr = simple_atr_calculation(highs, lows, closes, ATR_PERIOD)
        current_price = closes[-1]
        atr_percentage = (current_atr / current_price * 100) if current_price > 0 else 0.05
        
        print(f"     ATR值: {current_atr:.4f}")
        print(f"     ATR百分比: {atr_percentage:.3f}%")
        
        # 计算移动止盈参数
        止损距离 = atr_percentage * base_multiplier
        初始止盈距离 = atr_percentage * base_multiplier
        标准移动触发距离 = 初始止盈距离
        加速移动触发距离 = atr_percentage * 1.5
        最小移动幅度 = 0.1
        
        # 合理性检查
        最小距离 = 0.15
        最大距离 = 5.0
        
        止损距离 = max(min(止损距离, 最大距离), 最小距离)
        初始止盈距离 = max(min(初始止盈距离, 最大距离), 最小距离)
        标准移动触发距离 = 初始止盈距离
        加速移动触发距离 = max(min(加速移动触发距离, 最大距离), 最小移动幅度)
        
        print(f"     止损距离: {止损距离:.3f}%")
        print(f"     初始止盈距离: {初始止盈距离:.3f}%")
        print(f"     标准移动触发: {标准移动触发距离:.3f}%")
        print(f"     加速移动触发: {加速移动触发距离:.3f}%")
        print(f"     最小移动幅度: {最小移动幅度:.1f}%")
        
        # 波动率区间判断
        if atr_percentage < 0.3:
            波动率区间 = '低波动区'
        elif atr_percentage < 0.8:
            波动率区间 = '正常波动区'
        elif atr_percentage < 1.5:
            波动率区间 = '高波动区'
        else:
            波动率区间 = '极高波动区'
        
        print(f"     波动率区间: {波动率区间}")
        print()

def test_atr_consistency():
    """测试ATR计算的一致性"""
    print("=" * 80)
    print("🔧 ATR计算一致性测试")
    print("=" * 80)
    
    # 创建标准测试数据
    np.random.seed(123)
    base_price = 100.0
    days = 50
    
    # 生成价格序列
    trend_factor = np.linspace(0, 0.1, days)  # 10%上涨
    noise = np.random.normal(0, 0.015, days)  # 1.5%波动
    closes = base_price * (1 + trend_factor + noise)
    highs = closes * (1 + np.random.uniform(0.003, 0.010, days))
    lows = closes * (1 - np.random.uniform(0.003, 0.010, days))
    
    print(f"📊 标准测试数据:")
    print(f"   数据长度: {len(closes)}天")
    print(f"   起始价格: {closes[0]:.3f}")
    print(f"   结束价格: {closes[-1]:.3f}")
    print(f"   总涨幅: {(closes[-1] / closes[0] - 1) * 100:.2f}%")
    print()
    
    # 测试不同周期的ATR计算
    atr_periods = [14, 21, 28]
    
    print(f"🔍 不同周期ATR对比:")
    for period in atr_periods:
        atr_value = simple_atr_calculation(highs, lows, closes, period)
        atr_pct = (atr_value / closes[-1] * 100)
        
        # 计算对应的移动止盈参数
        if period == 21:
            multiplier = 2.0  # 统一使用的倍数
            status = "✅ 当前使用"
        elif period == 14:
            multiplier = 4.67  # 原14日方案倍数
            status = "❌ 已弃用"
        else:
            multiplier = 2.5   # 假设的28日倍数
            status = "⚪ 参考"
        
        stop_loss = atr_pct * multiplier
        take_profit = atr_pct * multiplier
        
        print(f"   {period}日ATR: {atr_value:.4f} ({atr_pct:.3f}%) {status}")
        print(f"     倍数: {multiplier:.2f} | 止损: {stop_loss:.3f}% | 止盈: {take_profit:.3f}%")
        print()
    
    # 验证21日ATR的稳定性
    print(f"🎯 21日ATR稳定性验证:")
    
    # 测试不同数据长度
    data_lengths = [25, 30, 40, 50]
    
    for length in data_lengths:
        if length <= len(closes):
            test_highs = highs[:length]
            test_lows = lows[:length]
            test_closes = closes[:length]
            
            atr_21 = simple_atr_calculation(test_highs, test_lows, test_closes, 21)
            atr_pct_21 = (atr_21 / test_closes[-1] * 100)
            
            print(f"   数据长度{length}天: ATR={atr_21:.4f} ({atr_pct_21:.3f}%)")
    
    print()

if __name__ == "__main__":
    try:
        test_unified_21day_atr()
        test_atr_consistency()
        
        print("=" * 80)
        print("✅ 统一21日ATR测试完成")
        print("=" * 80)
        print()
        print("📋 修改总结:")
        print("1. ✅ 移除趋势和震荡市场判断逻辑")
        print("2. ✅ 统一使用21日ATR计算")
        print("3. ✅ 统一使用2.0倍ATR倍数")
        print("4. ✅ 保留加速移动机制和最小移动限制")
        print("5. ✅ 简化市场模式为'统一21日ATR'")
        print("6. ✅ 移除趋势强度相关计算和显示")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
