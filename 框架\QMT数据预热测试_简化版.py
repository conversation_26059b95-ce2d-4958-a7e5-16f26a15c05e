#coding:gbk
"""
QMT数据预热测试策略 - 简化版
专门用于QMT策略环境，修复API调用问题

主要功能:
1. 下载历史数据到本地缓存
2. 获取并验证历史数据
3. 测试实时数据接收
4. 验证ATR计算数据

基于官方示例: 示例文档\获取历史数据

作者: QMT策略开发
版本: 3.0.0 (简化版)
日期: 2024-12-19
"""

import numpy as np
from datetime import datetime
import time

# ============================================================================
# QMT策略标准接口
# ============================================================================

def init(ContextInfo):
    """
    策略初始化函数 - 下载历史数据
    """
    print("🚀 QMT数据预热测试策略启动 (简化版)")
    print("="*60)
    
    # 获取股票代码（使用QMT标准方式）
    ContextInfo.stock = ContextInfo.stockcode + '.' + ContextInfo.market
    print(f"📊 测试股票: {ContextInfo.stock}")
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 下载历史数据
    print(f"\n📥 开始下载历史数据...")
    
    try:
        # 下载日线数据
        download_history_data(ContextInfo.stock, "1d", "20230101", "")
        print(f"✅ 日线数据下载完成")
        
        # 下载分钟数据
        download_history_data(ContextInfo.stock, "1m", "20241201", "")
        print(f"✅ 分钟数据下载完成")
        
    except Exception as e:
        print(f"⚠️ 历史数据下载异常: {e}")
        print(f"💡 提示: 请检查网络连接和股票代码")
    
    # 等待下载完成
    print(f"\n⏳ 等待数据下载完成...")
    time.sleep(10)
    
    # 初始化测试计数器
    ContextInfo.test_counter = 0
    
    print(f"\n💡 数据下载完成，等待handlebar()函数进行数据测试...")
    print("="*60)

def handlebar(ContextInfo):
    """
    K线数据处理函数 - 执行数据测试
    """
    # 每20根K线执行一次完整测试
    ContextInfo.test_counter = getattr(ContextInfo, 'test_counter', 0) + 1

    # 每5根K线执行一次快速tick测试
    if ContextInfo.test_counter % 5 == 0:
        quick_tick_test(ContextInfo)

    if ContextInfo.test_counter % 20 == 1:  # 第1次和每20次执行测试
        print(f"\n🔍 执行数据预热测试 (第{ContextInfo.test_counter}根K线)")
        print("-" * 50)
        
        # 执行测试
        test1_result = test_historical_data(ContextInfo)
        test2_result = test_realtime_data(ContextInfo)
        test3_result = test_atr_calculation(ContextInfo)
        test4_result = test_tick_data(ContextInfo)

        # 测试结果总结
        print(f"\n📋 测试结果总结:")
        print(f"✅ 历史数据测试: {'通过' if test1_result else '失败'}")
        print(f"✅ 实时数据测试: {'通过' if test2_result else '失败'}")
        print(f"✅ ATR计算测试: {'通过' if test3_result else '失败'}")
        print(f"✅ Tick数据测试: {'通过' if test4_result else '失败'}")

        if all([test1_result, test2_result, test3_result, test4_result]):
            print(f"🎉 所有测试通过，数据预热成功！")
        else:
            print(f"⚠️ 部分测试失败，请检查数据连接")
        
        print("-" * 50)

# ============================================================================
# 测试函数
# ============================================================================

def test_historical_data(ContextInfo):
    """
    测试历史数据获取
    """
    try:
        print(f"📈 测试历史数据获取: {ContextInfo.stock}")
        
        # 使用QMT官方API获取历史数据（基于官方示例）
        hist_data = ContextInfo.get_market_data_ex(
            [],  # 空列表表示获取所有字段
            [ContextInfo.stock],  # 证券代码列表
            period='1d',
            count=30,
            dividend_type='back_ratio'  # 后复权
        )
        
        if hist_data is not None and ContextInfo.stock in hist_data:
            data_df = hist_data[ContextInfo.stock]
            data_count = len(data_df)
            
            if data_count >= 20:
                # 获取最新数据
                latest_data = data_df.iloc[-1]
                latest_close = latest_data.get('close', 0)
                latest_volume = latest_data.get('volume', 0)
                
                print(f"  ✅ 获取到 {data_count} 根K线数据")
                print(f"  📊 最新价格: {latest_close:.3f}")
                print(f"  📊 最新成交量: {latest_volume:.0f}")
                return True
            else:
                print(f"  ❌ 数据不足: 仅获取到 {data_count} 根K线")
                return False
        else:
            print(f"  ❌ 未获取到数据")
            return False
            
    except Exception as e:
        print(f"  ❌ 历史数据测试异常: {e}")
        return False

def test_realtime_data(ContextInfo):
    """
    测试实时数据获取
    """
    try:
        print(f"📡 测试实时数据获取: {ContextInfo.stock}")
        
        # 获取最新的实时数据
        realtime_data = ContextInfo.get_market_data_ex(
            ['open', 'high', 'low', 'close', 'volume'],  # 字段列表
            [ContextInfo.stock],  # 证券代码列表
            period='1d',
            count=1,
            dividend_type='none'
        )
        
        if realtime_data is not None and ContextInfo.stock in realtime_data:
            data_df = realtime_data[ContextInfo.stock]
            
            if len(data_df) > 0:
                latest_data = data_df.iloc[-1]
                current_price = latest_data['close']
                current_volume = latest_data['volume']
                
                print(f"  ✅ 实时数据获取成功")
                print(f"  📊 当前价格: {current_price:.3f}")
                print(f"  📊 当前成交量: {current_volume:.0f}")
                
                # 检查数据合理性
                if current_price > 0 and current_volume >= 0:
                    return True
                else:
                    print(f"  ❌ 数据异常: 价格={current_price}, 成交量={current_volume}")
                    return False
            else:
                print(f"  ❌ 实时数据为空")
                return False
        else:
            print(f"  ❌ 未获取到实时数据")
            return False
            
    except Exception as e:
        print(f"  ❌ 实时数据测试异常: {e}")
        return False

def test_atr_calculation(ContextInfo):
    """
    测试ATR计算所需数据
    """
    try:
        print(f"📊 测试ATR计算数据: {ContextInfo.stock}")
        
        # 获取ATR计算所需的数据（至少14根K线）
        atr_data = ContextInfo.get_market_data_ex(
            ['high', 'low', 'close'],  # 字段列表
            [ContextInfo.stock],       # 证券代码列表
            period='1d',
            count=20,
            dividend_type='none'
        )
        
        if atr_data is not None and ContextInfo.stock in atr_data:
            data_df = atr_data[ContextInfo.stock]
            
            if len(data_df) >= 14:
                # 计算ATR
                highs = data_df['high'].values
                lows = data_df['low'].values
                closes = data_df['close'].values
                
                # 计算真实波幅
                tr_list = []
                for i in range(1, len(highs)):
                    tr1 = highs[i] - lows[i]
                    tr2 = abs(highs[i] - closes[i-1])
                    tr3 = abs(lows[i] - closes[i-1])
                    tr_list.append(max(tr1, tr2, tr3))
                
                if len(tr_list) >= 14:
                    atr = np.mean(tr_list[-14:])
                    trigger_threshold = atr * 4.0
                    stop_loss_threshold = atr * 3.0
                    
                    print(f"  ✅ ATR计算成功")
                    print(f"  📊 ATR值: {atr:.4f}")
                    print(f"  📊 触发阈值: {trigger_threshold:.4f}")
                    print(f"  📊 止损阈值: {stop_loss_threshold:.4f}")
                    return True
                else:
                    print(f"  ❌ TR数据不足: {len(tr_list)} < 14")
                    return False
            else:
                print(f"  ❌ K线数据不足: {len(data_df)} < 14")
                return False
        else:
            print(f"  ❌ 未获取到ATR数据")
            return False
            
    except Exception as e:
        print(f"  ❌ ATR计算测试异常: {e}")
        return False

def test_tick_data(ContextInfo):
    """
    测试Tick数据获取
    """
    try:
        print(f"⚡ 测试Tick数据获取: {ContextInfo.stock}")

        # 方法1: 使用get_market_data_ex获取分笔数据
        try:
            tick_data = ContextInfo.get_market_data_ex(
                ['time', 'lastPrice', 'volume', 'amount'],  # tick数据字段
                [ContextInfo.stock],                         # 证券代码列表
                period='tick',                               # tick周期
                count=100,                                   # 获取最近100笔
                dividend_type='none'
            )

            if tick_data is not None and ContextInfo.stock in tick_data:
                data_df = tick_data[ContextInfo.stock]
                tick_count = len(data_df)

                if tick_count > 0:
                    print(f"  ✅ 方法1成功: 获取到 {tick_count} 笔tick数据")

                    # 显示最新几笔数据
                    latest_ticks = data_df.tail(3)
                    for _, tick in latest_ticks.iterrows():
                        price = tick.get('lastPrice', 0)
                        volume = tick.get('volume', 0)
                        amount = tick.get('amount', 0)
                        tick_time = tick.get('time', '')
                        print(f"    📊 {tick_time}: 价格={price:.3f}, 量={volume}, 额={amount:.0f}")

                    return True
                else:
                    print(f"  ❌ 方法1: tick数据为空")
            else:
                print(f"  ❌ 方法1: 未获取到tick数据")

        except Exception as e:
            print(f"  ❌ 方法1失败: {e}")

        # 方法2: 使用get_full_tick获取完整tick数据
        try:
            print(f"  🔄 尝试方法2: get_full_tick")

            # 注意：get_full_tick可能需要不同的调用方式
            full_tick_data = ContextInfo.get_full_tick([ContextInfo.stock])

            if full_tick_data is not None:
                print(f"  ✅ 方法2成功: get_full_tick")
                print(f"  📊 数据类型: {type(full_tick_data)}")
                print(f"  📊 数据长度: {len(full_tick_data) if hasattr(full_tick_data, '__len__') else 'N/A'}")
                return True
            else:
                print(f"  ❌ 方法2: get_full_tick返回空数据")

        except Exception as e:
            print(f"  ❌ 方法2失败: {e}")

        return False

    except Exception as e:
        print(f"  ❌ Tick数据测试异常: {e}")
        return False

# ============================================================================
# 简化测试函数（用于handlebar中的快速验证）
# ============================================================================

def simple_data_test(ContextInfo):
    """
    简化数据测试 - 在handlebar中调用
    """
    try:
        # 获取当前价格
        current_data = ContextInfo.get_market_data_ex(
            ['close', 'volume'],  # 字段列表
            [ContextInfo.stock],  # 证券代码列表
            period='1d',
            count=1,
            dividend_type='none'
        )
        
        if current_data is not None and ContextInfo.stock in current_data:
            data_df = current_data[ContextInfo.stock]
            if len(data_df) > 0:
                latest = data_df.iloc[-1]
                price = latest['close']
                volume = latest['volume']
                
                print(f"📊 简化数据测试")
                print(f"  股票代码: {ContextInfo.stock}")
                print(f"  当前价格: {price:.3f}")
                print(f"  当前成交量: {volume:.0f}")
                
                if price > 0:
                    print(f"  ✅ 数据获取正常")
                    return True
                else:
                    print(f"  ❌ 价格数据异常")
                    return False
            else:
                print(f"  ❌ 数据为空")
                return False
        else:
            print(f"  ❌ 数据获取失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 数据获取异常: {e}")
        return False

def quick_tick_test(ContextInfo):
    """
    快速tick数据测试 - 用于handlebar中的轻量级测试
    """
    try:
        # 获取最新的几笔tick数据
        tick_data = ContextInfo.get_market_data_ex(
            ['lastPrice', 'volume'],  # 简化字段
            [ContextInfo.stock],
            period='tick',
            count=5,                  # 只获取最新5笔
            dividend_type='none'
        )

        if tick_data is not None and ContextInfo.stock in tick_data:
            data_df = tick_data[ContextInfo.stock]
            if len(data_df) > 0:
                latest_tick = data_df.iloc[-1]
                price = latest_tick.get('lastPrice', 0)
                volume = latest_tick.get('volume', 0)

                print(f"⚡ 快速Tick测试: 最新价格={price:.3f}, 量={volume}")
                return True

        print(f"⚡ 快速Tick测试: 无数据")
        return False

    except Exception as e:
        print(f"⚡ 快速Tick测试异常: {e}")
        return False

# ============================================================================
# 策略信息
# ============================================================================

print("📄 QMT数据预热测试策略已加载 (简化版)")
print("🔧 修复API调用问题，专用于QMT策略环境")
print("📅 版本: 3.0.0 (2024-12-19)")
