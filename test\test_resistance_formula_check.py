#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查阻力线公式的实际计算结果
"""

def test_resistance_formula():
    """测试阻力线公式的实际效果"""
    try:
        print("=== 检查阻力线公式计算结果 ===")
        
        import numpy as np
        
        # 测试真实的股票价格数据
        print("📊 测试场景1: 正常股票价格")
        
        # 模拟真实股票数据（价格在10-11元之间）
        high = 10.80
        low = 10.20
        close = 10.50
        
        print(f"K线数据: 高{high}, 低{low}, 收{close}")
        
        # 按通达信公式计算阻力线
        K线加权均值 = (high + low + 2 * close) / 4
        阻力线 = K线加权均值 + (K线加权均值 - low)
        
        print(f"加权均值: {K线加权均值:.3f}")
        print(f"阻力线: {阻力线:.3f}")
        print(f"收盘价: {close}")
        print(f"阻力线溢价: {((阻力线 - close) / close * 100):.2f}%")
        
        # 分析公式
        print(f"\n🔍 公式分析:")
        print(f"K线加权均值 = (HIGH + LOW + 2*CLOSE) / 4")
        print(f"             = ({high} + {low} + 2*{close}) / 4")
        print(f"             = {K线加权均值:.3f}")
        print(f"")
        print(f"阻力线 = K线加权均值 + (K线加权均值 - LOW)")
        print(f"       = {K线加权均值:.3f} + ({K线加权均值:.3f} - {low})")
        print(f"       = {K线加权均值:.3f} + {K线加权均值 - low:.3f}")
        print(f"       = {阻力线:.3f}")
        
        # 简化公式
        print(f"\n📐 简化分析:")
        print(f"阻力线 = 2*K线加权均值 - LOW")
        print(f"       = 2*{K线加权均值:.3f} - {low}")
        print(f"       = {2 * K线加权均值:.3f} - {low}")
        print(f"       = {2 * K线加权均值 - low:.3f}")
        
        # 进一步简化
        print(f"\n🧮 完全展开:")
        print(f"阻力线 = 2*(HIGH + LOW + 2*CLOSE)/4 - LOW")
        print(f"       = (HIGH + LOW + 2*CLOSE)/2 - LOW")
        print(f"       = (HIGH + LOW + 2*CLOSE - 2*LOW)/2")
        print(f"       = (HIGH - LOW + 2*CLOSE)/2")
        print(f"       = ({high} - {low} + 2*{close})/2")
        print(f"       = ({high - low + 2*close})/2")
        print(f"       = {(high - low + 2*close)/2:.3f}")
        
        # 验证
        简化阻力线 = (high - low + 2*close) / 2
        print(f"\n✅ 验证: 简化公式结果 {简化阻力线:.3f} = 原公式结果 {阻力线:.3f}")
        
        # 分析阻力线的特点
        print(f"\n📊 阻力线特点分析:")
        print(f"当收盘价 = 最高价时:")
        close_high = high
        阻力线_高 = (high - low + 2*close_high) / 2
        print(f"   阻力线 = ({high} - {low} + 2*{close_high})/2 = {阻力线_高:.3f}")
        print(f"   突破难度: {((阻力线_高 - close_high) / close_high * 100):.2f}%")
        
        print(f"\n当收盘价 = 最低价时:")
        close_low = low
        阻力线_低 = (high - low + 2*close_low) / 2
        print(f"   阻力线 = ({high} - {low} + 2*{close_low})/2 = {阻力线_低:.3f}")
        print(f"   突破难度: {((阻力线_低 - close_low) / close_low * 100):.2f}%")
        
        print(f"\n当收盘价 = 中位价时:")
        close_mid = (high + low) / 2
        阻力线_中 = (high - low + 2*close_mid) / 2
        print(f"   阻力线 = ({high} - {low} + 2*{close_mid})/2 = {阻力线_中:.3f}")
        print(f"   突破难度: {((阻力线_中 - close_mid) / close_mid * 100):.2f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_breakthrough_scenarios():
    """测试各种突破场景"""
    try:
        print("\n=== 测试突破场景 ===")
        
        import numpy as np
        
        scenarios = [
            {"name": "小幅上涨", "high": 10.50, "low": 10.00, "close": 10.30},
            {"name": "大幅上涨", "high": 11.00, "low": 10.00, "close": 10.80},
            {"name": "涨停板", "high": 11.00, "low": 10.00, "close": 11.00},
            {"name": "下跌反弹", "high": 10.20, "low": 9.80, "close": 10.10},
            {"name": "震荡整理", "high": 10.30, "low": 10.10, "close": 10.20},
        ]
        
        print("📊 各种K线形态的阻力线突破分析:")
        print("=" * 80)
        print(f"{'场景':<10} {'高':<6} {'低':<6} {'收':<6} {'阻力线':<8} {'溢价%':<8} {'能否突破'}")
        print("=" * 80)
        
        for scenario in scenarios:
            high = scenario["high"]
            low = scenario["low"]
            close = scenario["close"]
            
            # 计算阻力线
            阻力线 = (high - low + 2*close) / 2
            溢价率 = (阻力线 - close) / close * 100
            能否突破 = "✅" if close > 阻力线 else "❌"
            
            print(f"{scenario['name']:<10} {high:<6} {low:<6} {close:<6} {阻力线:<8.3f} {溢价率:<8.2f} {能否突破}")
        
        print("=" * 80)
        
        # 分析结论
        print("\n📋 分析结论:")
        print("1. 阻力线公式: (HIGH - LOW + 2*CLOSE) / 2")
        print("2. 当收盘价接近最高价时，阻力线约等于最高价")
        print("3. 当收盘价接近最低价时，阻力线显著高于收盘价")
        print("4. 要突破阻力线，通常需要下一根K线的收盘价超过当前最高价")
        
        # 测试连续K线的突破
        print("\n📊 测试连续K线突破:")
        
        # 第一根K线
        k1_high, k1_low, k1_close = 10.50, 10.00, 10.30
        k1_阻力线 = (k1_high - k1_low + 2*k1_close) / 2
        
        print(f"第1根K线: 高{k1_high}, 低{k1_low}, 收{k1_close}")
        print(f"第1根阻力线: {k1_阻力线:.3f}")
        
        # 第二根K线（突破）
        k2_high, k2_low, k2_close = 10.80, 10.20, 10.60
        k2_突破 = k2_close > k1_阻力线
        
        print(f"第2根K线: 高{k2_high}, 低{k2_low}, 收{k2_close}")
        print(f"突破第1根阻力线: {'✅' if k2_突破 else '❌'} ({k2_close} > {k1_阻力线:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_adx_data_requirement():
    """测试ADX数据需求"""
    try:
        print("\n=== 测试ADX数据需求 ===")
        
        import importlib.util
        import numpy as np
        
        # 加载策略文件
        spec = importlib.util.spec_from_file_location("strategy", "框架/6sk线.py")
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        
        # 创建检测器
        detector = strategy_module.CMFBIASDivergenceDetector(ADX_N=14, ADX_M=7)
        
        print(f"📊 ADX参数: ADX_N={detector.ADX_N}, ADX_M={detector.ADX_M}")
        
        # 测试不同数据量
        data_counts = [10, 15, 20, 25, 30]
        
        for count in data_counts:
            print(f"\n📊 测试{count}根K线数据:")
            
            # 生成测试数据
            highs = np.array([10.0 + i * 0.1 + 0.05 for i in range(count)])
            lows = np.array([10.0 + i * 0.1 - 0.05 for i in range(count)])
            closes = np.array([10.0 + i * 0.1 for i in range(count)])
            
            # 计算ADX
            adx_result = detector.calculate_ADX(highs, lows, closes)
            
            print(f"   ADX结果长度: {len(adx_result)}")
            print(f"   ADX非零值数量: {np.count_nonzero(adx_result)}")
            print(f"   ADX最大值: {adx_result.max():.2f}")
            print(f"   ADX最后值: {adx_result[-1]:.2f}")
            
            if adx_result[-1] > 0:
                print(f"   ✅ ADX计算正常")
            else:
                print(f"   ❌ ADX为0，可能数据不足")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 开始阻力线公式和ADX数据需求检查...")
    
    success_count = 0
    total_tests = 3
    
    if test_resistance_formula():
        success_count += 1
    
    if test_breakthrough_scenarios():
        success_count += 1
    
    if test_adx_data_requirement():
        success_count += 1
    
    print(f"\n📊 检查结果: {success_count}/{total_tests} 完成")
    
    if success_count == total_tests:
        print("\n📋 检查总结:")
        print("1. ✅ 阻力线公式正确: (HIGH - LOW + 2*CLOSE) / 2")
        print("2. ✅ 阻力线特点: 通常需要下根K线收盘价超过当前最高价才能突破")
        print("3. ✅ ADX数据需求: 至少需要ADX_N+1根K线开始计算")
        print("4. 📊 问题可能在于: 阻力线设置相对较高，需要较强的上涨才能突破")
    else:
        print("❌ 部分检查失败，需要进一步分析")
