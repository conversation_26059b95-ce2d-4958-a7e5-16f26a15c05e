# QMT API修复说明

基于QMT官方文档 `/context7/dict_thinktrader_net` 的关键修复点

## 🔧 主要修复内容

### 1. **passorder函数返回值处理**

**问题**：之前错误地认为`passorder`返回订单ID
**修复**：根据官方文档，`passorder`返回`None`

```python
# ❌ 错误的理解
if order_result and str(order_result) not in ['0', '-1']:
    print(f"订单ID: {order_result}")

# ✅ 正确的处理方式
order_result = passorder(...)  # 返回None
# 需要通过get_last_order_id获取订单ID
latest_order_id = get_last_order_id(account, 'stock', 'order', strategy_name)
```

### 2. **委托方向代码识别**

**问题**：使用了错误的字段和代码映射
**修复**：使用正确的`m_nOffsetFlag`字段

```python
# ✅ 正确的方向识别
order_op_type = getattr(order_obj, 'm_nOffsetFlag', -1)
direction_field = getattr(order_obj, 'm_nDirection', -1)

# 根据官方文档的EOffset_Flag_Type枚举
if order_op_type == 23:
    direction = '买入'
elif order_op_type == 24:
    direction = '卖出'
else:
    direction = f'未知({order_op_type})'
```

### 3. **QMT内置函数调用**

**问题**：尝试导入外部模块
**修复**：直接使用QMT内置函数

```python
# ❌ 错误的导入
from xtquant.xttrader import get_last_order_id

# ✅ 正确的使用（QMT内置函数）
latest_order_id = get_last_order_id(account, 'stock', 'order')
account_info = get_trade_detail_data(account, 'stock', 'account')
cancel_result = cancel(order_id, account, 'STOCK', ContextInfo)
```

### 4. **账户类型参数**

**问题**：账户类型参数不一致
**修复**：统一使用正确的账户类型

```python
# ✅ 正确的账户类型
account_types = {
    'STOCK': '股票账户',
    'CREDIT': '信用账户', 
    'FUTURE': '期货账户',
    'HUGANGTONG': '沪港通',
    'SHENGANGTONG': '深港通'
}

# 函数调用时使用大写
cancel(order_id, account_id, 'STOCK', ContextInfo)
get_trade_detail_data(account_id, 'stock', 'order')  # 这里用小写
```

## 📋 关键API函数说明

### passorder - 下单函数
```python
passorder(
    opType,          # 23=买入, 24=卖出
    orderType,       # 1101=限价单, 1102=市价单
    accountID,       # 账户ID
    orderCode,       # 股票代码
    prType,          # 11=限价, 5=最新价
    price,           # 价格
    volume,          # 数量
    strategyName,    # 策略名称
    quickTrade,      # 1=快速下单
    userOrderId,     # 用户订单ID
    ContextInfo      # 上下文对象
)
# 返回: None (不返回订单ID)
```

### get_last_order_id - 获取最新订单ID
```python
order_id = get_last_order_id(
    accountID,       # 账户ID
    accountType,     # 'stock', 'future'等
    dataType,        # 'order' 或 'deal'
    strategyName     # 可选：策略名称过滤
)
# 返回: 订单ID字符串，失败返回'-1'
```

### get_trade_detail_data - 获取交易数据
```python
data = get_trade_detail_data(
    accountID,       # 账户ID
    accountType,     # 'stock', 'future'等
    dataType         # 'account', 'position', 'order', 'deal'
)
# 返回: 对象列表
```

### cancel - 撤销订单
```python
result = cancel(
    orderId,         # 订单ID
    accountId,       # 账户ID
    accountType,     # 'STOCK', 'FUTURE'等（大写）
    ContextInfo      # 上下文对象
)
# 返回: bool，True=撤单信号发送成功
```

## 🔍 委托对象属性

根据官方文档，Order对象的关键属性：

```python
# 基本信息
m_strOrderSysID      # 委托号（订单ID）
m_strInstrumentID    # 股票代码
m_strInstrumentName  # 股票名称

# 方向和类型
m_nDirection         # 买卖方向（股票始终是48）
m_nOffsetFlag        # 操作类型（23=买入, 24=卖出）
m_nOrderPriceType    # 订单价格类型

# 价格和数量
m_dPrice            # 委托价格
m_nVolume           # 委托数量
m_nVolumeTraded     # 已成交数量

# 状态
m_eOrderStatus      # 订单状态
m_strOptName        # 买卖标记（中文描述）
```

## ⚠️ 重要注意事项

1. **异步特性**：QMT下单是异步的，`passorder`立即返回，不等待反馈
2. **延迟获取**：订单ID需要延迟获取，建议等待0.5-1秒
3. **字段差异**：不同版本的QMT可能字段名略有差异
4. **账户变量**：`account`和`accountType`是QMT运行时自动传递的全局变量
5. **内置函数**：所有交易相关函数都是QMT内置的，无需导入

## 🧪 测试建议

1. 使用 `QMT_API测试验证.py` 验证API调用
2. 先测试获取账户信息和委托信息
3. 小量测试下单功能
4. 验证委托方向识别的准确性
5. 测试撤单功能的可靠性

## 📚 参考文档

- QMT官方API文档：`/context7/dict_thinktrader_net`
- 枚举常量：`enum_constants.html`
- 代码示例：`code_examples.html`
