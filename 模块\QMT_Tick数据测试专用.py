#coding:gbk
"""
QMT Tick数据获取测试专用文件
=============================

目的：专门测试和诊断QMT tick数据获取问题
功能：
1. 测试历史tick数据获取
2. 测试实时tick数据获取  
3. 分析数据字段结构
4. 对比不同获取方式的差异

作者: QMT策略开发
版本: 1.0.0
日期: 2025-08-01
"""

import datetime
import time
import pandas as pd

# ============================================================================
# 全局配置
# ============================================================================

# 测试配置
TEST_CONFIG = {
    'test_stock': '',  # 将在init中设置
    'test_count': 50,  # 测试数据量
    'debug_mode': True,  # 调试模式
    'test_both': True,  # 同时测试历史和实时数据
}

# ============================================================================
# QMT策略标准接口
# ============================================================================

def init(ContextInfo):
    """
    策略初始化函数 - 设置测试参数
    """
    print("🚀 QMT Tick数据获取测试启动")
    print("="*80)
    
    # 设置测试股票
    TEST_CONFIG['test_stock'] = ContextInfo.stockcode + '.' + ContextInfo.market
    
    print(f"📊 测试股票: {TEST_CONFIG['test_stock']}")
    print(f"📊 测试数据量: {TEST_CONFIG['test_count']}")
    print(f"🕐 测试时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 初始化测试计数器
    ContextInfo.test_counter = 0
    ContextInfo.historical_test_done = False
    
    print(f"\n💡 初始化完成，等待handlebar进行数据测试...")
    print("="*80)

def handlebar(ContextInfo):
    """
    K线数据处理函数 - 执行tick数据测试
    """
    ContextInfo.test_counter = getattr(ContextInfo, 'test_counter', 0) + 1
    
    print(f"\n🔍 第{ContextInfo.test_counter}次测试执行")
    print("-" * 80)
    
    # 测试1: 历史tick数据获取（只执行一次）
    if not getattr(ContextInfo, 'historical_test_done', False):
        print(f"📈 开始历史tick数据测试...")
        test_historical_tick_data(ContextInfo)
        ContextInfo.historical_test_done = True
        print(f"✅ 历史tick数据测试完成\n")
    
    # 测试2: 实时tick数据获取（每次都执行）
    print(f"📡 开始实时tick数据测试...")
    test_realtime_tick_data(ContextInfo)
    print(f"✅ 实时tick数据测试完成")
    
    print("-" * 80)

# ============================================================================
# 历史tick数据测试函数
# ============================================================================

def test_historical_tick_data(ContextInfo):
    """
    测试历史tick数据获取
    """
    stock_code = TEST_CONFIG['test_stock']
    count = TEST_CONFIG['test_count']
    
    print(f"  📦 测试股票: {stock_code}")
    print(f"  📊 请求数量: {count}")
    
    # 计算时间范围
    now = datetime.datetime.now()
    end_date = now.strftime('%Y%m%d')
    start_date = (now - datetime.timedelta(days=3)).strftime('%Y%m%d')
    
    print(f"  📅 时间范围: {start_date} ~ {end_date}")
    
    # 测试方法1: 使用时间范围获取
    print(f"\n  🔬 方法1: get_market_data_ex + 时间范围")
    try:
        hist_data_1 = ContextInfo.get_market_data_ex(
            [],  # 获取所有字段
            [stock_code],
            period='tick',
            start_time=start_date,
            end_time=end_date,
            dividend_type='none'
        )
        
        analyze_tick_data(hist_data_1, stock_code, "方法1(时间范围)")
        
    except Exception as e:
        print(f"    ❌ 方法1失败: {e}")
    
    # 测试方法2: 使用count获取
    print(f"\n  🔬 方法2: get_market_data_ex + count")
    try:
        hist_data_2 = ContextInfo.get_market_data_ex(
            [],  # 获取所有字段
            [stock_code],
            period='tick',
            count=count,
            dividend_type='none'
        )
        
        analyze_tick_data(hist_data_2, stock_code, "方法2(count)")
        
    except Exception as e:
        print(f"    ❌ 方法2失败: {e}")
    
    # 测试方法3: 指定字段获取
    print(f"\n  🔬 方法3: get_market_data_ex + 指定字段")
    try:
        # 尝试常见的tick字段组合
        field_combinations = [
            ['lastPrice', 'volume', 'time'],
            ['last_price', 'volume', 'time'],
            ['price', 'volume', 'time'],
            ['close', 'volume', 'time'],
        ]
        
        for i, fields in enumerate(field_combinations):
            print(f"    🧪 字段组合{i+1}: {fields}")
            try:
                hist_data_3 = ContextInfo.get_market_data_ex(
                    fields,
                    [stock_code],
                    period='tick',
                    count=10,  # 少量数据测试
                    dividend_type='none'
                )
                
                if hist_data_3 and stock_code in hist_data_3:
                    print(f"      ✅ 字段组合{i+1}成功")
                    analyze_tick_data(hist_data_3, stock_code, f"方法3-字段{i+1}")
                    break
                else:
                    print(f"      ❌ 字段组合{i+1}失败: 无数据")
                    
            except Exception as e:
                print(f"      ❌ 字段组合{i+1}失败: {e}")
        
    except Exception as e:
        print(f"    ❌ 方法3失败: {e}")

# ============================================================================
# 实时tick数据测试函数
# ============================================================================

def test_realtime_tick_data(ContextInfo):
    """
    测试实时tick数据获取
    """
    stock_code = TEST_CONFIG['test_stock']
    
    print(f"  📦 测试股票: {stock_code}")
    
    # 测试方法1: get_market_data + 空列表
    print(f"\n  🔬 实时方法1: get_market_data + 空列表")
    try:
        realtime_data_1 = ContextInfo.get_market_data([], [stock_code])
        analyze_realtime_data(realtime_data_1, "实时方法1(空列表)")
        
    except Exception as e:
        print(f"    ❌ 实时方法1失败: {e}")
    
    # 测试方法2: get_market_data + 指定字段
    print(f"\n  🔬 实时方法2: get_market_data + 指定字段")
    try:
        # 尝试常见的字段组合
        field_combinations = [
            ['lastPrice', 'lastVolume'],
            ['last_price', 'last_volume'],
            ['price', 'volume'],
            ['close', 'volume'],
        ]
        
        for i, fields in enumerate(field_combinations):
            print(f"    🧪 字段组合{i+1}: {fields}")
            try:
                realtime_data_2 = ContextInfo.get_market_data(fields, [stock_code])
                
                if realtime_data_2 and len(realtime_data_2) > 0:
                    print(f"      ✅ 字段组合{i+1}成功")
                    analyze_realtime_data(realtime_data_2, f"实时方法2-字段{i+1}")
                    break
                else:
                    print(f"      ❌ 字段组合{i+1}失败: 无数据")
                    
            except Exception as e:
                print(f"      ❌ 字段组合{i+1}失败: {e}")
        
    except Exception as e:
        print(f"    ❌ 实时方法2失败: {e}")
    
    # 测试方法3: get_market_data_ex + 实时
    print(f"\n  🔬 实时方法3: get_market_data_ex + count=1")
    try:
        realtime_data_3 = ContextInfo.get_market_data_ex(
            [],
            [stock_code],
            period='tick',
            count=1,
            dividend_type='none'
        )
        
        if realtime_data_3 and stock_code in realtime_data_3:
            data_df = realtime_data_3[stock_code]
            if len(data_df) > 0:
                latest_data = data_df.iloc[-1]
                print(f"      ✅ 实时方法3成功")
                print(f"      📊 最新数据字段: {list(latest_data.keys())}")
                print(f"      📊 数据内容: {dict(latest_data)}")
            else:
                print(f"      ❌ 实时方法3失败: 数据为空")
        else:
            print(f"      ❌ 实时方法3失败: 无数据返回")
        
    except Exception as e:
        print(f"    ❌ 实时方法3失败: {e}")

# ============================================================================
# 数据分析函数
# ============================================================================

def analyze_tick_data(data, stock_code, method_name):
    """
    分析tick数据结构
    """
    print(f"    📊 {method_name} 数据分析:")
    
    if data is None:
        print(f"      ❌ 数据为None")
        return
    
    if stock_code not in data:
        print(f"      ❌ 股票代码不在数据中")
        print(f"      📊 可用代码: {list(data.keys())}")
        return
    
    data_df = data[stock_code]
    data_count = len(data_df)
    
    print(f"      ✅ 数据量: {data_count}")
    print(f"      📊 数据类型: {type(data_df)}")
    
    if hasattr(data_df, 'columns'):
        print(f"      📊 列名: {list(data_df.columns)}")
    
    if data_count > 0:
        first_row = data_df.iloc[0]
        print(f"      📊 第一行字段: {list(first_row.keys())}")
        print(f"      📊 第一行数据: {dict(first_row)}")
        
        # 尝试提取价格和成交量
        price_found = False
        volume_found = False
        
        for price_field in ['lastPrice', 'last_price', 'price', 'close', 'last']:
            if price_field in first_row and first_row[price_field] is not None:
                print(f"      💰 找到价格字段 '{price_field}': {first_row[price_field]}")
                price_found = True
                break
        
        for volume_field in ['volume', 'lastVolume', 'last_volume', 'vol']:
            if volume_field in first_row and first_row[volume_field] is not None:
                print(f"      📈 找到成交量字段 '{volume_field}': {first_row[volume_field]}")
                volume_found = True
                break
        
        if not price_found:
            print(f"      ⚠️ 未找到价格字段")
        if not volume_found:
            print(f"      ⚠️ 未找到成交量字段")

def analyze_realtime_data(data, method_name):
    """
    分析实时数据结构
    """
    print(f"    📊 {method_name} 数据分析:")
    
    if data is None:
        print(f"      ❌ 数据为None")
        return
    
    if len(data) == 0:
        print(f"      ❌ 数据为空列表")
        return
    
    first_item = data[0]
    print(f"      ✅ 数据量: {len(data)}")
    print(f"      📊 数据类型: {type(first_item)}")
    print(f"      📊 字段: {list(first_item.keys()) if hasattr(first_item, 'keys') else 'N/A'}")
    print(f"      📊 数据内容: {dict(first_item) if hasattr(first_item, 'keys') else first_item}")

# ============================================================================
# 策略信息
# ============================================================================

print("📄 QMT Tick数据获取测试专用文件已加载")
print("🔧 专门用于诊断tick数据获取问题")
print("📅 版本: 1.0.0 (2025-08-01)")
print("💡 使用方法: 在QMT中以tick周期运行此策略")
