# -*- coding: utf-8 -*-
"""
QMT历史数据预热测试策略 - 标准版
基于QMT官方示例文件的正确实现

参考文件: 示例文档\获取历史数据
主要功能:
1. 下载历史数据到本地缓存
2. 获取并验证历史数据
3. 测试ATR计算所需数据
4. 验证实时数据接收

使用方法:
1. 在QMT中新建策略
2. 复制此代码
3. 运行策略查看测试结果

作者: QMT策略开发
版本: 2.0.0 (基于官方示例)
日期: 2024-12-19
"""

import pandas as pd
import numpy as np
import time
import datetime

# ============================================================================
# QMT策略标准接口
# ============================================================================

def init(C):
    """
    历史数据获取初始化函数
    基于官方示例文件: 示例文档\获取历史数据
    """
    print("🚀 QMT历史数据预热测试策略启动 (标准版)")
    print("="*60)
    
    # === 第一步：设置数据下载参数 ===
    
    # 获取股票代码
    C.stock = C.stockcode + '.' + C.market
    print(f"📊 测试股票: {C.stock}")
    print(f"🕐 测试时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 设置下载参数（参考官方示例）
    start_date = '20230101'  # 从2023年开始下载
    end_date = ""            # 下载到最新
    period = "1d"            # 日线数据
    need_download = 1        # 需要下载
    
    code_list = [C.stock]    # 股票列表
    
    # === 第二步：执行数据下载 ===
    
    if need_download:
        print(f"\n📥 开始下载历史数据...")
        print(f"品种: {code_list}")
        print(f"周期: {period}")
        print(f"时间范围: {start_date} 到 {end_date if end_date else '最新'}")
        
        # 调用自定义下载函数（基于官方示例）
        my_download(code_list, period, start_date, end_date)
    
    # === 第三步：等待下载完成 ===
    
    print("⏳ 等待数据下载完成...")
    time.sleep(10)  # 等待10秒确保数据写入完成
    
    # === 第四步：获取和验证数据 ===
    
    print("\n🔍 开始数据验证...")
    
    # 使用QMT API获取历史数据（基于官方示例）
    data = C.get_market_data_ex(
        fields=[],  # 空列表表示获取所有字段
        stock_list=code_list,
        period=period,
        start_time=start_date,
        end_time=end_date,
        dividend_type="back_ratio"  # 后复权
    )
    
    # === 第五步：显示数据信息 ===
    
    print("\n=== 历史数据获取结果 ===")
    if data is not None and not data.empty:
        print(f"✅ 数据获取成功")
        
        for code in code_list:
            if code in data:
                df = data[code]
                print(f"\n📊 {code} 数据统计:")
                print(f"数据条数: {len(df)}")
                if len(df) > 0:
                    print(f"时间范围: {df.index[0]} 到 {df.index[-1]}")
                    print(f"数据字段: {list(df.columns)}")
                    
                    # 显示最新数据
                    latest = df.iloc[-1]
                    print(f"最新收盘价: {latest.get('close', 0):.3f}")
                    print(f"最新成交量: {latest.get('volume', 0):.0f}")
    else:
        print("❌ 未获取到有效数据，请检查代码和时间范围设置")
    
    # 显示合约详细信息
    print("\n=== 合约信息 ===")
    try:
        instrument_info = C.get_instrumentdetail(code_list[0])
        print(f"合约名称: {instrument_info.get('InstrumentName', 'N/A')}")
        print(f"交易所: {instrument_info.get('ExchangeID', 'N/A')}")
        print(f"最小变价单位: {instrument_info.get('PriceTick', 'N/A')}")
    except Exception as e:
        print(f"⚠️ 获取合约信息失败: {e}")
    
    print("="*60)
    print("✅ 数据预热测试完成")

def handlebar(C):
    """
    K线处理函数 - 执行实时数据测试
    """
    # 获取当前K线位置，避免重复测试
    current_bar = getattr(C, 'barpos', 0)
    last_test_bar = getattr(C, 'last_test_bar', -1)
    
    # 每20根K线执行一次测试
    if current_bar - last_test_bar >= 20:
        C.last_test_bar = current_bar
        
        print(f"\n🔍 执行实时数据测试 (K线位置: {current_bar})")
        print("-" * 40)
        
        # 执行实时数据测试
        test_realtime_data(C)
        test_atr_calculation(C)
        
        print("-" * 40)

# ============================================================================
# 数据下载函数（基于官方示例）
# ============================================================================

def my_download(stock_list, period, start_date='', end_date=''):
    """
    批量下载历史数据函数
    基于官方示例: 示例文档\获取历史数据
    """
    
    # === 数据周期标准化 ===
    if "d" in period:
        period = "1d"
        print(f"周期标准化: {period} → 1d (日线)")
    elif "m" in period:
        try:
            minute_num = int(period[0])
            if minute_num < 5:
                period = "1m"
                print(f"周期标准化: 分钟线 → 1m (1分钟基础)")
            else:
                period = "5m"
                print(f"周期标准化: 分钟线 → 5m (5分钟基础)")
        except ValueError:
            period = "1m"
            print(f"周期解析失败，默认使用: 1m")
    elif "tick" == period:
        print(f"使用tick数据（逐笔成交）")
    else:
        error_msg = f"不支持的数据周期: {period}"
        print(f"错误: {error_msg}")
        raise KeyboardInterrupt(error_msg)
    
    # === 批量下载处理 ===
    n = 1
    num = len(stock_list)
    
    print(f"\n开始批量下载，共{num}个品种")
    print(f"数据周期: {period}")
    print(f"时间范围: {start_date if start_date else '全量'} 到 {end_date if end_date else '最新'}")
    print("-" * 50)
    
    # 逐个下载证券数据
    for i in stock_list:
        print(f"正在下载 {n}/{num}: {i}")
        
        try:
            # 调用QMT底层下载函数
            download_history_data(i, period, start_date, end_date)
            print(f"✓ {i} 下载完成")
        except Exception as e:
            print(f"✗ {i} 下载失败: {e}")
        
        n += 1
    
    print("-" * 50)
    print("批量下载任务完成！")
    print(f"成功处理 {num} 个品种")
    print("请等待数据写入完成后再进行数据获取操作")

# ============================================================================
# 测试函数
# ============================================================================

def test_realtime_data(C):
    """
    测试实时数据获取
    """
    try:
        print(f"📡 测试实时数据获取: {C.stock}")
        
        # 获取最新数据
        current_data = C.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume'],
            stock_list=[C.stock],
            period='1d',
            count=1,
            dividend_type='none'
        )
        
        if current_data is not None and C.stock in current_data:
            df = current_data[C.stock]
            
            if len(df) > 0:
                latest = df.iloc[-1]
                current_price = latest['close']
                current_volume = latest['volume']
                
                print(f"  ✅ 实时数据获取成功")
                print(f"  📊 当前价格: {current_price:.3f}")
                print(f"  📊 当前成交量: {current_volume:.0f}")
                
                if current_price > 0 and current_volume >= 0:
                    return True
                else:
                    print(f"  ❌ 数据异常: 价格={current_price}, 成交量={current_volume}")
                    return False
            else:
                print(f"  ❌ 实时数据为空")
                return False
        else:
            print(f"  ❌ 未获取到实时数据")
            return False
            
    except Exception as e:
        print(f"  ❌ 实时数据测试异常: {e}")
        return False

def test_atr_calculation(C):
    """
    测试ATR计算所需数据
    """
    try:
        print(f"📊 测试ATR计算数据: {C.stock}")
        
        # 获取ATR计算所需的数据（至少14根K线）
        atr_data = C.get_market_data_ex(
            fields=['high', 'low', 'close'],
            stock_list=[C.stock],
            period='1d',
            count=20,
            dividend_type='none'
        )
        
        if atr_data is not None and C.stock in atr_data:
            df = atr_data[C.stock]
            
            if len(df) >= 14:
                # 计算ATR
                highs = df['high'].values
                lows = df['low'].values
                closes = df['close'].values
                
                # 计算真实波幅
                tr_list = []
                for i in range(1, len(highs)):
                    tr1 = highs[i] - lows[i]
                    tr2 = abs(highs[i] - closes[i-1])
                    tr3 = abs(lows[i] - closes[i-1])
                    tr_list.append(max(tr1, tr2, tr3))
                
                if len(tr_list) >= 14:
                    atr = np.mean(tr_list[-14:])
                    trigger_threshold = atr * 4.0
                    stop_loss_threshold = atr * 3.0
                    
                    print(f"  ✅ ATR计算成功")
                    print(f"  📊 ATR值: {atr:.4f}")
                    print(f"  📊 触发阈值: {trigger_threshold:.4f}")
                    print(f"  📊 止损阈值: {stop_loss_threshold:.4f}")
                    return True
                else:
                    print(f"  ❌ TR数据不足: {len(tr_list)} < 14")
                    return False
            else:
                print(f"  ❌ K线数据不足: {len(df)} < 14")
                return False
        else:
            print(f"  ❌ 未获取到ATR数据")
            return False
            
    except Exception as e:
        print(f"  ❌ ATR计算测试异常: {e}")
        return False

# ============================================================================
# 策略信息
# ============================================================================

print("📄 QMT历史数据预热测试策略已加载 (标准版)")
print("🔧 基于QMT官方示例文件实现")
print("📅 版本: 2.0.0 (2024-12-19)")
