#coding:gbk
"""
QMT兼容性测试模块
用于验证QMT环境的基本兼容性和属性可用性

主要功能：
1. 测试ContextInfo对象的属性
2. 验证股票代码获取方法
3. 测试账户ID获取
4. 验证数据获取API
"""

import datetime

def init(ContextInfo):
    """QMT策略初始化函数 - 兼容性测试"""
    print("🧪 QMT兼容性测试开始...")
    print("=" * 60)
    
    # 测试1: 检查ContextInfo对象的基本属性
    print("📊 测试1: ContextInfo对象属性检查")
    print("-" * 40)
    
    # 检查股票代码相关属性
    print("🔍 股票代码属性:")
    if hasattr(ContextInfo, 'stockcode'):
        print(f"  ✅ stockcode: {ContextInfo.stockcode}")
    else:
        print("  ❌ stockcode: 不存在")
    
    if hasattr(ContextInfo, 'market'):
        print(f"  ✅ market: {ContextInfo.market}")
    else:
        print("  ❌ market: 不存在")
    
    if hasattr(ContextInfo, 'stock'):
        print(f"  ✅ stock: {ContextInfo.stock}")
    else:
        print("  ❌ stock: 不存在")
    
    # 尝试构建完整股票代码
    try:
        if hasattr(ContextInfo, 'stockcode') and hasattr(ContextInfo, 'market'):
            full_stock_code = ContextInfo.stockcode + '.' + ContextInfo.market
            ContextInfo.stock = full_stock_code
            print(f"  ✅ 构建完整代码: {full_stock_code}")
        else:
            print("  ⚠️ 无法构建完整股票代码")
    except Exception as e:
        print(f"  ❌ 构建股票代码失败: {e}")
    
    print()
    
    # 检查账户相关属性
    print("🔍 账户属性:")
    account_attrs = ['accID', 'accountid', 'account_id', 'account']
    found_account = False
    
    for attr in account_attrs:
        if hasattr(ContextInfo, attr):
            try:
                value = getattr(ContextInfo, attr)
                print(f"  ✅ {attr}: {value}")
                found_account = True
            except Exception as e:
                print(f"  ⚠️ {attr}: 存在但获取失败 - {e}")
        else:
            print(f"  ❌ {attr}: 不存在")
    
    if not found_account:
        print("  ⚠️ 未找到任何账户ID属性")
    
    print()
    
    # 检查数据获取方法
    print("🔍 数据获取方法:")
    data_methods = [
        'get_market_data_ex',
        'get_trade_detail_data',
        'get_stock_list_in_file',
        'is_last_bar'
    ]
    
    for method in data_methods:
        if hasattr(ContextInfo, method):
            print(f"  ✅ {method}: 可用")
        else:
            print(f"  ❌ {method}: 不可用")
    
    print()
    
    # 检查其他常用属性
    print("🔍 其他属性:")
    other_attrs = [
        'period', 'close', 'open', 'high', 'low', 'volume',
        'stock_pool', 'barpos', 'datenum', 'timenum'
    ]
    
    for attr in other_attrs:
        if hasattr(ContextInfo, attr):
            try:
                value = getattr(ContextInfo, attr)
                if hasattr(value, '__len__') and not isinstance(value, str):
                    print(f"  ✅ {attr}: 数组/列表，长度={len(value)}")
                else:
                    print(f"  ✅ {attr}: {value}")
            except Exception as e:
                print(f"  ⚠️ {attr}: 存在但获取失败 - {e}")
        else:
            print(f"  ❌ {attr}: 不存在")
    
    print()
    print("=" * 60)
    print("✅ QMT兼容性测试初始化完成")

def handlebar(ContextInfo):
    """QMT策略K线处理函数 - 兼容性测试"""
    try:
        print("\n🧪 QMT兼容性测试 - K线处理")
        print("-" * 40)
        
        # 测试股票代码获取
        current_stock = None
        if hasattr(ContextInfo, 'stock') and ContextInfo.stock:
            current_stock = ContextInfo.stock
            print(f"📊 当前股票: {current_stock}")
        elif hasattr(ContextInfo, 'stockcode') and hasattr(ContextInfo, 'market'):
            current_stock = ContextInfo.stockcode + '.' + ContextInfo.market
            print(f"📊 构建股票代码: {current_stock}")
        else:
            print("⚠️ 无法获取股票代码")
            return
        
        # 测试价格数据获取
        print("📈 价格数据测试:")
        try:
            if hasattr(ContextInfo, 'close') and len(ContextInfo.close) > 0:
                current_price = ContextInfo.close[-1]
                print(f"  ✅ 收盘价: {current_price:.3f}")
            else:
                print("  ❌ 无法获取收盘价")
        except Exception as e:
            print(f"  ❌ 获取收盘价失败: {e}")
        
        try:
            if hasattr(ContextInfo, 'volume') and len(ContextInfo.volume) > 0:
                current_volume = ContextInfo.volume[-1]
                print(f"  ✅ 成交量: {current_volume}")
            else:
                print("  ❌ 无法获取成交量")
        except Exception as e:
            print(f"  ❌ 获取成交量失败: {e}")
        
        # 测试市场数据API
        print("📊 市场数据API测试:")
        if hasattr(ContextInfo, 'get_market_data_ex'):
            try:
                # 尝试获取实时数据
                market_data = ContextInfo.get_market_data_ex(
                    ['lastPrice', 'volume'],
                    [current_stock],
                    period=ContextInfo.period if hasattr(ContextInfo, 'period') else '1m',
                    count=1,
                    subscribe=False
                )
                if market_data and current_stock in market_data:
                    data = market_data[current_stock]
                    print(f"  ✅ API数据获取成功")
                    if 'lastPrice' in data and len(data['lastPrice']) > 0:
                        print(f"    最新价: {data['lastPrice'][-1]:.3f}")
                    if 'volume' in data and len(data['volume']) > 0:
                        print(f"    成交量: {data['volume'][-1]}")
                else:
                    print("  ⚠️ API返回空数据")
            except Exception as e:
                print(f"  ❌ API调用失败: {e}")
        else:
            print("  ❌ get_market_data_ex方法不可用")
        
        # 测试账户数据获取
        print("💰 账户数据测试:")
        account_id = None
        
        # 获取账户ID
        for attr in ['accID', 'accountid', 'account_id', 'account']:
            if hasattr(ContextInfo, attr):
                try:
                    account_id = getattr(ContextInfo, attr)
                    print(f"  ✅ 账户ID ({attr}): {account_id}")
                    break
                except:
                    continue
        
        if not account_id:
            account_id = "TEST_ACCOUNT"
            print(f"  ⚠️ 使用测试账户ID: {account_id}")
        
        # 测试交易数据获取
        if hasattr(ContextInfo, 'get_trade_detail_data'):
            try:
                # 测试账户信息获取
                account_info = ContextInfo.get_trade_detail_data(account_id, 'STOCK', 'ACCOUNT')
                if account_info and len(account_info) > 0:
                    print("  ✅ 账户信息获取成功")
                else:
                    print("  ⚠️ 账户信息为空")
                
                # 测试持仓信息获取
                positions = ContextInfo.get_trade_detail_data(account_id, 'STOCK', 'POSITION')
                if positions:
                    print(f"  ✅ 持仓信息获取成功，持仓数量: {len(positions)}")
                else:
                    print("  ⚠️ 无持仓信息")
                
            except Exception as e:
                print(f"  ❌ 交易数据获取失败: {e}")
        else:
            print("  ❌ get_trade_detail_data方法不可用")
        
        # 测试K线位置检查
        print("📍 K线位置测试:")
        if hasattr(ContextInfo, 'is_last_bar'):
            try:
                is_last = ContextInfo.is_last_bar()
                print(f"  ✅ 是否最新K线: {is_last}")
            except Exception as e:
                print(f"  ❌ is_last_bar调用失败: {e}")
        else:
            print("  ❌ is_last_bar方法不可用")
        
        print("-" * 40)
        print("✅ K线处理测试完成")
        
    except Exception as e:
        print(f"❌ K线处理测试异常: {e}")
        import traceback
        traceback.print_exc()

# 手动测试函数
def 测试初始化(ContextInfo):
    """手动测试初始化"""
    init(ContextInfo)

def 测试K线处理(ContextInfo):
    """手动测试K线处理"""
    handlebar(ContextInfo)

def 显示帮助(ContextInfo):
    """显示帮助信息"""
    print("🧪 QMT兼容性测试模块")
    print("=" * 50)
    print("📝 使用方法:")
    print("1. 在QMT中导入此测试模块")
    print("2. 运行策略查看兼容性测试结果")
    print("3. 根据测试结果调整策略代码")
    print()
    print("🔧 手动测试函数:")
    print("- 测试初始化(ContextInfo)")
    print("- 测试K线处理(ContextInfo)")
    print("- 显示帮助(ContextInfo)")
    print()
    print("📊 测试内容:")
    print("- ContextInfo对象属性检查")
    print("- 股票代码获取方法验证")
    print("- 账户ID获取测试")
    print("- 数据获取API验证")
    print("- 交易相关功能测试")
    print("=" * 50)

if __name__ == "__main__":
    print("🧪 QMT兼容性测试模块")
    print("用于验证QMT环境的基本兼容性")
    print("请在QMT中导入并运行此模块进行测试")
